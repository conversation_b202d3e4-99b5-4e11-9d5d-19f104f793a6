#include <windows.h>
#include "Protect.h"
#include "Interface.h"
#include "Tools.h"
#pragma comment(lib,"Detours/detours.lib")
#include "Detours/detours.h"
#pragma comment(lib,"Detours/enigma_ide.lib")
#include "Detours/enigma_ide.h"
#include "Engine.h"

CREATE_INTERFACE(IProtect)

static int (__cdecl *Connection)(int Type, const char *Text,...) = (int (__cdecl*)(int,const char*,...))0x0048B120;
static char (__thiscall *VectorSend)(void *thisPtr, const void *Buffer, int Length) = (char (__thiscall*)(void*, const void*, int))0x06708B0;

int __cdecl ConnectionFix(int Type, const char *Text, int Value, int Object, int Argument)
{
	return Connection(Type,"bd",Value,Object);
}

int __cdecl FLFix(int Type, const char *Text, int Value, int Time)
{
	return Connection(Type,"b",Value);
}

DWORD __stdcall FixGetFileAttributesA(LPCSTR lpFileName)
{
	return 0;
}

int __cdecl QuestCheck(int k, int j)
{
	return 1;
}

Engine::Packet Packet;

char __fastcall PacketHackFix(void *thisPtr, void* edx, const void *Buffer, int Length)
{
	unsigned char Type = *(unsigned char*)((char*)Buffer + 2);

	if (Type == 8)
	{
		Interface<ITools> Tools;
		int x = 0, b = 0, c = 0, d = 0, e = 0; unsigned char f = 0;
		Packet.Size = 24;
		Packet.Type = Type;

		Tools->ParseData((char*)Buffer+3, "dddddb", &x, &b, &c, &d, &e, &f);
		Tools->Compile((char*)&Packet.Data,21,"dddddb",GetTickCount(),65,47,394,465,f);
		Buffer = &Packet;
		Length = Packet.Size;
		return VectorSend(thisPtr, (const void*)&Packet, 21 + 3);
	}

	if (Type == 4)
	{
		Interface<ITools> Tools;
		int PID = 0, GID = 0;
		Packet.Size = 15;
		Packet.Type = Type;

		Tools->ParseData((char*)Buffer+3, "dd", &PID, &GID);
		Tools->Compile((char*)&Packet.Data,12,"ddd",PID,*(DWORD*)0x009EF780,GID);
		Buffer = &Packet;
		Length = Packet.Size;
		return VectorSend(thisPtr, (const void*)&Packet, 12 + 3);
	}

	if (Type == 253)
	{
		Interface<ITools> Tools;
		unsigned int number = 0, value = 0, Find = 0;
		Tools->ParseData((char*)Buffer+3, "dd", &number, &value);
		Find = (value - 253) & 0xFF;
		unsigned int newvalue = ((Find << 17) | Find) + 253;

		Packet.Size = 11;
		Packet.Type = Type;

		Tools->Compile((char*)&Packet.Data,8,"dd",newvalue,Find*123);
		Buffer = &Packet;
		Length = Packet.Size;
		return VectorSend(thisPtr, (const void*)&Packet, 8 + 3);
	}

	return VectorSend(thisPtr,Buffer,Length);
}

bool IProtect::Disable()
{
	Interface<ITools> Tools;
	//#include "vm_risc_begin.inc"
	//int DBLen = EP_ProtectedStringByID(5,NULL,0); char *DBName = new char[DBLen + 1];
	//memset(DBName,0,DBLen + 1); EP_ProtectedStringByID(5,DBName,DBLen);
	char DBName[255];
	strcpy(DBName, "KalOnline");
	Tools->SetMemoryEx(0x008A1F90,DBName,strlen(DBName)); //delete[] DBName;

	DetourTransactionBegin();
	DetourAttach(&(PVOID&)VectorSend, PacketHackFix);
	DetourTransactionCommit();

	//#include "vm_risc_end.inc"
	Tools->Intercept(ITools::_I_CALL, (void*)0x006D1E25, QuestCheck, 5);
	Tools->Intercept(ITools::_I_CALL, (void*)0x00665760, ConnectionFix, 5);
	Tools->Intercept(ITools::_I_CALL, (void*)0x0071339F, FLFix, 5);
	Tools->Intercept(ITools::_I_CALL, (void*)0x007E1F25, (void*)FixGetFileAttributesA, 6);
	Tools->FillMemoryEx(0x00652F7E, ITools::_I_NOP, 1);
	Tools->FillMemoryEx(0x00652F85, ITools::_I_NOP, 1);
	Tools->FillMemoryEx(0x00652F8D, ITools::_I_NOP, 6);
	Tools->FillMemoryEx(0x0048A611, ITools::_I_JMP_SHORT, 1);
	Tools->FillMemoryEx(0x0048A638, ITools::_I_JMP_SHORT, 1);
	Tools->SetMemoryEx(0x00877D50, "sbbbbbbbb", 10);
	Tools->FillMemoryEx(0x0066716A, ITools::_I_JMP_SHORT, 1);
	Tools->FillMemoryEx(0x006657C3, ITools::_I_NOP, 5);
	Tools->FillMemoryEx(0x006657C8, ITools::_I_NOP, 5);
	float ZoomLimit = 300.0; BYTE BlessRemove = 255;
	Tools->MemcpyExD((void*)0x00431763, &BlessRemove, 1);
	Tools->MemcpyExD((void*)0x008A18BC, &ZoomLimit, 4);
	return true;
}