static signed int (__cdecl *IsPlayerAttack)() = (signed int (__cdecl*)())0x006E0090;
static int (__thiscall *EndDashSkill)(int Player) = (int (__thiscall*)(int))0x004378A0;
static void *(__thiscall *SetTip)(int pTarget, LPCTSTR pString, COLORREF color, int dwLife, int bShowName) = (void *(__thiscall*)(int,LPCTSTR,COLORREF,int,int))0x00433DC0;	
static int (__thiscall *PlayerTick)(void *pTarget, unsigned int Value) = (int (__thiscall*)(void*,unsigned int))0x0041F120;
static signed int (__cdecl *SkillButton)(int Value) = (signed int (__cdecl*)(int))0x0042DE10;
extern std::map<int,int> ItemRestriction; extern std::multimap<std::string,int> ConfigItemRestriction;
DWORD ItemRestrictionDelay = 0;

signed int xRandom(signed int a1, signed int a2)
{
	std::srand(GetTickCount());

	if (a1 >= a2)
		return a1;
	else
		return a1 + std::rand() % (a2 - a1 + 1);
}

std::string xInt2String(int value)
{
	std::stringstream ss;
	ss << value;
	std::string str = ss.str();
	return str;
}

int xString2Int(std::string String)
{
	int Integer;
	std::istringstream iss(String);
	iss >> Integer;
	return Integer;
}

signed int __cdecl MySkillButton(int Value)
{
	if (Value == 93 && *(DWORD*)(*Engine::CGame_Character::m_Master + 20768) == 4)
	{
		Interface<IPackets> Packets;
		Packets->Send(120,"b",Value);
		return 0;
	}

	if (Value == 95)
	{
		Interface<IPackets> Packets;
		Packets->Send(120,"b",Value);
		return 0;
	}

	return SkillButton(Value);
}

int __fastcall MyPlayerTick(void *pTarget, void *edx, unsigned int Value)
{
	int Check = PlayerTick(pTarget,Value);

	if (*Engine::CGame_Character::m_Master)
	{
		int x = *(DWORD*)(*Engine::CGame_Character::m_Master + 18236);
		int y = *(DWORD*)(*Engine::CGame_Character::m_Master + 18244);
		int a = x >> 13, b = y >> 13;
		std::string z = xInt2String(a) + "_" + xInt2String(b);

		if (*Engine::CGame_Character::m_Master && strlen(z.c_str()) && GetTickCount() >= ItemRestrictionDelay && ConfigItemRestriction.count(z))
		{
			auto range = ConfigItemRestriction.equal_range(z);

			for (auto i = range.first; i != range.second; i++)
			{
				if (ItemRestriction.count(i->second) && ItemRestriction.find(i->second)->second)
				{
					int Item = Engine::KGameSys::FindItemFromIID(ItemRestriction.find(i->second)->second);

					if (Item && *(DWORD*)(Item + 48) & 1)
					{
						Interface<IPackets> Packets;
						unsigned int number = xRandom(1,10000);
						unsigned int value = ((number << 18) | 57) + 253;
						std::string a = xInt2String(value);
						std::string b = picosha2::hash256_hex_string(a);
						std::string c = base64_encode(reinterpret_cast<const unsigned char*>(b.c_str()), b.length());
						c = c.substr(0,c.length()-2);
						Packets->Send(253,"dds",number,value,c.c_str());
						Packets->Send(57,"d",ItemRestriction.find(i->second)->second);
						ItemRestrictionDelay = GetTickCount() + 3000;
					} else {
						ItemRestriction.erase(i->second);
					}
				}
			}
		}
	}

	if (*(int*)((int)pTarget + 19676) == 1 && *(int*)((int)pTarget + 20768) == 0 && *(DWORD*)((int)pTarget + 19684))
	{
		if (GetTickCount() > (*(DWORD*)((int)pTarget + 19684) + 1500)) EndDashSkill((int)pTarget);
	}

	if (*(DWORD*)((int)pTarget + 18364) & 262144)
	{
		if (*(DWORD *)((int)pTarget + 19944) == 0) SetTip((int)pTarget,*(const char**)((int)pTarget + 17700),RGB(0,128,255),10000,false);
	}

	if (*(DWORD*)((int)pTarget + 18364) & 524288)
	{
		if (*(DWORD *)((int)pTarget + 19944) == 0) SetTip((int)pTarget,*(const char**)((int)pTarget + 17700),RGB(255,0,0),10000,false);
	}

	return Check;
}

signed int __cdecl MyIsPlayerAttack()
{
	int Check = IsPlayerAttack();
	if (*(DWORD*)(*Engine::CGame_Character::m_Master + 18364) & 262144 || *(DWORD*)(*Engine::CGame_Character::m_Master + 18364) & 524288) Check = 1;
	return Check;
}

void *__fastcall MySetTip(int pTarget, void *edx, LPCTSTR pString, COLORREF color, int dwLife, int bShowName)
{
	if (*(DWORD*)(pTarget + 18364) & 262144) return SetTip(pTarget,pString,RGB(0,128,255),dwLife,bShowName);
	if (*(DWORD*)(pTarget + 18364) & 524288) return SetTip(pTarget,pString,RGB(255,0,0),dwLife,bShowName);
	return SetTip(pTarget,pString,color,dwLife,bShowName);
}