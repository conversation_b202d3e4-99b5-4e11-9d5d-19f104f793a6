//{{NO_DEPENDENCIES}}
// Microsoft Visual C++ generated include file.
// Used by keygen.rc
//
#define IDM_ABOUTBOX                    0x0010
#define IDD_ABOUTBOX                    100
#define IDS_ABOUTBOX                    101
#define IDD_KEYGEN_DIALOG               102
#define IDR_MAINFRAME                   128
#define IDC_STATIC_USERINFO             1000
#define IDC_EDIT_USERINFO               1001
#define IDC_CHECK_HARDWARE              1002
#define IDC_EDIT_HARDWARE               1003
#define IDC_CHECK_EXPIRATION            1004
#define IDC_DATETIMEPICKER_EXPIRATION   1005
#define IDC_STATIC_SECTIONS             1006
#define IDC_CHECK_SECTION1              1007
#define IDC_CHECK_SECTION2              1008
#define IDC_CHECK_SECTION3              1009
#define IDC_CHECK_SECTION4              1010
#define IDC_CHECK_SECTION5              1011
#define IDC_CHECK_SECTION6              1012
#define IDC_CHECK_SECTION7              1013
#define IDC_CHECK_SECTION8              1014
#define IDC_CHECK_SECTION9              1015
#define IDC_CHECK_SECTION10             1016
#define IDC_CHECK_SECTION11             1017
#define IDC_CHECK_SECTION12             1018
#define IDC_CHECK_SECTION13             1019
#define IDC_CHECK_SECTION14             1020
#define IDC_CHECK_SECTION15             1021
#define IDC_CHECK_SECTION16             1022
#define IDC_BUTTON_GEN                  1024
#define IDC_BUTTON_GENFP                1025
#define IDC_BUTTON_CLOSE                1026
#define IDC_EDIT_KEY                    1027
#define IDC_CHECKHYPHENS                1028
#define IDC_EDITNAME                    1029
#define IDC_COMBOKMODE                  1030
#define IDC_COMBOKBASE                  1031
#define IDC_CHECKEXPIRATION             1032
#define IDC_DTPEXPIRATION               1034
#define IDC_CHECKREGAFTER               1035
#define IDC_DTPREGAFTER                 1036
#define IDC_DTPREGBEFORE                1037
#define IDC_CHECKREGBEFORE              1038
#define IDC_CHECKCOUNTRY                1039
#define IDC_COMBOCOUNTRIES              1040
#define IDC_CHECKEXECS                  1041
#define IDC_CHECKDAYS                   1042
#define IDC_CHECKRTIME                  1043
#define IDC_CHECKGTIME                  1044
#define IDC_EDITEXECS                   1045
#define IDC_EDITRTIME                   1046
#define IDC_EDITDAYS                    1047
#define IDC_EDITGTIME                   1048
#define IDC_CHECKS1                     1049
#define IDC_CHECKS3                     1050
#define IDC_CHECKS2                     1051
#define IDC_CHECKS4                     1052
#define IDC_CHECKS5                     1053
#define IDC_CHECKS6                     1054
#define IDC_CHECKS7                     1055
#define IDC_CHECKS8                     1056
#define IDC_CHECKS9                     1057
#define IDC_CHECKS11                    1058
#define IDC_CHECKS10                    1059
#define IDC_CHECKS12                    1060
#define IDC_CHECKS13                    1061
#define IDC_CHECKS14                    1062
#define IDC_CHECKS15                    1063
#define IDC_CHECK25                     1064
#define IDC_CHECKS16                    1064
#define IDC_BUTTONGEN                   1065
#define IDC_BUTTONGENFP                 1066
#define IDC_BUTTONVER                   1067
#define IDC_BUTTONVERFP                 1068
#define IDC_EDITKEY                     1069
#define IDC_BUTTONCLOSE                 1070
#define IDC_CHECKHD                     1071
#define IDC_EDITHD                      1072

// Next default values for new objects
// 
#ifdef APSTUDIO_INVOKED
#ifndef APSTUDIO_READONLY_SYMBOLS
#define _APS_NEXT_RESOURCE_VALUE        130
#define _APS_NEXT_COMMAND_VALUE         32771
#define _APS_NEXT_CONTROL_VALUE         1073
#define _APS_NEXT_SYMED_VALUE           101
#endif
#endif
