#include <Windows.h>
#include <iterator>
#include <iomanip>
#include <sstream>
#include <wininet.h>
#pragma pack(1)
HINSTANCE hL = 0;
FARPROC p[806] = {0};
#pragma comment(lib,"Detours/detours.lib")
#include "Detours/detours.h"
#pragma comment(lib,"Detours/enigma_ide.lib")
#include "Detours/enigma_ide.h"
#include "Engine.h"
#include "Exports.h"
#include "MD5.h"
#include "base64.h"
#include "Sha256.h"
#include "Protect.h"
#include "Interface.h"
#include "Chatbox.h"
#include "Packets.h"
#include "VButtonFix.h"
#include "Buff.h"
#include "Tools.h"
#include "DSSWindow.h"
#include "MakeTip.h"
#include "RedBlue.h"
#include "ExpTable.h"
#pragma comment(lib,"Wininet.lib")
#pragma comment(lib,"Ws2_32.lib")
#pragma warning (disable : 4996)
#define CRC32(c, b) ((*(pcrc_32_tab+(((int)(c) ^ (b)) & 0xff))) ^ ((c) >> 8))
int StoreValue = 0;

static int update_keys(unsigned long *pkeys, const unsigned long *pcrc_32_tab, int c)
{
    (*(pkeys+0)) = CRC32((*(pkeys+0)), c);
    (*(pkeys+1)) += (*(pkeys+0)) & 0xff;
    (*(pkeys+1)) = (*(pkeys+1)) * 134775813L + 1;
    {
      register int keyshift = (int)((*(pkeys+1)) >> 24);
      (*(pkeys+2)) = CRC32((*(pkeys+2)), keyshift);
    }
    return c;
}

static void init_keys(unsigned long *pkeys, const char *passwd, const unsigned long *pcrc_32_tab)
{
    *(pkeys+0) = 305419896L;
    *(pkeys+1) = 591751049L;
    *(pkeys+2) = 878082192L;
    while (*passwd != '\0') {
        update_keys(pkeys,pcrc_32_tab,(int)*passwd);
        passwd++;
    }
}

int __cdecl PKHide(int a1, signed int a2, signed int a3, signed int a4, const char *a5)
{
	StoreValue = a1;
	return Engine::CEditor::PWDPK(a1,a2,a3,a4,a5);
}

/*
void HideMyPKPWD()
{
	#include "vm_risc_begin.inc"
	int DBLen = EP_ProtectedStringByID(4,NULL,0); char *DBName = new char[DBLen + 1];
	memset(DBName,0,DBLen + 1); EP_ProtectedStringByID(4,DBName,DBLen);
	init_keys((unsigned long*)(StoreValue + 164),DBName,(unsigned long*)Engine::CEditor::AddPKPWD());
	delete[] DBName;
	#include "vm_risc_end.inc"
}*/

void HideMyPKPWD()
{
	init_keys((unsigned long*)(StoreValue + 164),"EV)O8@BL$3O2E",(unsigned long*)Engine::CEditor::AddPKPWD());
}

void DisableProtection()
{
	Interface<IProtect> Protect;
	Protect->Disable();
	Interface<IChatbox> Chatbox;
	Chatbox->HookCommand();
	Interface<IPackets> Packets;
	Packets->Hook();
	Interface<IBuff> Buff;
	Buff->HookBuff();
	Interface<ITools> Tools;
	Tools->Intercept(ITools::_I_CALL, (void*)0x00802E54, HideMyPKPWD, 5);
}

int __stdcall MyxDecode(int chCodeNum, int ptr, int Len)
{
	//if (chCodeNum == 58) return Engine::KGameSys::xDecode(57,ptr,Len);
	return Engine::KGameSys::xDecode(chCodeNum,ptr,Len);
}

int __stdcall MyDecode(int chCodeNum, int ptr, int Len, int Arg)
{
	//if (chCodeNum == 58) chCodeNum = 57;
	return Engine::KGameSys::Decode(chCodeNum,ptr,Len,Arg);
}

/////////////////////////////////
// x-trap disable
static int (__cdecl *XTrap)(int a1, int a2, int a3) = (int (__cdecl*)(int,int,int))0x0083FEE0;
static int (__cdecl *XTrapDisable)(int a1) = (int (__cdecl*)(int))0x00840750;

int __cdecl MyXTrap(int a1)
{
	return 0;
}

int __cdecl MyXTrapDisable(int a1, int a2, int a3)
{
	return 0;
}
///////////////////////////////////


BOOL APIENTRY DllMain(HMODULE hModule, DWORD ul_reason_for_call, LPVOID lpReserved)
{
	switch (ul_reason_for_call)
	{
	case DLL_PROCESS_ATTACH:
		{
			InitializeADVAPI32();
			Interface<ITools> Tools;
			BofFix();
			SetExpTable();
			DisableProtection();
			DetourTransactionBegin();
			/////////////////////////////////////////////////////////
			// x-trap disable
			DetourAttach(&(PVOID&)XTrap, MyXTrapDisable);
			DetourAttach(&(PVOID&)XTrapDisable, MyXTrap);
			/////////////////////////////////////////////////////////
			DetourAttach(&(PVOID&)KalItemShop1, MyKalItemShop1);
			DetourAttach(&(PVOID&)KalItemShop2, MyKalItemShop2);
			DetourAttach(&(PVOID&)KalItemShop3, MyKalItemShop3);
			DetourAttach(&(PVOID&)Engine::KGameSys::OnOk, OnOk);
			DetourAttach(&(PVOID&)Engine::KGameSys::OnCancel, OnCancel);
			DetourAttach(&(PVOID&)PlayerTick, MyPlayerTick);
			DetourAttach(&(PVOID&)IsPlayerAttack, MyIsPlayerAttack);
			DetourAttach(&(PVOID&)SetTip, MySetTip);
			DetourAttach(&(PVOID&)Engine::KGameSys::PressKey, PressKey);
			DetourAttach(&(PVOID&)Engine::KGameSys::MakeTip, MakeTip);
			DetourAttach(&(PVOID&)SkillButton, MySkillButton);
			DetourAttach(&(PVOID&)Engine::CEditor::PWDPK, PKHide);
			//DetourAttach(&(PVOID&)Engine::KGameSys::Decode, MyDecode);
			DetourAttach(&(PVOID&)Engine::KGameSys::xDecode, MyxDecode);
			DetourAttach(&(PVOID&)Engine::Move::SetMovement, MySetMovement);
			DetourAttach(&(PVOID&)Engine::KGameSys::OpenWindow, AuctionOnOpen);
			DetourTransactionCommit();
			break;
		}
	case DLL_PROCESS_DETACH:
		{
			FinalizeADVAPI32();
			BofFix();
			SetExpTable();
			DisableProtection();
			DetourTransactionBegin();
			/////////////////////////////////////////////////////////
			// x-trap disable
			DetourAttach(&(PVOID&)XTrap, MyXTrapDisable);
			DetourAttach(&(PVOID&)XTrapDisable, MyXTrap);
			/////////////////////////////////////////////////////////
			DetourDetach(&(PVOID&)KalItemShop1, MyKalItemShop1);
			DetourDetach(&(PVOID&)KalItemShop2, MyKalItemShop2);
			DetourDetach(&(PVOID&)KalItemShop3, MyKalItemShop3);
			DetourDetach(&(PVOID&)Engine::KGameSys::OnOk, OnOk);
			DetourDetach(&(PVOID&)Engine::KGameSys::OnCancel, OnCancel);
			DetourDetach(&(PVOID&)PlayerTick, MyPlayerTick);
			DetourDetach(&(PVOID&)IsPlayerAttack, MyIsPlayerAttack);
			DetourDetach(&(PVOID&)SetTip, MySetTip);
			DetourDetach(&(PVOID&)Engine::KGameSys::PressKey, PressKey);
			DetourDetach(&(PVOID&)Engine::KGameSys::MakeTip, MakeTip);
			DetourDetach(&(PVOID&)SkillButton, MySkillButton);
			DetourDetach(&(PVOID&)Engine::CEditor::PWDPK, PKHide);
			//DetourDetach(&(PVOID&)Engine::KGameSys::Decode, MyDecode);
			DetourDetach(&(PVOID&)Engine::KGameSys::xDecode, MyxDecode);
			DetourDetach(&(PVOID&)Engine::Move::SetMovement, MySetMovement);
			DetourDetach(&(PVOID&)Engine::KGameSys::OpenWindow, AuctionOnOpen);
			DetourTransactionCommit();
			break;
		}
	}
	return TRUE;
}