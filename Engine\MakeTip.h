struct ConfigBattleHorseShowing
{
	int Grade;
	int Type;
	int Stat;
};

int DemonGongItem = 0, DGCheck = 0; extern std::map<int,ConfigBattleHorseShowing> BattleHorseShowing;

void __fastcall DemonGongRead(int Window, void *edx, int Item)
{
	DemonGongItem = Item; int DGType = 0, DGShape = 0;
	int check = Engine::KGameSys::ArmorGrade(*(WORD*)(DemonGongItem + 32));
	DGType = *(DWORD*)(check + 392); DGShape = *(DWORD*)(check + 396);
	Engine::KGameSys::DemonGongErase(Window,Item);
	*(DWORD*)(check + 392) = DGType;
	*(DWORD*)(check + 396) = DGShape;
}

int __fastcall DemonGongShow(int Grade, void *edx)
{
	if (Grade)
	{
		int check = Engine::KGameSys::ArmorGrade(*(WORD*)(DemonGongItem + 32));
		*(DWORD*)(check + 392) = 13;
		if (*(WORD*)(DemonGongItem + 104) >= 2 && *(DWORD*)(DemonGongItem + 108) && !*(DWORD*)(DemonGongItem + 112))
		{
			if (*(DWORD*)(DemonGongItem + 108) >> 24 == 0) *(DWORD*)(check + 396) = 54;
			if (*(DWORD*)(DemonGongItem + 108) >> 24 == 1) *(DWORD*)(check + 396) = 55;
			if (*(DWORD*)(DemonGongItem + 108) >> 24 == 2) *(DWORD*)(check + 396) = 56;
		}
		if (*(WORD*)(DemonGongItem + 104) >= 2 && *(DWORD*)(DemonGongItem + 108) && *(DWORD*)(DemonGongItem + 112))
		{
			if (DGCheck == 0)
			{
				if (*(DWORD*)(DemonGongItem + 112) >> 24 == 0) *(DWORD*)(check + 396) = 54;
				if (*(DWORD*)(DemonGongItem + 112) >> 24 == 1) *(DWORD*)(check + 396) = 55;
				if (*(DWORD*)(DemonGongItem + 112) >> 24 == 2) *(DWORD*)(check + 396) = 56;
			}
			if (DGCheck == 1)
			{
				if (*(DWORD*)(DemonGongItem + 108) >> 24 == 0) *(DWORD*)(check + 396) = 54;
				if (*(DWORD*)(DemonGongItem + 108) >> 24 == 1) *(DWORD*)(check + 396) = 55;
				if (*(DWORD*)(DemonGongItem + 108) >> 24 == 2) *(DWORD*)(check + 396) = 56;
			}
			DGCheck += 1; if (DGCheck >= 2) DGCheck = 0;
		}
		return check;
	}

	return Engine::KGameSys::ArmorGrade(Grade);
}

void BofFix()
{
	Interface<ITools> Tools;
	Tools->Intercept(ITools::_I_CALL, (void*)0x00759ABA, DemonGongRead, 5);
	Tools->Intercept(ITools::_I_CALL, (void*)0x005CDE0A, DemonGongShow, 5);
}

void __fastcall MakeTip(void *a, void *edx, int a2, int a3, signed int a4, char a5, int a6, int a7, int a8)
{
	int Item = 0;

	if (a5 & 4)
	{
		Item = Engine::KGameSys::FindItemFromIIDEx2(a3);
	} else {
		Item = Engine::KGameSys::FindItemFromIID(a3);
		if (!Item) Item = Engine::KGameSys::FindItemFromIIDEx(a3);
	}

	if (Item && BattleHorseShowing.count(a3)) *(BYTE*)(Item + 54) = BattleHorseShowing.find(a3)->second.Grade;
	if (BattleHorseShowing.count(a3)) *(BYTE*)((int)a + BattleHorseShowing.find(a3)->second.Type) = BattleHorseShowing.find(a3)->second.Stat;
	Engine::KGameSys::MakeTip(a, a2, a3, a4, a5, a6, a7, a8);
	if (BattleHorseShowing.count(a3)) *(BYTE*)((int)a + BattleHorseShowing.find(a3)->second.Type) = 0;
}