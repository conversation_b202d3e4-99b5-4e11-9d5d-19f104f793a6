﻿  base64.cpp
G:\Kal\2021 Sources\Source Code\Engine.dll\Engine\base64.cpp(60,65): warning C4267: '=': conversion from 'size_t' to 'unsigned char', possible loss of data
G:\Kal\2021 Sources\Source Code\Engine.dll\Engine\base64.cpp(71,64): warning C4267: '=': conversion from 'size_t' to 'unsigned char', possible loss of data
  Buff.cpp
  Chatbox.cpp
  Engine.cpp
  Graphics.cpp
  Hooks.cpp
  Interface.cpp
  OnSend.cpp
  Packets.cpp
G:\Kal\2021 Sources\Source Code\Engine.dll\Engine\Packets.cpp(99,2): error C3861: '__cpuid': identifier not found
G:\Kal\2021 Sources\Source Code\Engine.dll\Engine\Packets.cpp(102,2): error C3861: '__cpuid': identifier not found
G:\Kal\2021 Sources\Source Code\Engine.dll\Engine\Packets.cpp(105,2): error C3861: '__cpuid': identifier not found
G:\Kal\2021 Sources\Source Code\Engine.dll\Engine\Packets.cpp(431,31): warning C4267: '=': conversion from 'size_t' to 'unsigned short', possible loss of data
G:\Kal\2021 Sources\Source Code\Engine.dll\Engine\Packets.cpp(1010,42): warning C4302: 'type cast': truncation from 'char *' to 'char'
  Protect.cpp
G:\Kal\2021 Sources\Source Code\Engine.dll\Engine\Protect.cpp(97,2): warning C4996: 'strcpy': This function or variable may be unsafe. Consider using strcpy_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  Tools.cpp
G:\Kal\2021 Sources\Source Code\Engine.dll\Engine\Tools.cpp(328,15): warning C4302: 'type cast': truncation from 'void *' to 'BYTE'
G:\Kal\2021 Sources\Source Code\Engine.dll\Engine\Tools.cpp(332,15): warning C4302: 'type cast': truncation from 'void *' to 'BYTE'
