﻿<?xml version="1.0" encoding="UTF-8"?>
<EnigmaProject Version="1.60">
  <EnigmaProjectDetails>
    <FullNameOfProject>Enigma test project of Crypted Sections</FullNameOfProject>
    <ProjectDescription>This example drives you to understand how Enigma Crypted Section feature works. Try to compile executable firstly and then protect it! For registration features and for unlocking of crypted sections you must play with Enigma Keys Generator (See MainMenu-&gt;KeyGenerator). Note: do not forget to unlock necessary crypted section(s) while key generation!!!</ProjectDescription>
  </EnigmaProjectDetails>
  <Input>
    <FileName>test.exe</FileName>
    <ProductName>Test Application</ProductName>
    <VersionInfo>1.0</VersionInfo>
  </Input>
  <Output>
    <SameAsInputFile>False</SameAsInputFile>
    <FileName>test_protected.exe</FileName>
  </Output>
  <Compression>
    <Type>Best</Type>
    <Mode>AllResourcesExceptIconAndVersion</Mode>
  </Compression>
  <RegistrationFeatures>
    <RegistrationDataStoring>
      <Storage>SystemRegistry</Storage>
      <Registry>
        <Base>CurrentUser</Base>
        <Path>\SOFTWARE\EnigmaDevelopers\</Path>
      </Registry>
      <FileSystem>
        <Base>MyDocumentsFolder</Base>
        <Path>\notepad\secret.dat</Path>
      </FileSystem>
    </RegistrationDataStoring>
    <Common>
      <AllowHardwareLockedKeys>False</AllowHardwareLockedKeys>
      <AllowTimeLimitedKeys>False</AllowTimeLimitedKeys>
      <AllowExecOnlyIfRegistered>False</AllowExecOnlyIfRegistered>
      <KeyMode>0</KeyMode>
      <KeyBase>3</KeyBase>
    </Common>
    <HardwareAssociation>
      <UseVolumeSerialDrive>True</UseVolumeSerialDrive>
      <UseSystemVolumeName>False</UseSystemVolumeName>
      <UseComputerName>False</UseComputerName>
      <UseCPUType>False</UseCPUType>
      <UseMAC>False</UseMAC>
      <UseWindowsKey>False</UseWindowsKey>
      <UseMotherboard>False</UseMotherboard>
      <UseHDDSerial>False</UseHDDSerial>
    </HardwareAssociation>
    <Constants>
      <EncryptedConstant>2113444489</EncryptedConstant>
      <Secure1>3A44D5C34F6A54B65C7657D6587A7D35</Secure1>
      <WatermarkConstant>786A6B5894D8BC90AD25443E7271EE55</WatermarkConstant>
      <Mode512>
        <PublicKey>0201B810DA4A1ADD4351378790A98138533067CP4S86R7D8THS45GBCVUM635EPRQRMYRP3DAA5DUPZ6ABDSFP7F5ACP7ERGH4A7Y6B6NW6NMMBZF83WVER9Y4MMBNLBQDKR7KFVLGLV067CFDQCWCHGQVVRN24DECEPBL96YJQJTVDCRTNQG3E4WW4GK4GQ5X5L5H88D3XYHCBRBNASPD3P5CNYFKFHBCSDHHD6WPTCC4XVSM5S88067C2JSTCMVT48C8HC7SHKGTFJBM28P6XTBCNWHMV6J6KN6W5Q9TQLVR285U6GVCAAUTZLRTPSRGDQ742B4742XF4MACRR747YDP5FZZ9D</PublicKey>
        <PrivateKey>00C98B2SF9UBJA605AJX53GJFXJV8UH4A6PY2L6CV4MAMV7V3ERRVY99Y72V2P77Z2J3KBPGWR3WXKG5GF9Z6CKXJHY5VUMBTQ66H2MRZPCU00DLFJ675JTTTNEK00DLFJ675JTTTNEK</PrivateKey>
      </Mode512>
      <Mode768>
        <PublicKey>0201B810DA4A1ADD4351378790A9813853309AEHRR66KMK99UEPX8C54WQFDZQ5Y96WUCVZYMXBC32SJV7T5SBPR54E2DBH8UD9RNHTJ9BLJV6NHX52W7PZDDTTJDDGF3YFVL7PJFL8RS8THTFDF4RY7K3QXLTMXMC8DDSRVRWBVMMP3UYKCDDC7KGL9PG509ACPTRQ4MMUKGDAUJFFFGSCBFLYK9X9E74M58PW5FG9B9RTLXJQ4STPVCHJTEH8BP557VZPQYDBUZJXXJNGVZELFL4UL3LHHE8A645CK593DVWCQBSX4ZCCQ3ST8GCZLW2729XK3NT7GST3LMQGXSHLDWCXE09AENVX2QTX3VLT6FWB4JND875XZWQ26GW76TV3KVVHK7U489LLPBQ39HUWKMA65U3HKWBDHKBZEB525PHEH2YQHRZGNJAAYZXMWN8RNZ9T7VMZAJMP2V3S9NXZ9RHL96F7AT3NTMN36V4ACSFEN4DR6TJR95</PublicKey>
        <PrivateKey>019VRB95TKY456YC48LBS9FK34YN080VVPDJPPKU6XA9EPLX7HTVN3VSTTNJZFPH4HQAFY58R593KFKSNZ7WEZBA9AZ6PGN735EWEFUAEM3G8XVUFNL2ZFRPXA4V8KVXE4M8GACUJAT7H3TTWZT9ZDE43ZF46HC01AHFR85MVR2UP7K3N53M8MTNWBUP01AHFR85MVR2UP7K3N53M8MTNWBUP</PrivateKey>
      </Mode768>
      <Mode1024>
        <PublicKey>0201B810DA4A1ADD4351378790A981385330CDG9YQSEUK2HT546KWW46NHSCHMD2TNCSPVNSKGQC6CHDUSU9CC4LDPLTD3NLRVD3TFV9BU2SYBUBU9TXKLQ3CTKZE5GN9VFG37R9YNK2FE58K7ZYWTUJYFSJTBMDLLGA9GJ2LJXGHSS9CESGC2K69H65YQGAC7CRGF5T3CPLA5BDJZKVCUWPZRNA2DN7L956LZVZBQUDPX6PF30CDBH26Z82VE4QQ8N85LQHYJYYCJ6WQ89H9K9VVQX925SG5FJ79AGVZB6M8TTFKCJ56FAREP8GSTCNKWCXEALDP9Y2ZLS9QKZV8JRNDHG9D8BRDSCTMDQK9NLAPZ9TGYFS9G7ULXHYJF7F9A89HPXGTU2N4FQQJL98WZ5QND2KTEMMUUZFAX953HCGNSWUWU2M7LBEWZSH9GW5SC0CDJDY2HC4HJYUSVTAVXZ7MYVJWF3CU9WC3SVPSL29WF46DVS2SYKSSAMATPPYGJDE3PBFMGS34CXWQSXKCEAE63L2EXTS9NE2G597XASY3S34XMYST5YZF4THUTYT5DY9UKKXU6PH8MLXRPZMPMMR83JUX2WGNEW8ZGL4SKUCUB8CEAB6UZ4FB66WTP3B64VMX7XG75T4Y35479</PublicKey>
        <PrivateKey>020MLJ7XTRVLBDNUVMKGUFHCU8XBGMHHVRA0ADSHPBC7NVSZ82AM867XCBELNKMR7CZ56C4SW8KKRD9WZLE5X992GZVSZXS5J6JG9SLDW3Y696TU4DQZP778LVFKVRKANQD6FUGN2ACWUJM9ZK6ME7WXWDZ88LTWHME52RKUDKXSTT7RGWP7HFUJDGWEUG4ZHQ25DCQJ644CLEXRR3G020SAAREJB4WXZ4H2JEH4AB4U8FGFTA4CAF020SAAREJB4WXZ4H2JEH4AB4U8FGFTA4CAF</PrivateKey>
      </Mode1024>
      <Mode2048>
        <PublicKey>0201B810DA4A1ADD4351378790A98138533199H9KZC4CQ5ZKPEGYDW2EHN5AKZA5G5KP4HU8USWJR8XWW7MCH7244RVPSXFHVU6YQRK4LPUHM497XJYPLLA3DHUZWXK3CB5SUYF75NPYLTNPRUAPCTVDHAKYWTRQ97HES2E59WDN2LJKFYA2K8BGFSBHP4A6BF5FSDTS8NWLLC5NEQ76NJ9BAMWXG55UPFFMRQZTU5KV2G6LSPQUJJWA9U4LJ5SCJ9M9ZQZ6K7LZA9UAMJ533UQ5J3JPNQAUWJGZFRFCTGAGSBWPWRRF9HRGF9MCX55QRYGPYMRE6DBJ65SJDX34FVPYWWKWQ48MCKXYXPHQ23BJ42U8ZMDD8CG5DPHZQ5XTXUKA277XK6PKSTLJ88ZZ9EBXH62REMRZZRGNPKHYM34RW722SSZHMPFKY7X8UZ19ADGX3UQ7X444XH7MZXEZYW2YLFHR8YL3PTCXVSYX89ZXN7KW2XNBE8XS3TQRGZVF6KA5USH56NEZYH5RNWBBPVE9LD6H42RQWR5YVQMT5EQYQTK6YM9V8DA5BJCLGGZ2UGD6NPA2X3SNUS47F7HJBNBCWHRAYPPCQ5HWCJNPDHUDQMRN2P6PWJFWJ6CLVNK8HXEFLE4ENPXAFN89CRS43RUMVGDJQCSRB535XYCS6BZYLA9Q6HJV425F26XXHRGA7C8R8BVMELAPEH5DSA9KXTCV4H2RNVDFHG3YJSERZVFPWHYA823LUBENBL5U3J4LJX2KQLWL5XGGESPSE2FQWKSQ4NUCDLBEJPQAT2A2477DYS2XEXKLYEAKUN22622CRLBCAHPFNEV2FJ2H8N3JQGXJRGP19AFHGRW6SS2ZSFYK8EZAPVYL4MCP46UMR64Y9T89FHWEPXBRHKWJCSNNNGPU984J5XW4BWTU6PDWPN24W8RU55A9C3U9H7VHS5PFURASHBT4JAEE5ZKJBSYMCMDVBLU973R72RVPA6U96A2LX8HMASVST9RVJ7W66V4DYWRDZZQ3673W7C7LVF68TWPLNMYEUQ8FDZRNP38D9BX5YA3VENETDUUUMR9U7FFP4NZM7G4Y6J6EASK5SAXXTRMZ8F3AH5FC54E543N739BY5KVZQM7JZWD2P2HPE7BDRMM9JFCGPBWWASTZ7UDNRV6AFHVMBR9AGAERE9M6W6S2ZT8PBTABPDCKBPPPV6QW9DRXZUGAHDDRRSYXM4YXQ2QJX6CL33N7RV8846LK8A3DFLDXWZ2NKF8V</PublicKey>
        <PrivateKey>02DB825DUDFYHA88AG49NMM742HCQEJDKQQGDR6G3GL7MCED16DU9KTVE47XQ3M92FFKX8FU9ZXT7AK7VTGGJSYQERCZBR8J2DE2CFE3VUU4W7JEQJL49HDUXBW7SNPCV8MHXM7EQHYPQK3GSPQYV5VWESQE4AYWFZHXN2UX7Z3T3CV52XURB3YFKLUCY6CJTRACRVVDD568F6F28U74ABWCZ2ZMC32E83V34JZZPDBF3KEYQWREETGKQT6NANCMAD4XDEXW5LCYBJ3GMDJBM3E7FNK987JEUYEV7QNKGKNTDVMRG3G5L8NC9CNHQS8AVDMDBEGCG3DHGJUGPG22442C9LCD8FJ2LSDMQXLHCGCLP7ZSYQT2UJ4QFGU2CH3BTT3UADZMQSBAV33B3Z2Y8Y8RRD9J754G02DJ372LR8APP7HXKB2GNPHP6T65C3RR48Q8BHGPKEZ6PX2D02DJ372LR8APP7HXKB2GNPHP6T65C3RR48Q8BHGPKEZ6PX2D</PrivateKey>
      </Mode2048>
      <Mode3072>
        <PublicKey>0201B810DA4A1ADD4351378790A98138533267BJBN6W8SY33Y47RWJHWS385XWRV9TSVSB5EM7ECZYNSJ44ZKSD8KV46Y9T6TNC6QMDW4V6DBMADU3YUKLP88TCLEUD9FTEUHGXM6T57AQ2EMB59UPQVNSUT7Z4EHAMDQ6N77VK2A6P847MMWJH9E7C5XX6FNF6MHGWERPWQV9ZLVR9Z7X5D5Z2XQL6MBM7BDCT2DEDVRBFNDDBLG6LHZ9APM48LS5TKTJAGDYFDRVHEGTDH47PCLL8YJ4ZAWCB59F8BN5PZL3NQACP32G3YNBX5E3J7S28XLRGAX55ZML5XE6Q68EUAL38VUBJ2MZ2UGCCHM7CE6S99N44X9RMV7B55HULJ4KHPFYSBDUSPQTCWGS76BEQST2QCZDZ2T4FBA4YUJFF47AACEA4EEFESEYWN48UZCSDZAMGSU5VB9UJKR2XQUSDFS3TNG9QKURQWS7N5Q3VPYT4QAGGQ5GW2MMXHDJYW55FQPHSS255GVJ7WLLP85JHS8K6DVTJWB4W4Y8ZEFF7KP6W74PAA8FVPZVDW4RKGMQ8V56Q6R4V34XFE5HR4FZS8GZ6R36RKW6WN34S2AHR9LCGF8LULB7ZN6NC2AA5RPDZYGWSPPME6267CGLZCPGG98W557RXCDWAETJHDCRPUXX7AD3E8SL9WMGBV9QFHMSV9UFV7H9NTX3QYV95CN9P9DSYQWL35R2BWB4GKW6CTWVB2X6WH3P2WFBE6X55KBLAMDB3SRDSDVY37GKH78MUHY7KDJDC8SJU5FUQRYZ49ULX3YSZL7E284GFVZSGDFTTQNR6VMBM3LLR78SUSVMJW88XT2K4UHEATRU3V9MMR2VGNGCY9S6R7M4ELNPEBBMQP2HVPLY5M5VPA92V7NU8ZZ58YYTW5QK9USZ5LV59UZGB2CFDPZ6HFYU5WB7UU3UYWVNSNKM3W7GBYKJP8TMFW2YN6GNSSE4ZKFVZRLUT8QPZBBEQT5H9H5Z53D7QV4AJTKTX4EMW62TDWXQVK24JZPSG2VXKCMYZLBUASE25R3EEK9KBLJ8VX83ZXGTBL5CCDBY33HJ6FKX8AW3WPRX3XTCS9G2B9SVK2XFFKNSPGFG6FZNS8P2HERFGFL79DKUZZFWHFH4H9UZ7F7F6W3ZWN84JRRA7SU8T89EQMRBEPVYMMGLGSUMFWWMEDLJM5EGP5WEX9LDUAQXS55LKDQVRKX4R8NNBHSYBHSNMFYA2C6KGQQFK3TN267DPSZT3PTKHTLNMUV2YYZZRX3953LPWAKYF8LJUVMEQT3ZC4F9SWMYGUHG6XX66PJFQWSRUW2C79YLGZ6ZAFS7HLXBTH55UD3EGFBZQ5CLNJDZTEJACDVBCM5FQBTX4DKKUDGNSMWWLC5UMQRHW89CTU5636WYDXVZZDAUZ529458LDN2DAR9QADWRZUTU8MCKN8ACH3R36YKE52DDF67SM5UDUH7PTATHXE65UCGFRCV49UTF29BGDSUWGRMC839HUZYQVAG985YUF5KJCAVGKRD8E4YMPJ38KR6HD2KHDGYPJDMJMN4Y3DTJW5MYX3VUPF32637LMAWFM4QCU7BMP74SCF4N5QXZZVBELAQMPYMJC2ZWJ9XFV33YXTTPH3SG9KMUABLL83P7L3UBVM763XJPR5UEHDZ73XJUGR9QP6299K5PU46XGVWKWFAKWYMRXUSLCFJ3WQTEC6QJH6E5V8X2RMA5TXGSBEWLGSMNQ2SH6G9MLKK4S486DN4M4QVDTYTM4JQETAJQGP76NUR4UNDXR4W4JQSNWDTUGX69SEDC3AL8ZF4TJBQQRXSX2A86UUWCDCFHJE94A8EP3Q6A2QTYKEE62MV4VJB2AB</PublicKey>
        <PrivateKey>04DMQBFL5GKK4RXWEZYPEY8QB5GHBBGW3EY766DFF2Q6PLFLR4SSRWXBNW5CFGFC84UJ8PKRDQQYWN9921AH8AGZAQRWJYZ7C3QUJJCJAAGGT5P5GGLY6K5W4JZJDW7Y8EWS6SSYCS2MCR93MY9DG6VXLBRYVZWDX6XNFU286HMAM9E82KJVZNYGC8RXRW43SFFWDLR8C96R58A42F3TGLXPVWLGMNPPAWS4S2SSMNQLUPBAQMP7AUKAGV5CKL342W6UKQY3TUPXKGFXFES725PEX93UXX4K8JRY3MPQ5S94VNPBXQ5N6LDBLWB67LBLDCGNQS3JEKW667UF8LA8Q6B3AF4XGZKYB7BVVHF49DJUHSFJZRD63S4GMMAPQ8Q87HKN9G3J3575JEL5C29NZLWQW6MSYWJ7S3NKLKPWGU75ZJXKEL6Z9V97HAMDYHM6ARCH58EPWRPZUXN8RRWGCDFM6BH6XUTRTFE3VMAAZAUUKHKL6AFC2PCPXFRDWLVYPZKUGY8QDEZNECK9K5Y5MET9SRWQ36JF77XJ6NUKSMZ6DKUPEH4W43YQAECV7XVFWJ7ZTRVFQJZFU5BS8AL77YLCXDCR5VPFFZ9NP7EBBQ2AA04DP3S86B3YD8AFXQ3KPPNTPHBTQSM8PUHJHG87RUM4EV63GKPBRGSN3VX63FCS646DLWSH86B542U3F04DP3S86B3YD8AFXQ3KPPNTPHBTQSM8PUHJHG87RUM4EV63GKPBRGSN3VX63FCS646DLWSH86B542U3F</PrivateKey>
      </Mode3072>
      <Mode4096>
        <PublicKey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ublicKey>
        <PrivateKey>067DN6F8YFBTWF5QH8MHJB39Z7VNN2P56HS8RPBURAWB8KPL8FQWX4HSPRTY4HWFMFXAVX2YUY99G2U2KKK56CBE4BCPZWS3KJ98XRW4PY2CDQ3ALFY3HGJG42NRY9ZVGZZB59TE29KGC825BYBX3FRFMYBVN2VTHM8YM2KULXMH24XSHNVK4HEQ9L5264844DMBFUUXSBA9PT9LEXT6449TMAMF7RL6KU6CGP6FU9DNMJCNTUREF2U9WUMJBMXMMHWKWDG4VU5JQNFFNNWCP2YE2DGFDN3H27NTNR58GBNTTZBRK66BKWNVMF8H59KALB6K93Y8UWDUWV5HJZGV8X3AS5EG982DYV4W9CQ3GRRFRR8NABQC98FTAG465SXQ3JPN7X7VULQZX4XUK68URBFPGCW2UPBYH6CRG6C7YBRAURM2KMGN5L8XH4AUQN8WM4P454WNNWRE5SQEC78FK3LHHQU5VLXLYYN8F2NXGG7CCQ92EEC63ZE6S7CC7RTMLUMM3PAXPL5CJZFL26JNNYPVLAHM75NDL8AS8JR48PFXK49D4A6TY2ZU9YXX2Y2TLA3J8T56EPY236FEDGZEHTQR73NE2Y7LKL7423FFKGGPWYL8PB5VWD54PG328ACE6V8VLSGVP9VJMMWS2VDVU459C8K5XK869Z5KM4JWHAVEG56ZESC6HHJ423N6SAH2DW2UQZDQHSMKWUCHPUKZZPJXCQXF55Q3BJW6ZQYK4HM4Q8XWU8NT844E8YM96EGHSYEUKAJ3QDXANN2DELSGARH7LR2XH9UQBQNXGPZGB6JTEQP8HAQK5ZZYJE067DWX6C2JJ7L2HE4NGVXZV5XH9RDP9SNG4WZTC9KEMGXDX7JTQX8RPJJRT4YTA83L8GXDNDDHB7C79CN8UWGLU5EMNMBREQN5MYTYHGYK067DWX6C2JJ7L2HE4NGVXZV5XH9RDP9SNG4WZTC9KEMGXDX7JTQX8RPJJRT4YTA83L8GXDNDDHB7C79CN8UWGLU5EMNMBREQN5MYTYHGYK</PrivateKey>
      </Mode4096>
    </Constants>
    <RegistrationDialog>
      <Controls Count="11">
        <Control ID="0">
          <Type>Form</Type>
          <Name>RegistrationForm</Name>
          <Width>400</Width>
          <Height>243</Height>
          <Font>
            <Name>MS Sans Serif</Name>
            <Color>$00000000</Color>
            <Size>8</Size>
            <Bold>False</Bold>
            <Italic>False</Italic>
            <Underline>False</Underline>
            <Strikeout>False</Strikeout>
          </Font>
          <Text>RegistrationForm</Text>
          <OnClick>3</OnClick>
          <BorderStyle>0</BorderStyle>
          <Color>$00D8E9EC</Color>
          <OnClose>2</OnClose>
        </Control>
        <Control ID="1">
          <Type>Groupbox</Type>
          <Name>GroupBox1</Name>
          <Parent>0</Parent>
          <Left>8</Left>
          <Top>8</Top>
          <Width>376</Width>
          <Height>46</Height>
          <Visible>True</Visible>
          <Font>
            <Name>MS Sans Serif</Name>
            <Color>$00000000</Color>
            <Size>8</Size>
            <Bold>True</Bold>
            <Italic>False</Italic>
            <Underline>False</Underline>
            <Strikeout>False</Strikeout>
          </Font>
          <Text>HardwareID</Text>
          <Enabled>True</Enabled>
          <TabStop>False</TabStop>
          <TabOrder>0</TabOrder>
          <Color>$00D8E9EC</Color>
        </Control>
        <Control ID="2">
          <Type>Edit</Type>
          <Name>Edit1</Name>
          <Parent>1</Parent>
          <Left>8</Left>
          <Top>16</Top>
          <Width>360</Width>
          <Height>21</Height>
          <Visible>True</Visible>
          <Font>
            <Name>MS Sans Serif</Name>
            <Color>$00000000</Color>
            <Size>8</Size>
            <Bold>True</Bold>
            <Italic>False</Italic>
            <Underline>False</Underline>
            <Strikeout>False</Strikeout>
          </Font>
          <Text/>
          <Enabled>True</Enabled>
          <TabStop>True</TabStop>
          <TabOrder>0</TabOrder>
          <ReadOnly>True</ReadOnly>
          <Content>2</Content>
          <Color>$00FFFFFF</Color>
        </Control>
        <Control ID="3">
          <Type>Groupbox</Type>
          <Name>GroupBox2</Name>
          <Parent>0</Parent>
          <Left>8</Left>
          <Top>64</Top>
          <Width>376</Width>
          <Height>103</Height>
          <Visible>True</Visible>
          <Font>
            <Name>MS Sans Serif</Name>
            <Color>$00000000</Color>
            <Size>8</Size>
            <Bold>True</Bold>
            <Italic>False</Italic>
            <Underline>False</Underline>
            <Strikeout>False</Strikeout>
          </Font>
          <Text>Registration Information</Text>
          <Enabled>True</Enabled>
          <TabStop>False</TabStop>
          <TabOrder>1</TabOrder>
          <Color>$00D8E9EC</Color>
        </Control>
        <Control ID="4">
          <Type>Label</Type>
          <Name>Label1</Name>
          <Parent>3</Parent>
          <Left>8</Left>
          <Top>16</Top>
          <Width>39</Width>
          <Height>13</Height>
          <Visible>True</Visible>
          <Font>
            <Name>MS Sans Serif</Name>
            <Color>$00000000</Color>
            <Size>8</Size>
            <Bold>False</Bold>
            <Italic>False</Italic>
            <Underline>False</Underline>
            <Strikeout>False</Strikeout>
          </Font>
          <Text>Name</Text>
          <Enabled>True</Enabled>
          <Color>$00D8E9EC</Color>
        </Control>
        <Control ID="5">
          <Type>Edit</Type>
          <Name>Edit2</Name>
          <Parent>3</Parent>
          <Left>8</Left>
          <Top>32</Top>
          <Width>360</Width>
          <Height>21</Height>
          <Visible>True</Visible>
          <Font>
            <Name>MS Sans Serif</Name>
            <Color>$00000000</Color>
            <Size>8</Size>
            <Bold>True</Bold>
            <Italic>False</Italic>
            <Underline>False</Underline>
            <Strikeout>False</Strikeout>
          </Font>
          <Text/>
          <Enabled>True</Enabled>
          <TabStop>True</TabStop>
          <TabOrder>0</TabOrder>
          <ReadOnly>False</ReadOnly>
          <Content>0</Content>
          <Color>$00FFFFFF</Color>
        </Control>
        <Control ID="6">
          <Type>Label</Type>
          <Name>Label2</Name>
          <Parent>3</Parent>
          <Left>8</Left>
          <Top>56</Top>
          <Width>39</Width>
          <Height>13</Height>
          <Visible>True</Visible>
          <Font>
            <Name>MS Sans Serif</Name>
            <Color>$00000000</Color>
            <Size>8</Size>
            <Bold>False</Bold>
            <Italic>False</Italic>
            <Underline>False</Underline>
            <Strikeout>False</Strikeout>
          </Font>
          <Text>Key</Text>
          <Enabled>True</Enabled>
          <Color>$00D8E9EC</Color>
        </Control>
        <Control ID="7">
          <Type>Edit</Type>
          <Name>Edit3</Name>
          <Parent>3</Parent>
          <Left>8</Left>
          <Top>72</Top>
          <Width>360</Width>
          <Height>21</Height>
          <Visible>True</Visible>
          <Font>
            <Name>MS Sans Serif</Name>
            <Color>$00000000</Color>
            <Size>8</Size>
            <Bold>True</Bold>
            <Italic>False</Italic>
            <Underline>False</Underline>
            <Strikeout>False</Strikeout>
          </Font>
          <Text/>
          <Enabled>True</Enabled>
          <TabStop>True</TabStop>
          <TabOrder>1</TabOrder>
          <ReadOnly>False</ReadOnly>
          <Content>1</Content>
          <Color>$00FFFFFF</Color>
        </Control>
        <Control ID="8">
          <Type>Button</Type>
          <Name>Button1</Name>
          <Parent>0</Parent>
          <Left>8</Left>
          <Top>176</Top>
          <Width>100</Width>
          <Height>25</Height>
          <Visible>True</Visible>
          <Font>
            <Name>MS Sans Serif</Name>
            <Color>$00000000</Color>
            <Size>8</Size>
            <Bold>False</Bold>
            <Italic>False</Italic>
            <Underline>False</Underline>
            <Strikeout>False</Strikeout>
          </Font>
          <Text>Try</Text>
          <Default>False</Default>
          <Cancel>True</Cancel>
          <Enabled>True</Enabled>
          <OnClick>1</OnClick>
          <TabStop>True</TabStop>
          <TabOrder>2</TabOrder>
        </Control>
        <Control ID="9">
          <Type>Button</Type>
          <Name>Button2</Name>
          <Parent>0</Parent>
          <Left>148</Left>
          <Top>176</Top>
          <Width>100</Width>
          <Height>25</Height>
          <Visible>True</Visible>
          <Font>
            <Name>MS Sans Serif</Name>
            <Color>$00000000</Color>
            <Size>8</Size>
            <Bold>False</Bold>
            <Italic>False</Italic>
            <Underline>False</Underline>
            <Strikeout>False</Strikeout>
          </Font>
          <Text>Register</Text>
          <Default>True</Default>
          <Cancel>False</Cancel>
          <Enabled>True</Enabled>
          <OnClick>0</OnClick>
          <TabStop>True</TabStop>
          <TabOrder>3</TabOrder>
        </Control>
        <Control ID="10">
          <Type>Button</Type>
          <Name>Button3</Name>
          <Parent>0</Parent>
          <Left>285</Left>
          <Top>176</Top>
          <Width>100</Width>
          <Height>25</Height>
          <Visible>True</Visible>
          <Font>
            <Name>MS Sans Serif</Name>
            <Color>$00000000</Color>
            <Size>8</Size>
            <Bold>False</Bold>
            <Italic>False</Italic>
            <Underline>False</Underline>
            <Strikeout>False</Strikeout>
          </Font>
          <Text>Close</Text>
          <Default>False</Default>
          <Cancel>False</Cancel>
          <Enabled>True</Enabled>
          <OnClick>2</OnClick>
          <TabStop>True</TabStop>
          <TabOrder>4</TabOrder>
        </Control>
      </Controls>
      <Enabled>False</Enabled>
      <ShowOnTrialExpire>False</ShowOnTrialExpire>
      <ShowOnUnregistered>True</ShowOnUnregistered>
      <ShowCorrectMessage>False</ShowCorrectMessage>
      <ShowIncorrectMessage>False</ShowIncorrectMessage>
      <MessageCorrect>
        <Title>The Registration Succeeded</Title>
        <Text>Thanks for registration!</Text>
        <UseHL>True</UseHL>
        <HLSentence>The Enigma Protector</HLSentence>
        <HLURL>http://www.enigmaprotector.com/</HLURL>
      </MessageCorrect>
      <MessageIncorrect>
        <Title>The Registration did not succeeded</Title>
        <Text>Your registration information is incorrect!</Text>
        <UseHL>True</UseHL>
        <HLSentence>The Enigma Protector</HLSentence>
        <HLURL>http://www.enigmaprotector.com/</HLURL>
      </MessageIncorrect>
    </RegistrationDialog>
    <KeyExpirationReminder>
      <Enabled>False</Enabled>
      <DaysBeforeExpiration>30</DaysBeforeExpiration>
      <Message>
        <Title>Registration Key Expiration Reminder</Title>
        <Text>Your registration key will expire in %DaysToKeyExp% days. 
Please contact to our support team to obtain new one!
Use the link below to visit our web site.</Text>
        <UseHL>True</UseHL>
        <HLSentence>The Enigma Protector</HLSentence>
        <HLURL>http://enigmaprotector.com/</HLURL>
      </Message>
    </KeyExpirationReminder>
  </RegistrationFeatures>
  <CheckUp>
    <AntiDebugger>
      <Enabled>False</Enabled>
      <ShowMessageBeforeTermination>True</ShowMessageBeforeTermination>
      <Message>
        <Title>Debugger warning</Title>
        <Text>A debugger is detected on this computer.
Applications such as WinIce/SoftIce, OllyDbg, TWD could be executing.
This program will not execute while debuggers are running.</Text>
        <UseHL>True</UseHL>
        <HLSentence>Click here for help</HLSentence>
        <HLURL>http://www.yourcompany.com/help/</HLURL>
      </Message>
    </AntiDebugger>
    <ControlSum>
      <Enabled>False</Enabled>
      <RTIntegrityChecking>False</RTIntegrityChecking>
      <ShowMessageBeforeTermination>True</ShowMessageBeforeTermination>
      <Message>
        <Title>Virus warning</Title>
        <Text>Program integrity check failed. 
Please check for viruses on your computer. 
If program is not infected, it had been patched 
or tampered  - please re-download and re-install. 
This program will now close.</Text>
        <UseHL>True</UseHL>
        <HLSentence>Click here for help</HLSentence>
        <HLURL>http://www.yourcompany.com/help/</HLURL>
      </Message>
    </ControlSum>
    <StartupPassword>
      <Enabled>False</Enabled>
      <AlwaysAsk>True</AlwaysAsk>
      <AskFirstTime>False</AskFirstTime>
      <AskInDays>False</AskInDays>
      <DaysAfterAsk>30</DaysAfterAsk>
      <HidePassword>True</HidePassword>
      <AllowChange>False</AllowChange>
      <Message>
        <Title>Invalid password</Title>
        <Text>You have entered an invalid startup password.
Try to check it again or contact the software developers:</Text>
        <UseHL>True</UseHL>
        <HLSentence>Click here for registration</HLSentence>
        <HLURL>http://www.yourcompany.com/</HLURL>
      </Message>
      <Passwords Count="0"/>
    </StartupPassword>
    <FileName>
      <Enabled>False</Enabled>
      <OriginalFileName>notepad_protected.exe</OriginalFileName>
      <ShowMessageBeforeTermination>True</ShowMessageBeforeTermination>
      <Message>
        <Title>Invalid File Name</Title>
        <Text>The software&apos;s file name had been changed.  
Please restore the original file name. 
This program will now close.</Text>
        <UseHL>True</UseHL>
        <HLSentence>Press here to download official version!</HLSentence>
        <HLURL>http://www.yourcompany.com/downloads/</HLURL>
      </Message>
    </FileName>
    <DiskDrive>
      <Enabled>False</Enabled>
      <CheckUpPolicy>AllowAll</CheckUpPolicy>
      <Selection>
        <Removable>False</Removable>
        <Fixed>False</Fixed>
        <Remote>False</Remote>
        <CDROM>False</CDROM>
        <RAMDisk>False</RAMDisk>
      </Selection>
      <ShowMessageBeforeTermination>True</ShowMessageBeforeTermination>
      <Message>
        <Title>Invalid drive type</Title>
        <Text>The software has checked impossible disk drive type!
Try to contact to developers or download newest version of this software at:</Text>
        <UseHL>True</UseHL>
        <HLSentence>Click here for newest version!</HLSentence>
        <HLURL>http://www.yourcompany.com/downloads/</HLURL>
      </Message>
    </DiskDrive>
    <ExecutedCopies>
      <Enabled>False</Enabled>
      <AllowCopies>1</AllowCopies>
      <ShowMessageIfCopiesCountIsExceeded>True</ShowMessageIfCopiesCountIsExceeded>
      <Message>
        <Title>Warning</Title>
        <Text>You have exceeded maximal number of executed copies. 
Application won&apos;t be started.</Text>
        <UseHL>True</UseHL>
        <HLSentence>Software information</HLSentence>
        <HLURL>http://www.yourcompany.com/</HLURL>
      </Message>
    </ExecutedCopies>
    <UserLanguage>
      <Enabled>False</Enabled>
      <ALB>True</ALB>
      <DZA>True</DZA>
      <ARG>True</ARG>
      <ARM>True</ARM>
      <AUS>True</AUS>
      <AUT>True</AUT>
      <AZE>True</AZE>
      <BHR>True</BHR>
      <BLR>True</BLR>
      <BEL>True</BEL>
      <BLZ>True</BLZ>
      <BOL>True</BOL>
      <BRA>True</BRA>
      <BRN>True</BRN>
      <BGR>True</BGR>
      <KHM>True</KHM>
      <CAN>True</CAN>
      <CAF>True</CAF>
      <TCD>True</TCD>
      <CHL>True</CHL>
      <CHN>True</CHN>
      <COL>True</COL>
      <CRI>True</CRI>
      <HRV>True</HRV>
      <CZE>True</CZE>
      <DNK>True</DNK>
      <DOM>True</DOM>
      <ECU>True</ECU>
      <EGY>True</EGY>
      <SLV>True</SLV>
      <EST>True</EST>
      <FRO>True</FRO>
      <FIN>True</FIN>
      <FRA>True</FRA>
      <GEO>True</GEO>
      <DEU>True</DEU>
      <GRC>True</GRC>
      <GTM>True</GTM>
      <HND>True</HND>
      <HKG>True</HKG>
      <HUN>True</HUN>
      <ISL>True</ISL>
      <IND>True</IND>
      <IDN>True</IDN>
      <IRN>True</IRN>
      <IRQ>True</IRQ>
      <IRL>True</IRL>
      <ISR>True</ISR>
      <ITA>True</ITA>
      <JAM>True</JAM>
      <JPN>True</JPN>
      <JOR>True</JOR>
      <KAZ>True</KAZ>
      <KEN>True</KEN>
      <PRK>True</PRK>
      <KOR>True</KOR>
      <KWT>True</KWT>
      <KGZ>True</KGZ>
      <LVA>True</LVA>
      <LBN>True</LBN>
      <LBR>True</LBR>
      <LIE>True</LIE>
      <LTU>True</LTU>
      <LUX>True</LUX>
      <MAC>True</MAC>
      <MKD>True</MKD>
      <MYS>True</MYS>
      <MEX>True</MEX>
      <MDA>True</MDA>
      <MCO>True</MCO>
      <MNG>True</MNG>
      <MAR>True</MAR>
      <NLD>True</NLD>
      <NZL>True</NZL>
      <NIC>True</NIC>
      <NOR>True</NOR>
      <OMN>True</OMN>
      <PAK>True</PAK>
      <PAN>True</PAN>
      <PRY>True</PRY>
      <PER>True</PER>
      <PHL>True</PHL>
      <POL>True</POL>
      <PRT>True</PRT>
      <PRI>True</PRI>
      <QAT>True</QAT>
      <ROU>True</ROU>
      <RUS>True</RUS>
      <SAU>True</SAU>
      <SCG>True</SCG>
      <SGP>True</SGP>
      <SVK>True</SVK>
      <SVN>True</SVN>
      <ZAF>True</ZAF>
      <ESP>True</ESP>
      <SWE>True</SWE>
      <CHE>True</CHE>
      <TJK>True</TJK>
      <THA>True</THA>
      <TTO>True</TTO>
      <TUN>True</TUN>
      <TUR>True</TUR>
      <UKR>True</UKR>
      <ARE>True</ARE>
      <GBR>True</GBR>
      <USA>True</USA>
      <URY>True</URY>
      <UZB>True</UZB>
      <VEN>True</VEN>
      <VNM>True</VNM>
      <YEM>True</YEM>
      <ZWE>True</ZWE>
      <Others>True</Others>
      <ShowMessageBeforeTermination>True</ShowMessageBeforeTermination>
      <Message>
        <Title>Warning</Title>
        <Text>You have not permissions to start this application!
Please, contact to developers.</Text>
        <UseHL>True</UseHL>
        <HLSentence>Software information</HLSentence>
        <HLURL>http://www.yourcompany.com/</HLURL>
      </Message>
    </UserLanguage>
    <ExternalFiles>
      <Enabled>False</Enabled>
      <ShowMessageBeforeTermination>True</ShowMessageBeforeTermination>
      <Message>
        <Title>Warning</Title>
        <Text>The installation package had beed modified! 
File: %CU_EXTFILES% is corrupted!
Please reinstall application!</Text>
        <UseHL>True</UseHL>
        <HLSentence>Enigma Protector</HLSentence>
        <HLURL>http://www.enigmaprotector.com/</HLURL>
      </Message>
      <Files Count="0"/>
    </ExternalFiles>
    <ExecutedProcesses>
      <Enabled>False</Enabled>
      <ShowMessageBeforeTermination>True</ShowMessageBeforeTermination>
      <Message>
        <Title>Warning</Title>
        <Text>Spyware tool is executed on this PC. 
Please close the application %CU_EXECPR% try again!</Text>
        <UseHL>True</UseHL>
        <HLSentence>Enigma Protector</HLSentence>
        <HLURL>http://www.enigmaprotector.com/</HLURL>
      </Message>
      <Processes Count="0"/>
    </ExecutedProcesses>
    <LoadedDrivers>
      <Enabled>False</Enabled>
      <ShowMessageBeforeTermination>False</ShowMessageBeforeTermination>
      <Message>
        <Title>Warning</Title>
        <Text>Spyware tool is executed on this PC. 
Please close this tool try again!</Text>
        <UseHL>True</UseHL>
        <HLSentence>Enigma Protector</HLSentence>
        <HLURL>http://www.enigmaprotector.com/</HLURL>
      </Message>
      <Drivers Count="0"/>
    </LoadedDrivers>
    <InstalledServices>
      <Enabled>False</Enabled>
      <ShowMessageBeforeTermination>True</ShowMessageBeforeTermination>
      <Message>
        <Title>Warning</Title>
        <Text>Spyware tool is installed on this PC. 
Uninstall service &quot;%CU_INSTSERV%&quot; and try again!</Text>
        <UseHL>True</UseHL>
        <HLSentence>Enigma Protector</HLSentence>
        <HLURL>http://www.enigmaprotector.com/</HLURL>
      </Message>
      <Services Count="0"/>
    </InstalledServices>
    <WindowsVersion>
      <Enabled>False</Enabled>
      <WIN_VER_WINDOWS95>False</WIN_VER_WINDOWS95>
      <WIN_VER_WINDOWS95OSR2>False</WIN_VER_WINDOWS95OSR2>
      <WIN_VER_WINDOWS98>False</WIN_VER_WINDOWS98>
      <WIN_VER_WINDOWS98SE>False</WIN_VER_WINDOWS98SE>
      <WIN_VER_WINDOWSME>False</WIN_VER_WINDOWSME>
      <WIN_VER_WINDOWS2000>False</WIN_VER_WINDOWS2000>
      <WIN_VER_WINDOWS2000PROF>False</WIN_VER_WINDOWS2000PROF>
      <WIN_VER_WINDOWS2000DATASERVER>False</WIN_VER_WINDOWS2000DATASERVER>
      <WIN_VER_WINDOWS2000ADVSERVER>False</WIN_VER_WINDOWS2000ADVSERVER>
      <WIN_VER_WINDOWS2000SERVER>False</WIN_VER_WINDOWS2000SERVER>
      <WIN_VER_WINDOWSXP>False</WIN_VER_WINDOWSXP>
      <WIN_VER_WINDOWSXPHOME>False</WIN_VER_WINDOWSXPHOME>
      <WIN_VER_WINDOWSXPPROF>False</WIN_VER_WINDOWSXPPROF>
      <WIN_VER_WINDOWSXPPROFx64>False</WIN_VER_WINDOWSXPPROFx64>
      <WIN_VER_WINDOWSXPPROFDATACENTERx64>False</WIN_VER_WINDOWSXPPROFDATACENTERx64>
      <WIN_VER_WINDOWSXPPROFENERPRICEx64>False</WIN_VER_WINDOWSXPPROFENERPRICEx64>
      <WIN_VER_WINDOWSXPPROFSTANDARTx64>False</WIN_VER_WINDOWSXPPROFSTANDARTx64>
      <WIN_VER_WINDOWS2003SERVER>False</WIN_VER_WINDOWS2003SERVER>
      <WIN_VER_WINDOWS2003SERVERR2>False</WIN_VER_WINDOWS2003SERVERR2>
      <WIN_VER_WINDOWS2003STORAGESERVER>False</WIN_VER_WINDOWS2003STORAGESERVER>
      <WIN_VER_WINDOWS2003DATACENTERITANIUM>False</WIN_VER_WINDOWS2003DATACENTERITANIUM>
      <WIN_VER_WINDOWS2003ENTERPRICEITANIUM>False</WIN_VER_WINDOWS2003ENTERPRICEITANIUM>
      <WIN_VER_WINDOWS2003DATACENTERx64>False</WIN_VER_WINDOWS2003DATACENTERx64>
      <WIN_VER_WINDOWS2003ENERPRICEx64>False</WIN_VER_WINDOWS2003ENERPRICEx64>
      <WIN_VER_WINDOWS2003STANDARTx64>False</WIN_VER_WINDOWS2003STANDARTx64>
      <WIN_VER_WINDOWS2003COMPUTE>False</WIN_VER_WINDOWS2003COMPUTE>
      <WIN_VER_WINDOWS2003DATACENTER>False</WIN_VER_WINDOWS2003DATACENTER>
      <WIN_VER_WINDOWS2003ENTERPRICE>False</WIN_VER_WINDOWS2003ENTERPRICE>
      <WIN_VER_WINDOWS2003WEB>False</WIN_VER_WINDOWS2003WEB>
      <WIN_VER_WINDOWS2003STANDART>False</WIN_VER_WINDOWS2003STANDART>
      <WIN_VER_WINDOWSVISTA>False</WIN_VER_WINDOWSVISTA>
      <WIN_VER_WINDOWSVISTA_BUSINESS>False</WIN_VER_WINDOWSVISTA_BUSINESS>
      <WIN_VER_WINDOWSVISTA_CLUSTER_SERVER>False</WIN_VER_WINDOWSVISTA_CLUSTER_SERVER>
      <WIN_VER_WINDOWSVISTA_DATACENTER_SERVER>False</WIN_VER_WINDOWSVISTA_DATACENTER_SERVER>
      <WIN_VER_WINDOWSVISTA_DATACENTER_SERVER_CORE>False</WIN_VER_WINDOWSVISTA_DATACENTER_SERVER_CORE>
      <WIN_VER_WINDOWSVISTA_DATACENTER_SERVER_CORE_V>False</WIN_VER_WINDOWSVISTA_DATACENTER_SERVER_CORE_V>
      <WIN_VER_WINDOWSVISTA_DATACENTER_SERVER_V>False</WIN_VER_WINDOWSVISTA_DATACENTER_SERVER_V>
      <WIN_VER_WINDOWSVISTA_ENTERPRICE>False</WIN_VER_WINDOWSVISTA_ENTERPRICE>
      <WIN_VER_WINDOWSVISTA_ENTERPRISE_SERVER>False</WIN_VER_WINDOWSVISTA_ENTERPRISE_SERVER>
      <WIN_VER_WINDOWSVISTA_ENTERPRISE_SERVER_CORE>False</WIN_VER_WINDOWSVISTA_ENTERPRISE_SERVER_CORE>
      <WIN_VER_WINDOWSVISTA_ENTERPRISE_SERVER_V>False</WIN_VER_WINDOWSVISTA_ENTERPRISE_SERVER_V>
      <WIN_VER_WINDOWSVISTA_ENTERPRISE_SERVER_CORE_V>False</WIN_VER_WINDOWSVISTA_ENTERPRISE_SERVER_CORE_V>
      <WIN_VER_WINDOWSVISTA_ENTERPRISE_SERVER_IA64>False</WIN_VER_WINDOWSVISTA_ENTERPRISE_SERVER_IA64>
      <WIN_VER_WINDOWSVISTA_HOME_BASIC>False</WIN_VER_WINDOWSVISTA_HOME_BASIC>
      <WIN_VER_WINDOWSVISTA_HOME_PREMIUM>False</WIN_VER_WINDOWSVISTA_HOME_PREMIUM>
      <WIN_VER_WINDOWSVISTA_HOME_SERVER>False</WIN_VER_WINDOWSVISTA_HOME_SERVER>
      <WIN_VER_WINDOWSVISTA_SERVER_FOR_SMALLBUSINESS>False</WIN_VER_WINDOWSVISTA_SERVER_FOR_SMALLBUSINESS>
      <WIN_VER_WINDOWSVISTA_SMALLBUSINESS_SERVER>False</WIN_VER_WINDOWSVISTA_SMALLBUSINESS_SERVER>
      <WIN_VER_WINDOWSVISTA_SMALLBUSINESS_SERVER_PREMIUM>False</WIN_VER_WINDOWSVISTA_SMALLBUSINESS_SERVER_PREMIUM>
      <WIN_VER_WINDOWSVISTA_MEDIUMBUSINESS_SERVER_MANAGEMENT>False</WIN_VER_WINDOWSVISTA_MEDIUMBUSINESS_SERVER_MANAGEMENT>
      <WIN_VER_WINDOWSVISTA_MEDIUMBUSINESS_SERVER_MESSAGING>False</WIN_VER_WINDOWSVISTA_MEDIUMBUSINESS_SERVER_MESSAGING>
      <WIN_VER_WINDOWSVISTA_MEDIUMBUSINESS_SERVER_SECURITY>False</WIN_VER_WINDOWSVISTA_MEDIUMBUSINESS_SERVER_SECURITY>
      <WIN_VER_WINDOWSVISTA_STANDARD_SERVER>False</WIN_VER_WINDOWSVISTA_STANDARD_SERVER>
      <WIN_VER_WINDOWSVISTA_STANDARD_SERVER_V>False</WIN_VER_WINDOWSVISTA_STANDARD_SERVER_V>
      <WIN_VER_WINDOWSVISTA_STANDARD_SERVER_CORE>False</WIN_VER_WINDOWSVISTA_STANDARD_SERVER_CORE>
      <WIN_VER_WINDOWSVISTA_STANDARD_SERVER_CORE_V>False</WIN_VER_WINDOWSVISTA_STANDARD_SERVER_CORE_V>
      <WIN_VER_WINDOWSVISTA_STARTER>False</WIN_VER_WINDOWSVISTA_STARTER>
      <WIN_VER_WINDOWSVISTA_STORAGE_ENTERPRISE_SERVER>False</WIN_VER_WINDOWSVISTA_STORAGE_ENTERPRISE_SERVER>
      <WIN_VER_WINDOWSVISTA_STORAGE_EXPRESS_SERVER>False</WIN_VER_WINDOWSVISTA_STORAGE_EXPRESS_SERVER>
      <WIN_VER_WINDOWSVISTA_STORAGE_STANDARD_SERVER>False</WIN_VER_WINDOWSVISTA_STORAGE_STANDARD_SERVER>
      <WIN_VER_WINDOWSVISTA_STORAGE_WORKGROUP_SERVER>False</WIN_VER_WINDOWSVISTA_STORAGE_WORKGROUP_SERVER>
      <WIN_VER_WINDOWSVISTA_UNDEFINED>False</WIN_VER_WINDOWSVISTA_UNDEFINED>
      <WIN_VER_WINDOWSVISTA_ULTIMATE>False</WIN_VER_WINDOWSVISTA_ULTIMATE>
      <WIN_VER_WINDOWSVISTA_WEB_SERVER>False</WIN_VER_WINDOWSVISTA_WEB_SERVER>
      <WIN_VER_WINDOWSVISTA_WEB_SERVER_CORE>False</WIN_VER_WINDOWSVISTA_WEB_SERVER_CORE>
      <WIN_VER_WINDOWSVISTA_UNLICENSED>False</WIN_VER_WINDOWSVISTA_UNLICENSED>
      <WIN_VER_WINDOWS2008>False</WIN_VER_WINDOWS2008>
      <WIN_VER_WINDOWS2008_BUSINESS>False</WIN_VER_WINDOWS2008_BUSINESS>
      <WIN_VER_WINDOWS2008_CLUSTER_SERVER>False</WIN_VER_WINDOWS2008_CLUSTER_SERVER>
      <WIN_VER_WINDOWS2008_DATACENTER_SERVER>False</WIN_VER_WINDOWS2008_DATACENTER_SERVER>
      <WIN_VER_WINDOWS2008_DATACENTER_SERVER_CORE>False</WIN_VER_WINDOWS2008_DATACENTER_SERVER_CORE>
      <WIN_VER_WINDOWS2008_DATACENTER_SERVER_CORE_V>False</WIN_VER_WINDOWS2008_DATACENTER_SERVER_CORE_V>
      <WIN_VER_WINDOWS2008_DATACENTER_SERVER_V>False</WIN_VER_WINDOWS2008_DATACENTER_SERVER_V>
      <WIN_VER_WINDOWS2008_ENTERPRICE>False</WIN_VER_WINDOWS2008_ENTERPRICE>
      <WIN_VER_WINDOWS2008_ENTERPRISE_SERVER>False</WIN_VER_WINDOWS2008_ENTERPRISE_SERVER>
      <WIN_VER_WINDOWS2008_ENTERPRISE_SERVER_CORE>False</WIN_VER_WINDOWS2008_ENTERPRISE_SERVER_CORE>
      <WIN_VER_WINDOWS2008_ENTERPRISE_SERVER_V>False</WIN_VER_WINDOWS2008_ENTERPRISE_SERVER_V>
      <WIN_VER_WINDOWS2008_ENTERPRISE_SERVER_CORE_V>False</WIN_VER_WINDOWS2008_ENTERPRISE_SERVER_CORE_V>
      <WIN_VER_WINDOWS2008_ENTERPRISE_SERVER_IA64>False</WIN_VER_WINDOWS2008_ENTERPRISE_SERVER_IA64>
      <WIN_VER_WINDOWS2008_HOME_BASIC>False</WIN_VER_WINDOWS2008_HOME_BASIC>
      <WIN_VER_WINDOWS2008_HOME_PREMIUM>False</WIN_VER_WINDOWS2008_HOME_PREMIUM>
      <WIN_VER_WINDOWS2008_HOME_SERVER>False</WIN_VER_WINDOWS2008_HOME_SERVER>
      <WIN_VER_WINDOWS2008_SERVER_FOR_SMALLBUSINESS>False</WIN_VER_WINDOWS2008_SERVER_FOR_SMALLBUSINESS>
      <WIN_VER_WINDOWS2008_SMALLBUSINESS_SERVER>False</WIN_VER_WINDOWS2008_SMALLBUSINESS_SERVER>
      <WIN_VER_WINDOWS2008_SMALLBUSINESS_SERVER_PREMIUM>False</WIN_VER_WINDOWS2008_SMALLBUSINESS_SERVER_PREMIUM>
      <WIN_VER_WINDOWS2008_MEDIUMBUSINESS_SERVER_MANAGEMENT>False</WIN_VER_WINDOWS2008_MEDIUMBUSINESS_SERVER_MANAGEMENT>
      <WIN_VER_WINDOWS2008_MEDIUMBUSINESS_SERVER_MESSAGING>False</WIN_VER_WINDOWS2008_MEDIUMBUSINESS_SERVER_MESSAGING>
      <WIN_VER_WINDOWS2008_MEDIUMBUSINESS_SERVER_SECURITY>False</WIN_VER_WINDOWS2008_MEDIUMBUSINESS_SERVER_SECURITY>
      <WIN_VER_WINDOWS2008_STANDARD_SERVER>False</WIN_VER_WINDOWS2008_STANDARD_SERVER>
      <WIN_VER_WINDOWS2008_STANDARD_SERVER_V>False</WIN_VER_WINDOWS2008_STANDARD_SERVER_V>
      <WIN_VER_WINDOWS2008_STANDARD_SERVER_CORE>False</WIN_VER_WINDOWS2008_STANDARD_SERVER_CORE>
      <WIN_VER_WINDOWS2008_STANDARD_SERVER_CORE_V>False</WIN_VER_WINDOWS2008_STANDARD_SERVER_CORE_V>
      <WIN_VER_WINDOWS2008_STARTER>False</WIN_VER_WINDOWS2008_STARTER>
      <WIN_VER_WINDOWS2008_STORAGE_ENTERPRISE_SERVER>False</WIN_VER_WINDOWS2008_STORAGE_ENTERPRISE_SERVER>
      <WIN_VER_WINDOWS2008_STORAGE_EXPRESS_SERVER>False</WIN_VER_WINDOWS2008_STORAGE_EXPRESS_SERVER>
      <WIN_VER_WINDOWS2008_STORAGE_STANDARD_SERVER>False</WIN_VER_WINDOWS2008_STORAGE_STANDARD_SERVER>
      <WIN_VER_WINDOWS2008_STORAGE_WORKGROUP_SERVER>False</WIN_VER_WINDOWS2008_STORAGE_WORKGROUP_SERVER>
      <WIN_VER_WINDOWS2008_UNDEFINED>False</WIN_VER_WINDOWS2008_UNDEFINED>
      <WIN_VER_WINDOWS2008_ULTIMATE>False</WIN_VER_WINDOWS2008_ULTIMATE>
      <WIN_VER_WINDOWS2008_WEB_SERVER>False</WIN_VER_WINDOWS2008_WEB_SERVER>
      <WIN_VER_WINDOWS2008_WEB_SERVER_CORE>False</WIN_VER_WINDOWS2008_WEB_SERVER_CORE>
      <WIN_VER_WINDOWS2008_UNLICENSED>False</WIN_VER_WINDOWS2008_UNLICENSED>
      <ShowMessageBeforeTermination>True</ShowMessageBeforeTermination>
      <Message>
        <Title>Warning</Title>
        <Text>Current Windows Version &quot;%CU_WINVER%&quot; does not support!</Text>
        <UseHL>True</UseHL>
        <HLSentence>Enigma Protector</HLSentence>
        <HLURL>http://www.enigmaprotector.com/</HLURL>
      </Message>
    </WindowsVersion>
    <VirtualizationTools>
      <Enabled>False</Enabled>
      <VMWare>False</VMWare>
      <VirtualPC>False</VirtualPC>
      <Sandboxie>False</Sandboxie>
      <ShowMessageBeforeTermination>True</ShowMessageBeforeTermination>
      <Message>
        <Title>Warning</Title>
        <Text>This program can not be run under %CU_VIRTTOOLS%!</Text>
        <UseHL>True</UseHL>
        <HLSentence>Enigma Protector</HLSentence>
        <HLURL>http://www.enigmaprotector.com/</HLURL>
      </Message>
      <VirtualBox>False</VirtualBox>
    </VirtualizationTools>
    <Privileges>
      <Administrator>
        <Enabled>False</Enabled>
        <ShowMessageBeforeTermination>False</ShowMessageBeforeTermination>
        <Message>
          <Title>Warning</Title>
          <Text>You have not enough permissions to run this application. 
Please switch on administrator privileges of your Windows account and 
try again!</Text>
          <UseHL>True</UseHL>
          <HLSentence>Enigma Protector</HLSentence>
          <HLURL>http://enigmaprotector.com/</HLURL>
        </Message>
      </Administrator>
    </Privileges>
  </CheckUp>
  <Protection>
    <FileAnalyzerDeception>
      <NameOfTheProtectionCodeSection>.data</NameOfTheProtectionCodeSection>
      <ProtectionCodeMarker>BorlandDelphi</ProtectionCodeMarker>
    </FileAnalyzerDeception>
    <OriginalFileSizePreservation>
      <Enabled>False</Enabled>
    </OriginalFileSizePreservation>
    <ExtraResourceProtection>
      <Enabled>True</Enabled>
    </ExtraResourceProtection>
    <AdvanceForceImportProtection>
      <Enabled>False</Enabled>
    </AdvanceForceImportProtection>
    <WinAPIFunctionsRedirection>
      <Enabled>True</Enabled>
    </WinAPIFunctionsRedirection>
    <WinAPIFunctionsEmulation>
      <Enabled>True</Enabled>
    </WinAPIFunctionsEmulation>
    <FileAttachment>
      <Enabled>False</Enabled>
      <AllowLoadingFromAnyFolder>False</AllowLoadingFromAnyFolder>
      <DeleteExtractedOnExit>False</DeleteExtractedOnExit>
      <Files Count="0"/>
    </FileAttachment>
    <ProtectedStrings>
      <Strings Count="0"/>
    </ProtectedStrings>
  </Protection>
  <Obfuscation>
    <FileEntryPointSophistication>
      <Enabled>True</Enabled>
    </FileEntryPointSophistication>
  </Obfuscation>
  <Miscellaneous>
    <SplashScreenEncapsulation>
      <Enabled>False</Enabled>
      <SplashScreenPictureFileName/>
      <ShowOnTop>True</ShowOnTop>
      <TimeToShow>2000</TimeToShow>
      <StopExecution>False</StopExecution>
      <CloseOnMouseClick>False</CloseOnMouseClick>
    </SplashScreenEncapsulation>
    <Watermarks>
      <Enabled>False</Enabled>
      <Watermarks Count="0"/>
    </Watermarks>
    <Plugins>
      <Plugins Count="0"/>
    </Plugins>
  </Miscellaneous>
  <VirtualMachine>
    <Enabled>False</Enabled>
    <Filter/>
    <CommandLine/>
    <Functions Count="0"/>
  </VirtualMachine>
  <TrialControl>
    <Common>
      <ResetTrialInNewVersion>True</ResetTrialInNewVersion>
      <OpenFileIfTrialExpired>False</OpenFileIfTrialExpired>
      <OpenFileName>application.exe</OpenFileName>
    </Common>
    <LockTrialToUserLanguage>
      <Enabled>False</Enabled>
      <ALB>True</ALB>
      <DZA>True</DZA>
      <ARG>True</ARG>
      <ARM>True</ARM>
      <AUS>True</AUS>
      <AUT>True</AUT>
      <AZE>True</AZE>
      <BHR>True</BHR>
      <BLR>True</BLR>
      <BEL>True</BEL>
      <BLZ>True</BLZ>
      <BOL>True</BOL>
      <BRA>True</BRA>
      <BRN>True</BRN>
      <BGR>True</BGR>
      <KHM>True</KHM>
      <CAN>True</CAN>
      <CAF>True</CAF>
      <TCD>True</TCD>
      <CHL>True</CHL>
      <CHN>True</CHN>
      <COL>True</COL>
      <CRI>True</CRI>
      <HRV>True</HRV>
      <CZE>True</CZE>
      <DNK>True</DNK>
      <DOM>True</DOM>
      <ECU>True</ECU>
      <EGY>True</EGY>
      <SLV>True</SLV>
      <EST>True</EST>
      <FRO>True</FRO>
      <FIN>True</FIN>
      <FRA>True</FRA>
      <GEO>True</GEO>
      <DEU>True</DEU>
      <GRC>True</GRC>
      <GTM>True</GTM>
      <HND>True</HND>
      <HKG>True</HKG>
      <HUN>True</HUN>
      <ISL>True</ISL>
      <IND>True</IND>
      <IDN>True</IDN>
      <IRN>True</IRN>
      <IRQ>True</IRQ>
      <IRL>True</IRL>
      <ISR>True</ISR>
      <ITA>True</ITA>
      <JAM>True</JAM>
      <JPN>True</JPN>
      <JOR>True</JOR>
      <KAZ>True</KAZ>
      <KEN>True</KEN>
      <PRK>True</PRK>
      <KOR>True</KOR>
      <KWT>True</KWT>
      <KGZ>True</KGZ>
      <LVA>True</LVA>
      <LBN>True</LBN>
      <LBR>True</LBR>
      <LIE>True</LIE>
      <LTU>True</LTU>
      <LUX>True</LUX>
      <MAC>True</MAC>
      <MKD>True</MKD>
      <MYS>True</MYS>
      <MEX>True</MEX>
      <MDA>True</MDA>
      <MCO>True</MCO>
      <MNG>True</MNG>
      <MAR>True</MAR>
      <NLD>True</NLD>
      <NZL>True</NZL>
      <NIC>True</NIC>
      <NOR>True</NOR>
      <OMN>True</OMN>
      <PAK>True</PAK>
      <PAN>True</PAN>
      <PRY>True</PRY>
      <PER>True</PER>
      <PHL>True</PHL>
      <POL>True</POL>
      <PRT>True</PRT>
      <PRI>True</PRI>
      <QAT>True</QAT>
      <ROU>True</ROU>
      <RUS>True</RUS>
      <SAU>True</SAU>
      <SCG>True</SCG>
      <SGP>True</SGP>
      <SVK>True</SVK>
      <SVN>True</SVN>
      <ZAF>True</ZAF>
      <ESP>True</ESP>
      <SWE>True</SWE>
      <CHE>True</CHE>
      <TJK>True</TJK>
      <THA>True</THA>
      <TTO>True</TTO>
      <TUN>True</TUN>
      <TUR>True</TUR>
      <UKR>True</UKR>
      <ARE>True</ARE>
      <GBR>True</GBR>
      <USA>True</USA>
      <URY>True</URY>
      <UZB>True</UZB>
      <VEN>True</VEN>
      <VNM>True</VNM>
      <YEM>True</YEM>
      <ZWE>True</ZWE>
      <Others>True</Others>
    </LockTrialToUserLanguage>
    <CountOfExecutionLimit>
      <Enabled>False</Enabled>
      <MaximalCountOfExecutions>0</MaximalCountOfExecutions>
      <TerminateAfterDelay>True</TerminateAfterDelay>
      <Delay>0</Delay>
      <ShowMessageBeforeTermination>True</ShowMessageBeforeTermination>
      <Message>
        <Title>Trial has expired!</Title>
        <Text>Sorry, but your trial period has expired!
You have exceeded %TrialExecsTotal% trial executions!
Please, register this software to remove trial limitations!</Text>
        <UseHL>True</UseHL>
        <HLSentence>Click here for registration</HLSentence>
        <HLURL>http://www.yourcompany.com/</HLURL>
      </Message>
    </CountOfExecutionLimit>
    <CountOfDaysLimit>
      <Enabled>False</Enabled>
      <MaximalCountOfDays>0</MaximalCountOfDays>
      <TerminateAfterDelay>True</TerminateAfterDelay>
      <Delay>0</Delay>
      <ShowMessageBeforeTermination>True</ShowMessageBeforeTermination>
      <Message>
        <Title>Trial has expired!</Title>
        <Text>Sorry, but your trial period has expired!
You have exceeded %TrialDaysTotal% trial days!
Please, register this software to remove trial limitations!</Text>
        <UseHL>True</UseHL>
        <HLSentence>Click here for registration</HLSentence>
        <HLURL>http://www.yourcompany.com/</HLURL>
      </Message>
    </CountOfDaysLimit>
    <ExpirationDateLimit>
      <Enabled>False</Enabled>
      <ExpirationDate>2007-12-19T23:59:30.629Z</ExpirationDate>
      <TerminateAfterDelay>True</TerminateAfterDelay>
      <Delay>0</Delay>
      <ShowMessageBeforeTermination>True</ShowMessageBeforeTermination>
      <Message>
        <Title>Trial has expired!</Title>
        <Text>Sorry, but your trial period has expired!
The expiration date is %TrialExpDay%/%TrialExpMonth%/%TrialExpYear% (dd/mm/yyyy)
Please, register this software to remove trial limitations!</Text>
        <UseHL>True</UseHL>
        <HLSentence>Click here for registration</HLSentence>
        <HLURL>http://www.yourcompany.com/</HLURL>
      </Message>
    </ExpirationDateLimit>
    <ExpirationFromDateTillDate>
      <Enabled>False</Enabled>
      <StartDate>2007-12-19T23:59:30.629Z</StartDate>
      <EndDate>2007-12-19T23:59:30.629Z</EndDate>
      <TerminateAfterDelay>True</TerminateAfterDelay>
      <Delay>0</Delay>
      <ShowMessageBeforeTermination>True</ShowMessageBeforeTermination>
      <Message>
        <Title>Trial has expired!</Title>
        <Text>Sorry, but your trial period has expired!
You can use it from %TrialStartDay%/%TrialStartMonth%/%TrialStartYear% till %TrialEndDay%/%TrialEndMonth%/%TrialEndYear% (dd/mm/yyyy)
Please, register this software to remove trial limitations!</Text>
        <UseHL>True</UseHL>
        <HLSentence>Click here for registration</HLSentence>
        <HLURL>http://www.yourcompany.com/</HLURL>
      </Message>
    </ExpirationFromDateTillDate>
    <ExecutionTimeLimit>
      <Enabled>False</Enabled>
      <NumberOfMinutes>0</NumberOfMinutes>
      <TerminateOnExpiration>True</TerminateOnExpiration>
      <ShowMessageBeforeTermination>True</ShowMessageBeforeTermination>
      <Message>
        <Title>Trial has expired!</Title>
        <Text>Sorry, but your trial execution time has expired! 
Application works only %TrialExecMinsTotal% minutes since start.
Please, register this software to remove trial limitations!</Text>
        <UseHL>True</UseHL>
        <HLSentence>Register it now!</HLSentence>
        <HLURL>http://www.yourcompany.com/</HLURL>
      </Message>
    </ExecutionTimeLimit>
    <Reminder>
      <Enabled>False</Enabled>
      <PeriodOfIterations>0</PeriodOfIterations>
      <Message>
        <Title>Shareware reminder!</Title>
        <Text>This program is shareware - you get to try it before you buy it. 
If you want to continue using it after the trial 
period you will have to buy it.
Please register to remove this nag-screen!
</Text>
        <UseHL>True</UseHL>
        <HLSentence>Click here for registration</HLSentence>
        <HLURL>http://www.yourcompany.com/</HLURL>
      </Message>
    </Reminder>
    <TimeControl>
      <TerminateOnClockReversing>False</TerminateOnClockReversing>
      <ShowMessageBeforeTermination>True</ShowMessageBeforeTermination>
      <Message>
        <Title>Clock reversing</Title>
        <Text>The software had determined that the system clock
had been turned back. Please set the correct date. 
This program will now close.</Text>
        <UseHL>True</UseHL>
        <HLSentence>Click here for registration</HLSentence>
        <HLURL>http://www.yourcompany.com/</HLURL>
      </Message>
    </TimeControl>
  </TrialControl>
</EnigmaProject>
