//---------------------------------------------------------------------------

#include <vcl.h>
#pragma hdrstop

#include "MainUnit.h"

#include "../../../EnigmaSDK/Bcb/enigma_keygen_ide.h"
#pragma link "../../../EnigmaSDK/Bcb/keygen.lib"

//---------------------------------------------------------------------------
#pragma package(smart_init)
#pragma link "CSPIN"
#pragma resource "*.dfm"
TfrmMain *frmMain;
//---------------------------------------------------------------------------
__fastcall TfrmMain::TfrmMain(TComponent* Owner)
        : TForm(Owner)
{
}
//---------------------------------------------------------------------------
void __fastcall TfrmMain::cbHardwareClick(TObject *Sender)
{
  eHardware->Enabled = cbHardware->Checked;
}
//---------------------------------------------------------------------------

void __fastcall TfrmMain::FormShow(TObject *Sender)
{
  for (int i = 0; i < ARRAYSIZE(KEY_COUNTRIES); i++)
  {
    comCountry->Items->Add(KEY_COUNTRIES[i].Name);
  }
  cbHardware->OnClick(cbHardware);
  cbExpiration->OnClick(cbExpiration);
  cbKeyMode->ItemIndex = 0;
  cbKeyBase->ItemIndex = 3;
  comCountry->ItemIndex = 0;
}
//---------------------------------------------------------------------------

void __fastcall TfrmMain::cbExpirationClick(TObject *Sender)
{
  dtpExpiration->Enabled = cbExpiration->Checked;
}
//---------------------------------------------------------------------------

void __fastcall TfrmMain::bCloseClick(TObject *Sender)
{
  Application->Terminate();
}
//---------------------------------------------------------------------------

void __fastcall TfrmMain::bGenClick(TObject *Sender)
{
  TKeyGenParams kg;
  char keybuffer[2048];
  WORD wYear;
  WORD wDay;
  WORD wMonth;
  DWORD dwResult;

  mKey->Clear();

  if (eUserInfo->Text == "")
  {
    ShowMessage("Registration info is empty!");
    return;
  }

  // Clear params buffer
  memset(&kg, 0, sizeof(kg));
  // Key mode
  kg.KeyMode = cbKeyMode->ItemIndex;
  // Key base
  kg.KeyBase = cbKeyBase->ItemIndex;
  // Hyphens
  kg.KeyWithHyphens = cbHyphens->Checked;

  AnsiString asPrivateKey;
  AnsiString asPublicKey;
  switch (kg.KeyMode)
  {
    case RM_512:
    {
      asPublicKey  =  "0201B810DA4A1ADD4351378790A98138533067CP4S86R7D8THS45GBCVUM635EPRQRMYRP3DAA5DUPZ6ABDSFP7F5AC";
      asPublicKey +=  "P7ERGH4A7Y6B6NW6NMMBZF83WVER9Y4MMBNLBQDKR7KFVLGLV067CFDQC";
      asPublicKey +=  "WCHGQVVRN24DECEPBL96YJQJTVDCRTNQG3E4WW4GK4GQ5X5L5H88D3XYH";
      asPublicKey +=  "CBRBNASPD3P5CNYFKFHBCSDHHD6WPTCC4XVSM5S88067C2JSTCMVT48C8";
      asPublicKey +=  "HC7SHKGTFJBM28P6XTBCNWHMV6J6KN6W5Q9TQLVR285U6GVCAAUTZLRTP";
      asPublicKey +=  "SRGDQ742B4742XF4MACRR747YDP5FZZ9D";
      asPrivateKey =  "00C98B2SF9UBJA605AJX53GJFXJV8UH4A6PY2L6CV4MAMV7V3ERRVY99Y";
      asPrivateKey += "72V2P77Z2J3KBPGWR3WXKG5GF9Z6CKXJHY5VUMBTQ66H2MRZPCU00DLFJ";
      asPrivateKey += "675JTTTNEK00DLFJ675JTTTNEK";
      break;
    }
    case RM_768:
    {
      asPublicKey  =   "0201B810DA4A1ADD4351378790A9813853309AEHRR66KMK99UEPX8C54WQFDZQ5Y96WUCVZYMXBC32SJV7T5SBPR54E";
      asPublicKey  +=  "2DBH8UD9RNHTJ9BLJV6NHX52W7PZDDTTJDDGF3YFVL7PJFL8RS8THTFDF";
      asPublicKey  +=  "4RY7K3QXLTMXMC8DDSRVRWBVMMP3UYKCDDC7KGL9PG509ACPTRQ4MMUKG";
      asPublicKey  +=  "DAUJFFFGSCBFLYK9X9E74M58PW5FG9B9RTLXJQ4STPVCHJTEH8BP557VZ";
      asPublicKey  +=  "PQYDBUZJXXJNGVZELFL4UL3LHHE8A645CK593DVWCQBSX4ZCCQ3ST8GCZ";
      asPublicKey  +=  "LW2729XK3NT7GST3LMQGXSHLDWCXE09AENVX2QTX3VLT6FWB4JND875XZ";
      asPublicKey  +=  "WQ26GW76TV3KVVHK7U489LLPBQ39HUWKMA65U3HKWBDHKBZEB525PHEH2";
      asPublicKey  +=  "YQHRZGNJAAYZXMWN8RNZ9T7VMZAJMP2V3S9NXZ9RHL96F7AT3NTMN36V4";
      asPublicKey  +=  "ACSFEN4DR6TJR95";
      asPrivateKey =  "019VRB95TKY456YC48LBS9FK34YN080VVPDJPPKU6XA9EPLX7HTVN3VST";
      asPrivateKey += "TNJZFPH4HQAFY58R593KFKSNZ7WEZBA9AZ6PGN735EWEFUAEM3G8XVUFN";
      asPrivateKey += "L2ZFRPXA4V8KVXE4M8GACUJAT7H3TTWZT9ZDE43ZF46HC01AHFR85MVR2";
      asPrivateKey += "UP7K3N53M8MTNWBUP01AHFR85MVR2UP7K3N53M8MTNWBUP";
      break;
    }
    case RM_1024:
    {
      asPublicKey  =   "0201B810DA4A1ADD4351378790A981385330CDG9YQSEUK2HT546KWW46NHSCHMD2TNCSPVNSKGQC6CHDUSU9CC4LDPL";
      asPublicKey  +=  "TD3NLRVD3TFV9BU2SYBUBU9TXKLQ3CTKZE5GN9VFG37R9YNK2FE58K7ZY";
      asPublicKey  +=  "WTUJYFSJTBMDLLGA9GJ2LJXGHSS9CESGC2K69H65YQGAC7CRGF5T3CPLA";
      asPublicKey  +=  "5BDJZKVCUWPZRNA2DN7L956LZVZBQUDPX6PF30CDBH26Z82VE4QQ8N85L";
      asPublicKey  +=  "QHYJYYCJ6WQ89H9K9VVQX925SG5FJ79AGVZB6M8TTFKCJ56FAREP8GSTC";
      asPublicKey  +=  "NKWCXEALDP9Y2ZLS9QKZV8JRNDHG9D8BRDSCTMDQK9NLAPZ9TGYFS9G7U";
      asPublicKey  +=  "LXHYJF7F9A89HPXGTU2N4FQQJL98WZ5QND2KTEMMUUZFAX953HCGNSWUW";
      asPublicKey  +=  "U2M7LBEWZSH9GW5SC0CDJDY2HC4HJYUSVTAVXZ7MYVJWF3CU9WC3SVPSL";
      asPublicKey  +=  "29WF46DVS2SYKSSAMATPPYGJDE3PBFMGS34CXWQSXKCEAE63L2EXTS9NE";
      asPublicKey  +=  "2G597XASY3S34XMYST5YZF4THUTYT5DY9UKKXU6PH8MLXRPZMPMMR83JU";
      asPublicKey  +=  "X2WGNEW8ZGL4SKUCUB8CEAB6UZ4FB66WTP3B64VMX7XG75T4Y35479";
      asPrivateKey =  "020MLJ7XTRVLBDNUVMKGUFHCU8XBGMHHVRA0ADSHPBC7NVSZ82AM867XC";
      asPrivateKey += "BELNKMR7CZ56C4SW8KKRD9WZLE5X992GZVSZXS5J6JG9SLDW3Y696TU4D";
      asPrivateKey += "QZP778LVFKVRKANQD6FUGN2ACWUJM9ZK6ME7WXWDZ88LTWHME52RKUDKX";
      asPrivateKey += "STT7RGWP7HFUJDGWEUG4ZHQ25DCQJ644CLEXRR3G020SAAREJB4WXZ4H2";
      asPrivateKey += "JEH4AB4U8FGFTA4CAF020SAAREJB4WXZ4H2JEH4AB4U8FGFTA4CAF";
      break;
    }
    case RM_2048:
    {
      asPublicKey  =   "0201B810DA4A1ADD4351378790A98138533199H9KZC4CQ5ZKPEGYDW2EHN5AKZA5G5KP4HU8USWJR8XWW7MCH7244RV";
      asPublicKey  +=  "PSXFHVU6YQRK4LPUHM497XJYPLLA3DHUZWXK3CB5SUYF75NPYLTNPRUAP";
      asPublicKey  +=  "CTVDHAKYWTRQ97HES2E59WDN2LJKFYA2K8BGFSBHP4A6BF5FSDTS8NWLL";
      asPublicKey  +=  "C5NEQ76NJ9BAMWXG55UPFFMRQZTU5KV2G6LSPQUJJWA9U4LJ5SCJ9M9ZQ";
      asPublicKey  +=  "Z6K7LZA9UAMJ533UQ5J3JPNQAUWJGZFRFCTGAGSBWPWRRF9HRGF9MCX55";
      asPublicKey  +=  "QRYGPYMRE6DBJ65SJDX34FVPYWWKWQ48MCKXYXPHQ23BJ42U8ZMDD8CG5";
      asPublicKey  +=  "DPHZQ5XTXUKA277XK6PKSTLJ88ZZ9EBXH62REMRZZRGNPKHYM34RW722S";
      asPublicKey  +=  "SZHMPFKY7X8UZ19ADGX3UQ7X444XH7MZXEZYW2YLFHR8YL3PTCXVSYX89";
      asPublicKey  +=  "ZXN7KW2XNBE8XS3TQRGZVF6KA5USH56NEZYH5RNWBBPVE9LD6H42RQWR5";
      asPublicKey  +=  "YVQMT5EQYQTK6YM9V8DA5BJCLGGZ2UGD6NPA2X3SNUS47F7HJBNBCWHRA";
      asPublicKey  +=  "YPPCQ5HWCJNPDHUDQMRN2P6PWJFWJ6CLVNK8HXEFLE4ENPXAFN89CRS43";
      asPublicKey  +=  "RUMVGDJQCSRB535XYCS6BZYLA9Q6HJV425F26XXHRGA7C8R8BVMELAPEH";
      asPublicKey  +=  "5DSA9KXTCV4H2RNVDFHG3YJSERZVFPWHYA823LUBENBL5U3J4LJX2KQLW";
      asPublicKey  +=  "L5XGGESPSE2FQWKSQ4NUCDLBEJPQAT2A2477DYS2XEXKLYEAKUN22622C";
      asPublicKey  +=  "RLBCAHPFNEV2FJ2H8N3JQGXJRGP19AFHGRW6SS2ZSFYK8EZAPVYL4MCP4";
      asPublicKey  +=  "6UMR64Y9T89FHWEPXBRHKWJCSNNNGPU984J5XW4BWTU6PDWPN24W8RU55";
      asPublicKey  +=  "A9C3U9H7VHS5PFURASHBT4JAEE5ZKJBSYMCMDVBLU973R72RVPA6U96A2";
      asPublicKey  +=  "LX8HMASVST9RVJ7W66V4DYWRDZZQ3673W7C7LVF68TWPLNMYEUQ8FDZRN";
      asPublicKey  +=  "P38D9BX5YA3VENETDUUUMR9U7FFP4NZM7G4Y6J6EASK5SAXXTRMZ8F3AH";
      asPublicKey  +=  "5FC54E543N739BY5KVZQM7JZWD2P2HPE7BDRMM9JFCGPBWWASTZ7UDNRV";
      asPublicKey  +=  "6AFHVMBR9AGAERE9M6W6S2ZT8PBTABPDCKBPPPV6QW9DRXZUGAHDDRRSY";
      asPublicKey  +=  "XM4YXQ2QJX6CL33N7RV8846LK8A3DFLDXWZ2NKF8V";
      asPrivateKey =  "02DB825DUDFYHA88AG49NMM742HCQEJDKQQGDR6G3GL7MCED16DU9KTVE";
      asPrivateKey += "47XQ3M92FFKX8FU9ZXT7AK7VTGGJSYQERCZBR8J2DE2CFE3VUU4W7JEQJ";
      asPrivateKey += "L49HDUXBW7SNPCV8MHXM7EQHYPQK3GSPQYV5VWESQE4AYWFZHXN2UX7Z3";
      asPrivateKey += "T3CV52XURB3YFKLUCY6CJTRACRVVDD568F6F28U74ABWCZ2ZMC32E83V3";
      asPrivateKey += "4JZZPDBF3KEYQWREETGKQT6NANCMAD4XDEXW5LCYBJ3GMDJBM3E7FNK98";
      asPrivateKey += "7JEUYEV7QNKGKNTDVMRG3G5L8NC9CNHQS8AVDMDBEGCG3DHGJUGPG2244";
      asPrivateKey += "2C9LCD8FJ2LSDMQXLHCGCLP7ZSYQT2UJ4QFGU2CH3BTT3UADZMQSBAV33";
      asPrivateKey += "B3Z2Y8Y8RRD9J754G02DJ372LR8APP7HXKB2GNPHP6T65C3RR48Q8BHGP";
      asPrivateKey += "KEZ6PX2D02DJ372LR8APP7HXKB2GNPHP6T65C3RR48Q8BHGPKEZ6PX2D";
      break;
    }
    case RM_3072:
    {
      asPublicKey  =   "0201B810DA4A1ADD4351378790A98138533267BJBN6W8SY33Y47RWJHWS385XWRV9TSVSB5EM7ECZYNSJ44ZKSD8KV4";
      asPublicKey  +=  "6Y9T6TNC6QMDW4V6DBMADU3YUKLP88TCLEUD9FTEUHGXM6T57AQ2EMB59";
      asPublicKey  +=  "UPQVNSUT7Z4EHAMDQ6N77VK2A6P847MMWJH9E7C5XX6FNF6MHGWERPWQV";
      asPublicKey  +=  "9ZLVR9Z7X5D5Z2XQL6MBM7BDCT2DEDVRBFNDDBLG6LHZ9APM48LS5TKTJ";
      asPublicKey  +=  "AGDYFDRVHEGTDH47PCLL8YJ4ZAWCB59F8BN5PZL3NQACP32G3YNBX5E3J";
      asPublicKey  +=  "7S28XLRGAX55ZML5XE6Q68EUAL38VUBJ2MZ2UGCCHM7CE6S99N44X9RMV";
      asPublicKey  +=  "7B55HULJ4KHPFYSBDUSPQTCWGS76BEQST2QCZDZ2T4FBA4YUJFF47AACE";
      asPublicKey  +=  "A4EEFESEYWN48UZCSDZAMGSU5VB9UJKR2XQUSDFS3TNG9QKURQWS7N5Q3";
      asPublicKey  +=  "VPYT4QAGGQ5GW2MMXHDJYW55FQPHSS255GVJ7WLLP85JHS8K6DVTJWB4W";
      asPublicKey  +=  "4Y8ZEFF7KP6W74PAA8FVPZVDW4RKGMQ8V56Q6R4V34XFE5HR4FZS8GZ6R";
      asPublicKey  +=  "36RKW6WN34S2AHR9LCGF8LULB7ZN6NC2AA5RPDZYGWSPPME6267CGLZCP";
      asPublicKey  +=  "GG98W557RXCDWAETJHDCRPUXX7AD3E8SL9WMGBV9QFHMSV9UFV7H9NTX3";
      asPublicKey  +=  "QYV95CN9P9DSYQWL35R2BWB4GKW6CTWVB2X6WH3P2WFBE6X55KBLAMDB3";
      asPublicKey  +=  "SRDSDVY37GKH78MUHY7KDJDC8SJU5FUQRYZ49ULX3YSZL7E284GFVZSGD";
      asPublicKey  +=  "FTTQNR6VMBM3LLR78SUSVMJW88XT2K4UHEATRU3V9MMR2VGNGCY9S6R7M";
      asPublicKey  +=  "4ELNPEBBMQP2HVPLY5M5VPA92V7NU8ZZ58YYTW5QK9USZ5LV59UZGB2CF";
      asPublicKey  +=  "DPZ6HFYU5WB7UU3UYWVNSNKM3W7GBYKJP8TMFW2YN6GNSSE4ZKFVZRLUT";
      asPublicKey  +=  "8QPZBBEQT5H9H5Z53D7QV4AJTKTX4EMW62TDWXQVK24JZPSG2VXKCMYZL";
      asPublicKey  +=  "BUASE25R3EEK9KBLJ8VX83ZXGTBL5CCDBY33HJ6FKX8AW3WPRX3XTCS9G";
      asPublicKey  +=  "2B9SVK2XFFKNSPGFG6FZNS8P2HERFGFL79DKUZZFWHFH4H9UZ7F7F6W3Z";
      asPublicKey  +=  "WN84JRRA7SU8T89EQMRBEPVYMMGLGSUMFWWMEDLJM5EGP5WEX9LDUAQXS";
      asPublicKey  +=  "55LKDQVRKX4R8NNBHSYBHSNMFYA2C6KGQQFK3TN267DPSZT3PTKHTLNMU";
      asPublicKey  +=  "V2YYZZRX3953LPWAKYF8LJUVMEQT3ZC4F9SWMYGUHG6XX66PJFQWSRUW2";
      asPublicKey  +=  "C79YLGZ6ZAFS7HLXBTH55UD3EGFBZQ5CLNJDZTEJACDVBCM5FQBTX4DKK";
      asPublicKey  +=  "UDGNSMWWLC5UMQRHW89CTU5636WYDXVZZDAUZ529458LDN2DAR9QADWRZ";
      asPublicKey  +=  "UTU8MCKN8ACH3R36YKE52DDF67SM5UDUH7PTATHXE65UCGFRCV49UTF29";
      asPublicKey  +=  "BGDSUWGRMC839HUZYQVAG985YUF5KJCAVGKRD8E4YMPJ38KR6HD2KHDGY";
      asPublicKey  +=  "PJDMJMN4Y3DTJW5MYX3VUPF32637LMAWFM4QCU7BMP74SCF4N5QXZZVBE";
      asPublicKey  +=  "LAQMPYMJC2ZWJ9XFV33YXTTPH3SG9KMUABLL83P7L3UBVM763XJPR5UEH";
      asPublicKey  +=  "DZ73XJUGR9QP6299K5PU46XGVWKWFAKWYMRXUSLCFJ3WQTEC6QJH6E5V8";
      asPublicKey  +=  "X2RMA5TXGSBEWLGSMNQ2SH6G9MLKK4S486DN4M4QVDTYTM4JQETAJQGP7";
      asPublicKey  +=  "6NUR4UNDXR4W4JQSNWDTUGX69SEDC3AL8ZF4TJBQQRXSX2A86UUWCDCFH";
      asPublicKey  +=  "JE94A8EP3Q6A2QTYKEE62MV4VJB2AB";
      asPrivateKey =  "04DMQBFL5GKK4RXWEZYPEY8QB5GHBBGW3EY766DFF2Q6PLFLR4SSRWXBN";
      asPrivateKey += "W5CFGFC84UJ8PKRDQQYWN9921AH8AGZAQRWJYZ7C3QUJJCJAAGGT5P5GG";
      asPrivateKey += "LY6K5W4JZJDW7Y8EWS6SSYCS2MCR93MY9DG6VXLBRYVZWDX6XNFU286HM";
      asPrivateKey += "AM9E82KJVZNYGC8RXRW43SFFWDLR8C96R58A42F3TGLXPVWLGMNPPAWS4";
      asPrivateKey += "S2SSMNQLUPBAQMP7AUKAGV5CKL342W6UKQY3TUPXKGFXFES725PEX93UX";
      asPrivateKey += "X4K8JRY3MPQ5S94VNPBXQ5N6LDBLWB67LBLDCGNQS3JEKW667UF8LA8Q6";
      asPrivateKey += "B3AF4XGZKYB7BVVHF49DJUHSFJZRD63S4GMMAPQ8Q87HKN9G3J3575JEL";
      asPrivateKey += "5C29NZLWQW6MSYWJ7S3NKLKPWGU75ZJXKEL6Z9V97HAMDYHM6ARCH58EP";
      asPrivateKey += "WRPZUXN8RRWGCDFM6BH6XUTRTFE3VMAAZAUUKHKL6AFC2PCPXFRDWLVYP";
      asPrivateKey += "ZKUGY8QDEZNECK9K5Y5MET9SRWQ36JF77XJ6NUKSMZ6DKUPEH4W43YQAE";
      asPrivateKey += "CV7XVFWJ7ZTRVFQJZFU5BS8AL77YLCXDCR5VPFFZ9NP7EBBQ2AA04DP3S";
      asPrivateKey += "86B3YD8AFXQ3KPPNTPHBTQSM8PUHJHG87RUM4EV63GKPBRGSN3VX63FCS";
      asPrivateKey += "646DLWSH86B542U3F04DP3S86B3YD8AFXQ3KPPNTPHBTQSM8PUHJHG87R";
      asPrivateKey += "UM4EV63GKPBRGSN3VX63FCS646DLWSH86B542U3F";
      break;
    }
    case RM_4096:
    {
      asPublicKey  =   "0201B810DA4A1ADD4351378790A98138533334BN75EUD4LFWYFVNFGBRCEMKKXG62QRBXL8GMHQ975B68FJVEMNXQQ7";
      asPublicKey  +=  "RKR7RCZMWDVP86QPB3LR49G9BM7EUUZXNCRZBXRF4PEY3RX6UXT4X286K";
      asPublicKey  +=  "WMR7AMEE6QNP9AEHGWJT9AVU3F62F37B5GVX2BQF73QWHRFQSQ6XMRP6M";
      asPublicKey  +=  "ZDRQ8JT89UF3YRK5UL34MM3L8HLDTWAJF5YY7MRWJUFDQRK9JNCJ6C9KJ";
      asPublicKey  +=  "WRE5ACQSNS24NEAHDYKS3AK75HBCTKP9NR2CSL7Q9PPXM4TF7L37USP77";
      asPublicKey  +=  "Z7WER6JY67F7YGKTP6MFRMKK9H8XFPZ9XWKK97XCFXK7AW4TB96WGEH4B";
      asPublicKey  +=  "YPF9QRF2DGC3LCHW8VTD2EMHVRDR6EEBUG8R4XYKJPLBPRCJP445DU2BW";
      asPublicKey  +=  "JWELKAY45GKDU5HLTTDTWRH2JFKJ7AVAKLPEELN29YHX5JR773P9ERW7N";
      asPublicKey  +=  "UFMWDKD4UL4YPU5QW2L4A7ZN3E8ZDQVEZUR6ZRDQXRLQRLUNHVB4XGBWD";
      asPublicKey  +=  "HM79NGJARHDBQJCD8NWZCWPBY26QARP7A5S48DCV29F7MUTHU3YAHYVS7";
      asPublicKey  +=  "ZPGXJZZA5HBBY4K5BQPUG6MVCBG8H3C7VL8KH9EH58NUVCE6F2CGMSM94";
      asPublicKey  +=  "4CQDKNVGWWWRNK4DG26UT88Y9ULUPJ25G9KLTQPK3TBXZEK43X8KPMJT8";
      asPublicKey  +=  "DF6MTQ5RB94XR2BT9H9NYYTXL29GF8KZY9QNHAW4LTT2RDQTD2ELWPSV4";
      asPublicKey  +=  "4AND9K8G5VKLQ287UBEVHA82CMSMGQQBMNQZVAZ9G8T5TD6G9F7XQHMJT";
      asPublicKey  +=  "HCL6PFEDUW5MN9JASF2EKPT2H33325A3SFGGM5C3RYKZSYSSQC5FLHNX9";
      asPublicKey  +=  "VHN4HQVWD66YECY4R97JZHQALRTCHXUWH85LSJCCTQZT8JF52FHG6FV9J";
      asPublicKey  +=  "UJWEU24K9Z7SKYTYA9NH7GW29FSNKUK3DMFXY48VEYZVDRTTMLFYSJH8B";
      asPublicKey  +=  "RQXV72REYGSM6SLX89EVQ52CN9N8YDLWHAWCDDNM89YN2FCRTLE8688XN";
      asPublicKey  +=  "F4MZMFE6GP9JK2JTFJVUKBLES2ZVWSBCJE9N3D7DG9LL9Z3U42QFJJPE2";
      asPublicKey  +=  "HE5XCRY25UND96R9D7655ZBSCPF796FJZ4GPLY7AMZLGLR77GRP467ELK";
      asPublicKey  +=  "PXSZ9ZF8MUYFDHV4EFACPZ79CDFTYW56NE9JDWDVUMNLY9EXDSF8ZNABE";
      asPublicKey  +=  "MDQU6T9RVLTNRMZJJ8EJSUM9TV8YA6MNWF2Y66MD3VQ9L662F5X6VE9ZC";
      asPublicKey  +=  "PPRTNMEV2DG9BUMFMSL7PUR9H5QG6VVBDBMEU3PLABL57HMSQ5C83E9X8";
      asPublicKey  +=  "RTPY4QSEH2H7R2QDJHT5LQSFNQJL2NZ24GLNTFXG468WC5UUTDD68G8HM";
      asPublicKey  +=  "477Z8P4F9ZZHUGZ6VPFF32A2MPWY8LPXNECCKM9EEVLDDPUJ8LXN2AE6K";
      asPublicKey  +=  "Y2Q42FVBCJM9GQTXNRHBED735KCFD4YGV4U5U3LJD6NJ886W6XQLWH3G7";
      asPublicKey  +=  "3VZ6T5YDUQP6DA5ZHLKQSGATDJHUWC2ELTE7QJFXH8H9EAQYZPPMW76JG";
      asPublicKey  +=  "Z48UBND9VXFXZMSGBNWQPNQG7E4TCBDMVKUSA9TTVSNLN3DXEZNXH787R";
      asPublicKey  +=  "N7XHFBJNZ5W74DUMNBYVHEQDYMZBQCFMS8LGSDUAU6QE9DD5Q334BX62T";
      asPublicKey  +=  "JP5WQQDS2ZEWW76XW7VMLX3L7V9UVD7RL297VDBVRFPQ5MYA3YFVBVNVA";
      asPublicKey  +=  "R5TJXMQ4X2U2PW6UWSKBXT58HG3QPZM2RVEX2PDMCVJDFTJMXR9NPLW4C";
      asPublicKey  +=  "6AE4Q9A8TTAQ68BSN9WWTFFTSX68ECVARFPDBBH5DMP4KYFARTFVCSWNF";
      asPublicKey  +=  "FGAPBYYGFJA4MGHEDWTNE54MPGER7EPCZEV2ESJRMM6XDMFGABG97HECN";
      asPublicKey  +=  "3SZFUBRKCNDZ2TE9F6EERULRCDAV5EMY6KVY9NY9MYNPP2FQ5WFWLMRYD";
      asPublicKey  +=  "4TNYYHE6EDMQ6KK79HH5Q5KBW6DDQYYNUR365W2454CHEZWZAPU82SX4H";
      asPublicKey  +=  "KHNZXBRVWAT7G9CJBKHNBPSVEL67X8UJHBFHZH8ZD4NSEVFZM4Y768TJ6";
      asPublicKey  +=  "QP3BGEKLTY4UZUWHG63QMKVWAQW9E7WRFNJCH4XRD97JHNKMFUTRFVH68";
      asPublicKey  +=  "G5CKSMGC725AZ9CBSL2JX43P4EY44CMF56AJ6BEXVCJP2ZDWCT9G55K4D";
      asPublicKey  +=  "F24J7TTJMFXTQH95CBNX67CBPNWEKHCWQV6VD73Y9KCZHNBFUFS8TCYNN";
      asPublicKey  +=  "WXRGFKNVRDFUKWHDWDDTSPEW8YCKLPMX2GUV3N4JFJMUE6TSJR6Z53C9F";
      asPublicKey  +=  "SKZAY3EN7G66JPBF5E9S6Y7RQHPWZHSRDQ7XH87PHUJFLJFGD7WNE3Q9P";
      asPublicKey  +=  "EXMW33LVJBFEUXKNUB6SU7ZYCEX96XT8G27FLUV7XPV4JK64DWDJCX9WV";
      asPublicKey  +=  "4VL8CK23FQJ28UG9JUZTQJ3DRL32BTJVRD4LVQ6MK9LCE7ZZP925JK7ZD";
      asPublicKey  +=  "92SCV4DDJJ5EXMVBF";
      asPrivateKey =  "067DN6F8YFBTWF5QH8MHJB39Z7VNN2P56HS8RPBURAWB8KPL8FQWX4HSP";
      asPrivateKey += "RTY4HWFMFXAVX2YUY99G2U2KKK56CBE4BCPZWS3KJ98XRW4PY2CDQ3ALF";
      asPrivateKey += "Y3HGJG42NRY9ZVGZZB59TE29KGC825BYBX3FRFMYBVN2VTHM8YM2KULXM";
      asPrivateKey += "H24XSHNVK4HEQ9L5264844DMBFUUXSBA9PT9LEXT6449TMAMF7RL6KU6C";
      asPrivateKey += "GP6FU9DNMJCNTUREF2U9WUMJBMXMMHWKWDG4VU5JQNFFNNWCP2YE2DGFD";
      asPrivateKey += "N3H27NTNR58GBNTTZBRK66BKWNVMF8H59KALB6K93Y8UWDUWV5HJZGV8X";
      asPrivateKey += "3AS5EG982DYV4W9CQ3GRRFRR8NABQC98FTAG465SXQ3JPN7X7VULQZX4X";
      asPrivateKey += "UK68URBFPGCW2UPBYH6CRG6C7YBRAURM2KMGN5L8XH4AUQN8WM4P454WN";
      asPrivateKey += "NWRE5SQEC78FK3LHHQU5VLXLYYN8F2NXGG7CCQ92EEC63ZE6S7CC7RTML";
      asPrivateKey += "UMM3PAXPL5CJZFL26JNNYPVLAHM75NDL8AS8JR48PFXK49D4A6TY2ZU9Y";
      asPrivateKey += "XX2Y2TLA3J8T56EPY236FEDGZEHTQR73NE2Y7LKL7423FFKGGPWYL8PB5";
      asPrivateKey += "VWD54PG328ACE6V8VLSGVP9VJMMWS2VDVU459C8K5XK869Z5KM4JWHAVE";
      asPrivateKey += "G56ZESC6HHJ423N6SAH2DW2UQZDQHSMKWUCHPUKZZPJXCQXF55Q3BJW6Z";
      asPrivateKey += "QYK4HM4Q8XWU8NT844E8YM96EGHSYEUKAJ3QDXANN2DELSGARH7LR2XH9";
      asPrivateKey += "UQBQNXGPZGB6JTEQP8HAQK5ZZYJE067DWX6C2JJ7L2HE4NGVXZV5XH9RD";
      asPrivateKey += "P9SNG4WZTC9KEMGXDX7JTQX8RPJJRT4YTA83L8GXDNDDHB7C79CN8UWGL";
      asPrivateKey += "U5EMNMBREQN5MYTYHGYK067DWX6C2JJ7L2HE4NGVXZV5XH9RDP9SNG4WZ";
      asPrivateKey += "TC9KEMGXDX7JTQX8RPJJRT4YTA83L8GXDNDDHB7C79CN8UWGLU5EMNMBR";
      asPrivateKey += "EQN5MYTYHGYK";
      break;
    }
  }
  kg.PrivateKey = asPrivateKey.c_str();
  kg.PublicKey = asPublicKey.c_str();

  // Fill key buffer
  memset(&keybuffer, 0, sizeof(keybuffer));
  kg.Key = (char*) keybuffer;
  kg.KeyLen = sizeof(keybuffer);

  // Fill User Info
  char namebuf[255];
  memset(&namebuf, 0, sizeof(namebuf));
  memcpy(&namebuf, eUserInfo->Text.c_str(), eUserInfo->Text.Length());
  kg.RegInfo = namebuf;
  kg.RegInfoLen = eUserInfo->Text.Length();

  // Hardware ID
  kg.UseHardwareLocking = cbHardware->Checked;
  if (kg.UseHardwareLocking)
  {
    char hdbuf[255];
    memset(&hdbuf, 0, sizeof(hdbuf));
    memcpy(&hdbuf, eHardware->Text.c_str(), eHardware->Text.Length());
    kg.HardwareID = hdbuf;
  }

  // Key expiration info
  kg.UseKeyExpiration = cbExpiration->Checked;
  if (kg.UseKeyExpiration)
  {
    DecodeDate(dtpExpiration->DateTime, wYear, wMonth, wDay);
    kg.ExpirationYear = wYear;
    kg.ExpirationMonth = wMonth;
    kg.ExpirationDay = wDay;
  }

  // Register After
  kg.UseRegisterAfter = cbRegisterAfter->Checked;
  if (kg.UseRegisterAfter)
  {
    DecodeDate(dtpRegisterAfter->DateTime, wYear, wMonth, wDay);
    kg.RegisterAfterYear = wYear;
    kg.RegisterAfterMonth = wMonth;
    kg.RegisterAfterDay = wDay;
  }

  // Register Before
  kg.UseRegisterBefore = cbRegisterBefore->Checked;
  if (kg.UseRegisterBefore)
  {
    DecodeDate(dtpRegisterBefore->DateTime, wYear, wMonth, wDay);
    kg.RegisterBeforeYear = wYear;
    kg.RegisterBeforeMonth = wMonth;
    kg.RegisterBeforeDay = wDay;
  }

  // Executions
  kg.UseExecutionsLimit = cbExecutions->Checked;
  if (kg.UseExecutionsLimit)
  {
    kg.ExecutionsCount = seExecutions->Value;
  }

  // Days
  kg.UseDaysLimit = cbDays->Checked;
  if (kg.UseDaysLimit)
  {
    kg.DaysCount = seDays->Value;
  }

  // Runtime
  kg.UseRunTimeLimit = cbRuntime->Checked;
  if (kg.UseRunTimeLimit)
  {
    kg.RunTimeMinutes = seRuntime->Value;
  }

  // Global Time
  kg.UseGlobalTimeLimit = cbGlobalTime->Checked;
  if (kg.UseGlobalTimeLimit)
  {
    kg.GlobalTimeMinutes = seGlobalTime->Value;
  }

  // Country
  kg.UseCountyLimit = cbCountry->Checked;
  if (kg.UseCountyLimit)
  {
    kg.CountryCode = KEY_COUNTRIES[comCountry->ItemIndex].Code;
  }

  // Crypt Sections
  kg.EncryptedSections[0] = cbSection1->Checked;
  kg.EncryptedSections[1] = cbSection2->Checked;
  kg.EncryptedSections[2] = cbSection3->Checked;
  kg.EncryptedSections[3] = cbSection4->Checked;
  kg.EncryptedSections[4] = cbSection5->Checked;
  kg.EncryptedSections[5] = cbSection6->Checked;
  kg.EncryptedSections[6] = cbSection7->Checked;
  kg.EncryptedSections[7] = cbSection8->Checked;
  kg.EncryptedSections[8] = cbSection9->Checked;
  kg.EncryptedSections[9] = cbSection10->Checked;
  kg.EncryptedSections[10] = cbSection11->Checked;
  kg.EncryptedSections[11] = cbSection12->Checked;
  kg.EncryptedSections[12] = cbSection13->Checked;
  kg.EncryptedSections[13] = cbSection14->Checked;
  kg.EncryptedSections[14] = cbSection15->Checked;
  kg.EncryptedSections[15] = cbSection16->Checked;

  // Secure constant (get it from project file)!
  kg.EncryptedConstant = 2113444489;

  dwResult = KG_GenerateRegistrationKey(&kg);

  if (dwResult == EP_NO_ERROR)
  {
    mKey->Text = (AnsiString)kg.Key;
  } else
  {
    switch (dwResult)
    {
      case EP_ERROR_UNKNOWN:
        mKey->Text = "EP_ERROR_UNKNOWN";
        break;
      case EP_ERROR_KEYBUFFEREMPTY:
        mKey->Text = "EP_ERROR_KEYBUFFEREMPTY";
        break;
      case EP_ERROR_KEYBUFFERISLESS:
        mKey->Text = "EP_ERROR_KEYBUFFERISLESS";
        break;
      case EP_ERROR_REGINFOEMPTY:
        mKey->Text = "EP_ERROR_REGINFOEMPTY";
        break;
      case EP_ERROR_REGINFOTOOLARGE:
        mKey->Text = "EP_ERROR_REGINFOTOOLARGE";
        break;
      case EP_ERROR_PRIVATEKEYISNOTSET:
        mKey->Text = "EP_ERROR_PRIVATEKEYISNOTSET";
        break;
      case EP_ERROR_PUBLICKEYISNOTSET:
        mKey->Text = "EP_ERROR_PUBLICKEYISNOTSET";
        break;
      case EP_ERROR_PRIVATEKEYISINVALID:
        mKey->Text = "EP_ERROR_PRIVATEKEYISINVALID";
        break;
      case EP_ERROR_PUBLICKEYISINVALID:
        mKey->Text = "EP_ERROR_PUBLICKEYISINVALID";
        break;
      case EP_ERROR_KEYMODEISINVALID:
        mKey->Text = "EP_ERROR_KEYMODEISINVALID";
        break;
      case EP_ERROR_KEYBASEISINVALID:
        mKey->Text = "EP_ERROR_KEYBASEISINVALID";
        break;
      case EP_ERROR_CURRENTDATEISINVALID:
        mKey->Text = "EP_ERROR_CURRENTDATEISINVALID";
        break;
      case EP_ERROR_EXPIRATIONDATEISINVALID:
        mKey->Text = "EP_ERROR_EXPIRATIONDATEISINVALID";
        break;
      case EP_ERROR_KEYISINVALID:
        mKey->Text = "EP_ERROR_KEYISINVALID";
        break;
      case EP_ERROR_HARDWAREID:
        mKey->Text = "EP_ERROR_HARDWAREID";
        break;
      case EP_ERROR_HARDWAREBUFFEREMPTY:
        mKey->Text = "EP_ERROR_HARDWAREBUFFEREMPTY";
        break;
      case EP_ERROR_HARDWAREIDINVALIDFORKEY:
        mKey->Text = "EP_ERROR_HARDWAREIDINVALIDFORKEY";
        break;
      case EP_ERROR_PROJECTFILENOTFOUND:
        mKey->Text = "EP_ERROR_PROJECTFILENOTFOUND";
        break;
      case EP_ERROR_INVALIDPROJECTFILE:
        mKey->Text = "EP_ERROR_INVALIDPROJECTFILE";
        break;
      case EP_ERROR_EXECUTIONSNUMBERINVALID:
        mKey->Text = "EP_ERROR_EXECUTIONSNUMBERINVALID";
        break;
      case EP_ERROR_DAYSNUMBERINVALID:
        mKey->Text = "EP_ERROR_DAYSNUMBERINVALID";
        break;
      case EP_ERROR_COUNTRYCODEINVALID:
        mKey->Text = "EP_ERROR_COUNTRYCODEINVALID";
        break;
      case EP_ERROR_RUNTIMEINVALID:
        mKey->Text = "EP_ERROR_RUNTIMEINVALID";
        break;
      case EP_ERROR_GLOBALTIMEINVALID:
        mKey->Text = "EP_ERROR_GLOBALTIMEINVALID";
        break;
      case EP_ERROR_INSTALLBEFOREINVALID:
        mKey->Text = "EP_ERROR_INSTALLBEFOREINVALID";
        break;
      case EP_ERROR_INSTALLAFTERINVALID:
        mKey->Text = "EP_ERROR_INSTALLAFTERINVALID";
        break;
      default:
        mKey->Text = "Unknown error";
        break;
    }
  }
}
//---------------------------------------------------------------------------

void __fastcall TfrmMain::bGenFPClick(TObject *Sender)
{
  TKeyGenParams kg;
  char keybuffer[2048];
  WORD wYear;
  WORD wDay;
  WORD wMonth;
  DWORD dwResult;

  mKey->Clear();

  if (eUserInfo->Text == "")
  {
    ShowMessage("Registration info is empty!");
    return;
  }

  // Clear params buffer
  memset(&kg, 0, sizeof(kg));
  // Hyphens
  kg.KeyWithHyphens = cbHyphens->Checked;

  // Fill key buffer
  memset(&keybuffer, 0, sizeof(keybuffer));
  kg.Key = (char*) keybuffer;
  kg.KeyLen = sizeof(keybuffer);

  // Fill User Info
  char namebuf[255];
  memset(&namebuf, 0, sizeof(namebuf));
  memcpy(&namebuf, eUserInfo->Text.c_str(), eUserInfo->Text.Length());
  kg.RegInfo = namebuf;
  kg.RegInfoLen = eUserInfo->Text.Length();

  // Hardware ID
  kg.UseHardwareLocking = cbHardware->Checked;
  if (kg.UseHardwareLocking)
  {
    char hdbuf[255];
    memset(&hdbuf, 0, sizeof(hdbuf));
    memcpy(&hdbuf, eHardware->Text.c_str(), eHardware->Text.Length());
    kg.HardwareID = hdbuf;
  }

  // Key expiration info
  kg.UseKeyExpiration = cbExpiration->Checked;
  if (kg.UseKeyExpiration)
  {
    DecodeDate(dtpExpiration->DateTime, wYear, wMonth, wDay);
    kg.ExpirationYear = wYear;
    kg.ExpirationMonth = wMonth;
    kg.ExpirationDay = wDay;
  }

  // Register After
  kg.UseRegisterAfter = cbRegisterAfter->Checked;
  if (kg.UseRegisterAfter)
  {
    DecodeDate(dtpRegisterAfter->DateTime, wYear, wMonth, wDay);
    kg.RegisterAfterYear = wYear;
    kg.RegisterAfterMonth = wMonth;
    kg.RegisterAfterDay = wDay;
  }

  // Register Before
  kg.UseRegisterBefore = cbRegisterBefore->Checked;
  if (kg.UseRegisterBefore)
  {
    DecodeDate(dtpRegisterBefore->DateTime, wYear, wMonth, wDay);
    kg.RegisterBeforeYear = wYear;
    kg.RegisterBeforeMonth = wMonth;
    kg.RegisterBeforeDay = wDay;
  }

  // Executions
  kg.UseExecutionsLimit = cbExecutions->Checked;
  if (kg.UseExecutionsLimit)
  {
    kg.ExecutionsCount = seExecutions->Value;
  }

  // Days
  kg.UseDaysLimit = cbDays->Checked;
  if (kg.UseDaysLimit)
  {
    kg.DaysCount = seDays->Value;
  }

  // Runtime
  kg.UseRunTimeLimit = cbRuntime->Checked;
  if (kg.UseRunTimeLimit)
  {
    kg.RunTimeMinutes = seRuntime->Value;
  }

  // Global Time
  kg.UseGlobalTimeLimit = cbGlobalTime->Checked;
  if (kg.UseGlobalTimeLimit)
  {
    kg.GlobalTimeMinutes = seGlobalTime->Value;
  }

  // Country
  kg.UseCountyLimit = cbCountry->Checked;
  if (kg.UseCountyLimit)
  {
    kg.CountryCode = KEY_COUNTRIES[comCountry->ItemIndex].Code;
  }  

  // Crypt Sections
  kg.EncryptedSections[0] = cbSection1->Checked;
  kg.EncryptedSections[1] = cbSection2->Checked;
  kg.EncryptedSections[2] = cbSection3->Checked;
  kg.EncryptedSections[3] = cbSection4->Checked;
  kg.EncryptedSections[4] = cbSection5->Checked;
  kg.EncryptedSections[5] = cbSection6->Checked;
  kg.EncryptedSections[6] = cbSection7->Checked;
  kg.EncryptedSections[7] = cbSection8->Checked;
  kg.EncryptedSections[8] = cbSection9->Checked;
  kg.EncryptedSections[9] = cbSection10->Checked;
  kg.EncryptedSections[10] = cbSection11->Checked;
  kg.EncryptedSections[11] = cbSection12->Checked;
  kg.EncryptedSections[12] = cbSection13->Checked;
  kg.EncryptedSections[13] = cbSection14->Checked;
  kg.EncryptedSections[14] = cbSection15->Checked;
  kg.EncryptedSections[15] = cbSection16->Checked;

  dwResult = KG_GenerateRegistrationKeyFromProject("default.enigma", &kg);

  if (dwResult == EP_NO_ERROR)
  {
    mKey->Text = (AnsiString)kg.Key;
  } else
  {
    switch (dwResult)
    {
      case EP_ERROR_UNKNOWN:
        mKey->Text = "EP_ERROR_UNKNOWN";
        break;
      case EP_ERROR_KEYBUFFEREMPTY:
        mKey->Text = "EP_ERROR_KEYBUFFEREMPTY";
        break;
      case EP_ERROR_KEYBUFFERISLESS:
        mKey->Text = "EP_ERROR_KEYBUFFERISLESS";
        break;
      case EP_ERROR_REGINFOEMPTY:
        mKey->Text = "EP_ERROR_REGINFOEMPTY";
        break;
      case EP_ERROR_REGINFOTOOLARGE:
        mKey->Text = "EP_ERROR_REGINFOTOOLARGE";
        break;
      case EP_ERROR_PRIVATEKEYISNOTSET:
        mKey->Text = "EP_ERROR_PRIVATEKEYISNOTSET";
        break;
      case EP_ERROR_PUBLICKEYISNOTSET:
        mKey->Text = "EP_ERROR_PUBLICKEYISNOTSET";
        break;
      case EP_ERROR_PRIVATEKEYISINVALID:
        mKey->Text = "EP_ERROR_PRIVATEKEYISINVALID";
        break;
      case EP_ERROR_PUBLICKEYISINVALID:
        mKey->Text = "EP_ERROR_PUBLICKEYISINVALID";
        break;
      case EP_ERROR_KEYMODEISINVALID:
        mKey->Text = "EP_ERROR_KEYMODEISINVALID";
        break;
      case EP_ERROR_KEYBASEISINVALID:
        mKey->Text = "EP_ERROR_KEYBASEISINVALID";
        break;
      case EP_ERROR_CURRENTDATEISINVALID:
        mKey->Text = "EP_ERROR_CURRENTDATEISINVALID";
        break;
      case EP_ERROR_EXPIRATIONDATEISINVALID:
        mKey->Text = "EP_ERROR_EXPIRATIONDATEISINVALID";
        break;
      case EP_ERROR_KEYISINVALID:
        mKey->Text = "EP_ERROR_KEYISINVALID";
        break;
      case EP_ERROR_HARDWAREID:
        mKey->Text = "EP_ERROR_HARDWAREID";
        break;
      case EP_ERROR_HARDWAREBUFFEREMPTY:
        mKey->Text = "EP_ERROR_HARDWAREBUFFEREMPTY";
        break;
      case EP_ERROR_HARDWAREIDINVALIDFORKEY:
        mKey->Text = "EP_ERROR_HARDWAREIDINVALIDFORKEY";
        break;
      case EP_ERROR_PROJECTFILENOTFOUND:
        mKey->Text = "EP_ERROR_PROJECTFILENOTFOUND";
        break;
      case EP_ERROR_INVALIDPROJECTFILE:
        mKey->Text = "EP_ERROR_INVALIDPROJECTFILE";
        break;
      case EP_ERROR_EXECUTIONSNUMBERINVALID:
        mKey->Text = "EP_ERROR_EXECUTIONSNUMBERINVALID";
        break;
      case EP_ERROR_DAYSNUMBERINVALID:
        mKey->Text = "EP_ERROR_DAYSNUMBERINVALID";
        break;
      case EP_ERROR_COUNTRYCODEINVALID:
        mKey->Text = "EP_ERROR_COUNTRYCODEINVALID";
        break;
      case EP_ERROR_RUNTIMEINVALID:
        mKey->Text = "EP_ERROR_RUNTIMEINVALID";
        break;
      case EP_ERROR_GLOBALTIMEINVALID:
        mKey->Text = "EP_ERROR_GLOBALTIMEINVALID";
        break;
      case EP_ERROR_INSTALLBEFOREINVALID:
        mKey->Text = "EP_ERROR_INSTALLBEFOREINVALID";
        break;
      case EP_ERROR_INSTALLAFTERINVALID:
        mKey->Text = "EP_ERROR_INSTALLAFTERINVALID";
        break;
      default:
        mKey->Text = "Unknown error";
        break;
    }
  }
}
//---------------------------------------------------------------------------

void __fastcall TfrmMain::btnVerifyClick(TObject *Sender)
{
  if (eUserInfo->Text == "")
  {
    ShowMessage("Registration info is empty!");
    return;
  }
  if (mKey->Text == "")
  {
    ShowMessage("Registration key is empty!");
    return;
  }

  TKeyVerifyParams kv;
  WORD wYear;
  WORD wDay;
  WORD wMonth;
  DWORD dwResult;

  // Clear params buffer
  memset(&kv, 0, sizeof(kv));
  // Key mode
  kv.KeyMode = cbKeyMode->ItemIndex;
  // Key base
  kv.KeyBase = cbKeyBase->ItemIndex;

  AnsiString asPublicKey;
  switch (kv.KeyMode)
  {
    case RM_512:
    {
      asPublicKey  =  "0201B810DA4A1ADD4351378790A98138533067CP4S86R7D8THS45GBCVUM635EPRQRMYRP3DAA5DUPZ6ABDSFP7F5AC";
      asPublicKey +=  "P7ERGH4A7Y6B6NW6NMMBZF83WVER9Y4MMBNLBQDKR7KFVLGLV067CFDQC";
      asPublicKey +=  "WCHGQVVRN24DECEPBL96YJQJTVDCRTNQG3E4WW4GK4GQ5X5L5H88D3XYH";
      asPublicKey +=  "CBRBNASPD3P5CNYFKFHBCSDHHD6WPTCC4XVSM5S88067C2JSTCMVT48C8";
      asPublicKey +=  "HC7SHKGTFJBM28P6XTBCNWHMV6J6KN6W5Q9TQLVR285U6GVCAAUTZLRTP";
      asPublicKey +=  "SRGDQ742B4742XF4MACRR747YDP5FZZ9D" + 0;
      break;
    }
    case RM_768:
    {
      asPublicKey  =   "0201B810DA4A1ADD4351378790A9813853309AEHRR66KMK99UEPX8C54WQFDZQ5Y96WUCVZYMXBC32SJV7T5SBPR54E";
      asPublicKey  +=  "2DBH8UD9RNHTJ9BLJV6NHX52W7PZDDTTJDDGF3YFVL7PJFL8RS8THTFDF";
      asPublicKey  +=  "4RY7K3QXLTMXMC8DDSRVRWBVMMP3UYKCDDC7KGL9PG509ACPTRQ4MMUKG";
      asPublicKey  +=  "DAUJFFFGSCBFLYK9X9E74M58PW5FG9B9RTLXJQ4STPVCHJTEH8BP557VZ";
      asPublicKey  +=  "PQYDBUZJXXJNGVZELFL4UL3LHHE8A645CK593DVWCQBSX4ZCCQ3ST8GCZ";
      asPublicKey  +=  "LW2729XK3NT7GST3LMQGXSHLDWCXE09AENVX2QTX3VLT6FWB4JND875XZ";
      asPublicKey  +=  "WQ26GW76TV3KVVHK7U489LLPBQ39HUWKMA65U3HKWBDHKBZEB525PHEH2";
      asPublicKey  +=  "YQHRZGNJAAYZXMWN8RNZ9T7VMZAJMP2V3S9NXZ9RHL96F7AT3NTMN36V4";
      asPublicKey  +=  "ACSFEN4DR6TJR95";
      break;
    }
    case RM_1024:
    {
      asPublicKey  =   "0201B810DA4A1ADD4351378790A981385330CDG9YQSEUK2HT546KWW46NHSCHMD2TNCSPVNSKGQC6CHDUSU9CC4LDPL";
      asPublicKey  +=  "TD3NLRVD3TFV9BU2SYBUBU9TXKLQ3CTKZE5GN9VFG37R9YNK2FE58K7ZY";
      asPublicKey  +=  "WTUJYFSJTBMDLLGA9GJ2LJXGHSS9CESGC2K69H65YQGAC7CRGF5T3CPLA";
      asPublicKey  +=  "5BDJZKVCUWPZRNA2DN7L956LZVZBQUDPX6PF30CDBH26Z82VE4QQ8N85L";
      asPublicKey  +=  "QHYJYYCJ6WQ89H9K9VVQX925SG5FJ79AGVZB6M8TTFKCJ56FAREP8GSTC";
      asPublicKey  +=  "NKWCXEALDP9Y2ZLS9QKZV8JRNDHG9D8BRDSCTMDQK9NLAPZ9TGYFS9G7U";
      asPublicKey  +=  "LXHYJF7F9A89HPXGTU2N4FQQJL98WZ5QND2KTEMMUUZFAX953HCGNSWUW";
      asPublicKey  +=  "U2M7LBEWZSH9GW5SC0CDJDY2HC4HJYUSVTAVXZ7MYVJWF3CU9WC3SVPSL";
      asPublicKey  +=  "29WF46DVS2SYKSSAMATPPYGJDE3PBFMGS34CXWQSXKCEAE63L2EXTS9NE";
      asPublicKey  +=  "2G597XASY3S34XMYST5YZF4THUTYT5DY9UKKXU6PH8MLXRPZMPMMR83JU";
      asPublicKey  +=  "X2WGNEW8ZGL4SKUCUB8CEAB6UZ4FB66WTP3B64VMX7XG75T4Y35479";
      break;
    }
    case RM_2048:
    {
      asPublicKey  =   "0201B810DA4A1ADD4351378790A98138533199H9KZC4CQ5ZKPEGYDW2EHN5AKZA5G5KP4HU8USWJR8XWW7MCH7244RV";
      asPublicKey  +=  "PSXFHVU6YQRK4LPUHM497XJYPLLA3DHUZWXK3CB5SUYF75NPYLTNPRUAP";
      asPublicKey  +=  "CTVDHAKYWTRQ97HES2E59WDN2LJKFYA2K8BGFSBHP4A6BF5FSDTS8NWLL";
      asPublicKey  +=  "C5NEQ76NJ9BAMWXG55UPFFMRQZTU5KV2G6LSPQUJJWA9U4LJ5SCJ9M9ZQ";
      asPublicKey  +=  "Z6K7LZA9UAMJ533UQ5J3JPNQAUWJGZFRFCTGAGSBWPWRRF9HRGF9MCX55";
      asPublicKey  +=  "QRYGPYMRE6DBJ65SJDX34FVPYWWKWQ48MCKXYXPHQ23BJ42U8ZMDD8CG5";
      asPublicKey  +=  "DPHZQ5XTXUKA277XK6PKSTLJ88ZZ9EBXH62REMRZZRGNPKHYM34RW722S";
      asPublicKey  +=  "SZHMPFKY7X8UZ19ADGX3UQ7X444XH7MZXEZYW2YLFHR8YL3PTCXVSYX89";
      asPublicKey  +=  "ZXN7KW2XNBE8XS3TQRGZVF6KA5USH56NEZYH5RNWBBPVE9LD6H42RQWR5";
      asPublicKey  +=  "YVQMT5EQYQTK6YM9V8DA5BJCLGGZ2UGD6NPA2X3SNUS47F7HJBNBCWHRA";
      asPublicKey  +=  "YPPCQ5HWCJNPDHUDQMRN2P6PWJFWJ6CLVNK8HXEFLE4ENPXAFN89CRS43";
      asPublicKey  +=  "RUMVGDJQCSRB535XYCS6BZYLA9Q6HJV425F26XXHRGA7C8R8BVMELAPEH";
      asPublicKey  +=  "5DSA9KXTCV4H2RNVDFHG3YJSERZVFPWHYA823LUBENBL5U3J4LJX2KQLW";
      asPublicKey  +=  "L5XGGESPSE2FQWKSQ4NUCDLBEJPQAT2A2477DYS2XEXKLYEAKUN22622C";
      asPublicKey  +=  "RLBCAHPFNEV2FJ2H8N3JQGXJRGP19AFHGRW6SS2ZSFYK8EZAPVYL4MCP4";
      asPublicKey  +=  "6UMR64Y9T89FHWEPXBRHKWJCSNNNGPU984J5XW4BWTU6PDWPN24W8RU55";
      asPublicKey  +=  "A9C3U9H7VHS5PFURASHBT4JAEE5ZKJBSYMCMDVBLU973R72RVPA6U96A2";
      asPublicKey  +=  "LX8HMASVST9RVJ7W66V4DYWRDZZQ3673W7C7LVF68TWPLNMYEUQ8FDZRN";
      asPublicKey  +=  "P38D9BX5YA3VENETDUUUMR9U7FFP4NZM7G4Y6J6EASK5SAXXTRMZ8F3AH";
      asPublicKey  +=  "5FC54E543N739BY5KVZQM7JZWD2P2HPE7BDRMM9JFCGPBWWASTZ7UDNRV";
      asPublicKey  +=  "6AFHVMBR9AGAERE9M6W6S2ZT8PBTABPDCKBPPPV6QW9DRXZUGAHDDRRSY";
      asPublicKey  +=  "XM4YXQ2QJX6CL33N7RV8846LK8A3DFLDXWZ2NKF8V";
      break;
    }
    case RM_3072:
    {
      asPublicKey  =   "0201B810DA4A1ADD4351378790A98138533267BJBN6W8SY33Y47RWJHWS385XWRV9TSVSB5EM7ECZYNSJ44ZKSD8KV4";
      asPublicKey  +=  "6Y9T6TNC6QMDW4V6DBMADU3YUKLP88TCLEUD9FTEUHGXM6T57AQ2EMB59";
      asPublicKey  +=  "UPQVNSUT7Z4EHAMDQ6N77VK2A6P847MMWJH9E7C5XX6FNF6MHGWERPWQV";
      asPublicKey  +=  "9ZLVR9Z7X5D5Z2XQL6MBM7BDCT2DEDVRBFNDDBLG6LHZ9APM48LS5TKTJ";
      asPublicKey  +=  "AGDYFDRVHEGTDH47PCLL8YJ4ZAWCB59F8BN5PZL3NQACP32G3YNBX5E3J";
      asPublicKey  +=  "7S28XLRGAX55ZML5XE6Q68EUAL38VUBJ2MZ2UGCCHM7CE6S99N44X9RMV";
      asPublicKey  +=  "7B55HULJ4KHPFYSBDUSPQTCWGS76BEQST2QCZDZ2T4FBA4YUJFF47AACE";
      asPublicKey  +=  "A4EEFESEYWN48UZCSDZAMGSU5VB9UJKR2XQUSDFS3TNG9QKURQWS7N5Q3";
      asPublicKey  +=  "VPYT4QAGGQ5GW2MMXHDJYW55FQPHSS255GVJ7WLLP85JHS8K6DVTJWB4W";
      asPublicKey  +=  "4Y8ZEFF7KP6W74PAA8FVPZVDW4RKGMQ8V56Q6R4V34XFE5HR4FZS8GZ6R";
      asPublicKey  +=  "36RKW6WN34S2AHR9LCGF8LULB7ZN6NC2AA5RPDZYGWSPPME6267CGLZCP";
      asPublicKey  +=  "GG98W557RXCDWAETJHDCRPUXX7AD3E8SL9WMGBV9QFHMSV9UFV7H9NTX3";
      asPublicKey  +=  "QYV95CN9P9DSYQWL35R2BWB4GKW6CTWVB2X6WH3P2WFBE6X55KBLAMDB3";
      asPublicKey  +=  "SRDSDVY37GKH78MUHY7KDJDC8SJU5FUQRYZ49ULX3YSZL7E284GFVZSGD";
      asPublicKey  +=  "FTTQNR6VMBM3LLR78SUSVMJW88XT2K4UHEATRU3V9MMR2VGNGCY9S6R7M";
      asPublicKey  +=  "4ELNPEBBMQP2HVPLY5M5VPA92V7NU8ZZ58YYTW5QK9USZ5LV59UZGB2CF";
      asPublicKey  +=  "DPZ6HFYU5WB7UU3UYWVNSNKM3W7GBYKJP8TMFW2YN6GNSSE4ZKFVZRLUT";
      asPublicKey  +=  "8QPZBBEQT5H9H5Z53D7QV4AJTKTX4EMW62TDWXQVK24JZPSG2VXKCMYZL";
      asPublicKey  +=  "BUASE25R3EEK9KBLJ8VX83ZXGTBL5CCDBY33HJ6FKX8AW3WPRX3XTCS9G";
      asPublicKey  +=  "2B9SVK2XFFKNSPGFG6FZNS8P2HERFGFL79DKUZZFWHFH4H9UZ7F7F6W3Z";
      asPublicKey  +=  "WN84JRRA7SU8T89EQMRBEPVYMMGLGSUMFWWMEDLJM5EGP5WEX9LDUAQXS";
      asPublicKey  +=  "55LKDQVRKX4R8NNBHSYBHSNMFYA2C6KGQQFK3TN267DPSZT3PTKHTLNMU";
      asPublicKey  +=  "V2YYZZRX3953LPWAKYF8LJUVMEQT3ZC4F9SWMYGUHG6XX66PJFQWSRUW2";
      asPublicKey  +=  "C79YLGZ6ZAFS7HLXBTH55UD3EGFBZQ5CLNJDZTEJACDVBCM5FQBTX4DKK";
      asPublicKey  +=  "UDGNSMWWLC5UMQRHW89CTU5636WYDXVZZDAUZ529458LDN2DAR9QADWRZ";
      asPublicKey  +=  "UTU8MCKN8ACH3R36YKE52DDF67SM5UDUH7PTATHXE65UCGFRCV49UTF29";
      asPublicKey  +=  "BGDSUWGRMC839HUZYQVAG985YUF5KJCAVGKRD8E4YMPJ38KR6HD2KHDGY";
      asPublicKey  +=  "PJDMJMN4Y3DTJW5MYX3VUPF32637LMAWFM4QCU7BMP74SCF4N5QXZZVBE";
      asPublicKey  +=  "LAQMPYMJC2ZWJ9XFV33YXTTPH3SG9KMUABLL83P7L3UBVM763XJPR5UEH";
      asPublicKey  +=  "DZ73XJUGR9QP6299K5PU46XGVWKWFAKWYMRXUSLCFJ3WQTEC6QJH6E5V8";
      asPublicKey  +=  "X2RMA5TXGSBEWLGSMNQ2SH6G9MLKK4S486DN4M4QVDTYTM4JQETAJQGP7";
      asPublicKey  +=  "6NUR4UNDXR4W4JQSNWDTUGX69SEDC3AL8ZF4TJBQQRXSX2A86UUWCDCFH";
      asPublicKey  +=  "JE94A8EP3Q6A2QTYKEE62MV4VJB2AB";
      break;
    }
    case RM_4096:
    {
      asPublicKey  =   "0201B810DA4A1ADD4351378790A98138533334BN75EUD4LFWYFVNFGBRCEMKKXG62QRBXL8GMHQ975B68FJVEMNXQQ7";
      asPublicKey  +=  "RKR7RCZMWDVP86QPB3LR49G9BM7EUUZXNCRZBXRF4PEY3RX6UXT4X286K";
      asPublicKey  +=  "WMR7AMEE6QNP9AEHGWJT9AVU3F62F37B5GVX2BQF73QWHRFQSQ6XMRP6M";
      asPublicKey  +=  "ZDRQ8JT89UF3YRK5UL34MM3L8HLDTWAJF5YY7MRWJUFDQRK9JNCJ6C9KJ";
      asPublicKey  +=  "WRE5ACQSNS24NEAHDYKS3AK75HBCTKP9NR2CSL7Q9PPXM4TF7L37USP77";
      asPublicKey  +=  "Z7WER6JY67F7YGKTP6MFRMKK9H8XFPZ9XWKK97XCFXK7AW4TB96WGEH4B";
      asPublicKey  +=  "YPF9QRF2DGC3LCHW8VTD2EMHVRDR6EEBUG8R4XYKJPLBPRCJP445DU2BW";
      asPublicKey  +=  "JWELKAY45GKDU5HLTTDTWRH2JFKJ7AVAKLPEELN29YHX5JR773P9ERW7N";
      asPublicKey  +=  "UFMWDKD4UL4YPU5QW2L4A7ZN3E8ZDQVEZUR6ZRDQXRLQRLUNHVB4XGBWD";
      asPublicKey  +=  "HM79NGJARHDBQJCD8NWZCWPBY26QARP7A5S48DCV29F7MUTHU3YAHYVS7";
      asPublicKey  +=  "ZPGXJZZA5HBBY4K5BQPUG6MVCBG8H3C7VL8KH9EH58NUVCE6F2CGMSM94";
      asPublicKey  +=  "4CQDKNVGWWWRNK4DG26UT88Y9ULUPJ25G9KLTQPK3TBXZEK43X8KPMJT8";
      asPublicKey  +=  "DF6MTQ5RB94XR2BT9H9NYYTXL29GF8KZY9QNHAW4LTT2RDQTD2ELWPSV4";
      asPublicKey  +=  "4AND9K8G5VKLQ287UBEVHA82CMSMGQQBMNQZVAZ9G8T5TD6G9F7XQHMJT";
      asPublicKey  +=  "HCL6PFEDUW5MN9JASF2EKPT2H33325A3SFGGM5C3RYKZSYSSQC5FLHNX9";
      asPublicKey  +=  "VHN4HQVWD66YECY4R97JZHQALRTCHXUWH85LSJCCTQZT8JF52FHG6FV9J";
      asPublicKey  +=  "UJWEU24K9Z7SKYTYA9NH7GW29FSNKUK3DMFXY48VEYZVDRTTMLFYSJH8B";
      asPublicKey  +=  "RQXV72REYGSM6SLX89EVQ52CN9N8YDLWHAWCDDNM89YN2FCRTLE8688XN";
      asPublicKey  +=  "F4MZMFE6GP9JK2JTFJVUKBLES2ZVWSBCJE9N3D7DG9LL9Z3U42QFJJPE2";
      asPublicKey  +=  "HE5XCRY25UND96R9D7655ZBSCPF796FJZ4GPLY7AMZLGLR77GRP467ELK";
      asPublicKey  +=  "PXSZ9ZF8MUYFDHV4EFACPZ79CDFTYW56NE9JDWDVUMNLY9EXDSF8ZNABE";
      asPublicKey  +=  "MDQU6T9RVLTNRMZJJ8EJSUM9TV8YA6MNWF2Y66MD3VQ9L662F5X6VE9ZC";
      asPublicKey  +=  "PPRTNMEV2DG9BUMFMSL7PUR9H5QG6VVBDBMEU3PLABL57HMSQ5C83E9X8";
      asPublicKey  +=  "RTPY4QSEH2H7R2QDJHT5LQSFNQJL2NZ24GLNTFXG468WC5UUTDD68G8HM";
      asPublicKey  +=  "477Z8P4F9ZZHUGZ6VPFF32A2MPWY8LPXNECCKM9EEVLDDPUJ8LXN2AE6K";
      asPublicKey  +=  "Y2Q42FVBCJM9GQTXNRHBED735KCFD4YGV4U5U3LJD6NJ886W6XQLWH3G7";
      asPublicKey  +=  "3VZ6T5YDUQP6DA5ZHLKQSGATDJHUWC2ELTE7QJFXH8H9EAQYZPPMW76JG";
      asPublicKey  +=  "Z48UBND9VXFXZMSGBNWQPNQG7E4TCBDMVKUSA9TTVSNLN3DXEZNXH787R";
      asPublicKey  +=  "N7XHFBJNZ5W74DUMNBYVHEQDYMZBQCFMS8LGSDUAU6QE9DD5Q334BX62T";
      asPublicKey  +=  "JP5WQQDS2ZEWW76XW7VMLX3L7V9UVD7RL297VDBVRFPQ5MYA3YFVBVNVA";
      asPublicKey  +=  "R5TJXMQ4X2U2PW6UWSKBXT58HG3QPZM2RVEX2PDMCVJDFTJMXR9NPLW4C";
      asPublicKey  +=  "6AE4Q9A8TTAQ68BSN9WWTFFTSX68ECVARFPDBBH5DMP4KYFARTFVCSWNF";
      asPublicKey  +=  "FGAPBYYGFJA4MGHEDWTNE54MPGER7EPCZEV2ESJRMM6XDMFGABG97HECN";
      asPublicKey  +=  "3SZFUBRKCNDZ2TE9F6EERULRCDAV5EMY6KVY9NY9MYNPP2FQ5WFWLMRYD";
      asPublicKey  +=  "4TNYYHE6EDMQ6KK79HH5Q5KBW6DDQYYNUR365W2454CHEZWZAPU82SX4H";
      asPublicKey  +=  "KHNZXBRVWAT7G9CJBKHNBPSVEL67X8UJHBFHZH8ZD4NSEVFZM4Y768TJ6";
      asPublicKey  +=  "QP3BGEKLTY4UZUWHG63QMKVWAQW9E7WRFNJCH4XRD97JHNKMFUTRFVH68";
      asPublicKey  +=  "G5CKSMGC725AZ9CBSL2JX43P4EY44CMF56AJ6BEXVCJP2ZDWCT9G55K4D";
      asPublicKey  +=  "F24J7TTJMFXTQH95CBNX67CBPNWEKHCWQV6VD73Y9KCZHNBFUFS8TCYNN";
      asPublicKey  +=  "WXRGFKNVRDFUKWHDWDDTSPEW8YCKLPMX2GUV3N4JFJMUE6TSJR6Z53C9F";
      asPublicKey  +=  "SKZAY3EN7G66JPBF5E9S6Y7RQHPWZHSRDQ7XH87PHUJFLJFGD7WNE3Q9P";
      asPublicKey  +=  "EXMW33LVJBFEUXKNUB6SU7ZYCEX96XT8G27FLUV7XPV4JK64DWDJCX9WV";
      asPublicKey  +=  "4VL8CK23FQJ28UG9JUZTQJ3DRL32BTJVRD4LVQ6MK9LCE7ZZP925JK7ZD";
      asPublicKey  +=  "92SCV4DDJJ5EXMVBF";
      break;
    }
  }
  kv.PublicKey = asPublicKey.c_str();

  // Fill key buffer
  char keybuf[2048];
  memset(&keybuf, 0, sizeof(keybuf));
  memcpy(&keybuf, mKey->Text.c_str(), mKey->Text.Length());
  kv.Key = keybuf;
  kv.KeyLen = mKey->Text.Length();

  // Fill User Info
  char namebuf[255];
  memset(&namebuf, 0, sizeof(namebuf));
  memcpy(&namebuf, eUserInfo->Text.c_str(), eUserInfo->Text.Length());
  kv.RegInfo = namebuf;
  kv.RegInfoLen = eUserInfo->Text.Length();

  // Hardware ID
  kv.UseHardwareLocking = cbHardware->Checked;
  if (kv.UseHardwareLocking)
  {
    char hdbuf[255];
    memset(&hdbuf, 0, sizeof(hdbuf));
    memcpy(&hdbuf, eHardware->Text.c_str(), eHardware->Text.Length());
    kv.HardwareID = hdbuf;
  }

  // Secure constant (get it from project file)!
  kv.EncryptedConstant = 2113444489;

  dwResult = KG_VerifyRegistrationInfo(&kv);

  AnsiString sresult;
  if (dwResult == EP_NO_ERROR)
  {
    sresult = "Valid registration key!\n";
    sresult += Format("Creation date %d/%d/%d\n", ARRAYOFCONST(((int)kv.CreationYear, (int)kv.CreationMonth, (int)kv.CreationDay)));
    if (kv.UseKeyExpiration)
    {
      sresult += Format("Expiration date %d/%d/%d\n", ARRAYOFCONST(((int)kv.ExpirationYear, (int)kv.ExpirationMonth, (int)kv.ExpirationDay)));
    }
    if (kv.UseRegisterAfter)
    {
      sresult += Format("Register After date %d/%d/%d\n", ARRAYOFCONST(((int)kv.RegisterAfterYear, (int)kv.RegisterAfterMonth, (int)kv.RegisterAfterDay)));
    }
    if (kv.UseRegisterBefore)
    {
      sresult += Format("Register Before date %d/%d/%d\n", ARRAYOFCONST(((int)kv.RegisterBeforeYear, (int)kv.RegisterBeforeMonth, (int)kv.RegisterBeforeDay)));
    }
    if (kv.UseExecutionsLimit)
    {
      sresult += Format("Executions %d\n", ARRAYOFCONST(((int)kv.ExecutionsCount)));
    }
    if (kv.UseDaysLimit)
    {
      sresult += Format("Days %d\n", ARRAYOFCONST(((int)kv.DaysCount)));
    }
    if (kv.UseRunTimeLimit)
    {
      sresult += Format("Run-time minutes %d\n", ARRAYOFCONST(((int)kv.RunTimeMinutes)));
    }
    if (kv.UseGlobalTimeLimit)
    {
      sresult += Format("Global Time minutes %d\n", ARRAYOFCONST(((int)kv.GlobalTimeMinutes)));
    }
    if (kv.UseCountyLimit)
    {
      for (int i = 0; i < ARRAYSIZE(KEY_COUNTRIES); i++)
      {
        if (kv.CountryCode == KEY_COUNTRIES[i].Code)
        {
          sresult += Format("Country Lock %s\n", ARRAYOFCONST((KEY_COUNTRIES[i].Name)));
          break;
        }
      }
    }
    for (int i = 0; i < NUMBER_OF_CRYPTED_SECTIONS; i++)
    {
      if (kv.EncryptedSections[i])
      {
        sresult += Format("Unlocked section #%d\n", ARRAYOFCONST((i + 1)));
      }
    }
  } else
  {
    switch (dwResult)
    {
      case EP_ERROR_UNKNOWN:
        sresult = "EP_ERROR_UNKNOWN";
        break;
      case EP_ERROR_KEYBUFFEREMPTY:
        sresult = "EP_ERROR_KEYBUFFEREMPTY";
        break;
      case EP_ERROR_KEYBUFFERISLESS:
        sresult = "EP_ERROR_KEYBUFFERISLESS";
        break;
      case EP_ERROR_REGINFOEMPTY:
        sresult = "EP_ERROR_REGINFOEMPTY";
        break;
      case EP_ERROR_REGINFOTOOLARGE:
        sresult = "EP_ERROR_REGINFOTOOLARGE";
        break;
      case EP_ERROR_PRIVATEKEYISNOTSET:
        sresult = "EP_ERROR_PRIVATEKEYISNOTSET";
        break;
      case EP_ERROR_PUBLICKEYISNOTSET:
        sresult = "EP_ERROR_PUBLICKEYISNOTSET";
        break;
      case EP_ERROR_PRIVATEKEYISINVALID:
        sresult = "EP_ERROR_PRIVATEKEYISINVALID";
        break;
      case EP_ERROR_PUBLICKEYISINVALID:
        sresult = "EP_ERROR_PUBLICKEYISINVALID";
        break;
      case EP_ERROR_KEYMODEISINVALID:
        sresult = "EP_ERROR_KEYMODEISINVALID";
        break;
      case EP_ERROR_KEYBASEISINVALID:
        sresult = "EP_ERROR_KEYBASEISINVALID";
        break;
      case EP_ERROR_CURRENTDATEISINVALID:
        sresult = "EP_ERROR_CURRENTDATEISINVALID";
        break;
      case EP_ERROR_EXPIRATIONDATEISINVALID:
        sresult = "EP_ERROR_EXPIRATIONDATEISINVALID";
        break;
      case EP_ERROR_KEYISINVALID:
        sresult = "EP_ERROR_KEYISINVALID";
        break;
      case EP_ERROR_HARDWAREID:
        sresult = "EP_ERROR_HARDWAREID";
        break;
      case EP_ERROR_HARDWAREBUFFEREMPTY:
        sresult = "EP_ERROR_HARDWAREBUFFEREMPTY";
        break;
      case EP_ERROR_HARDWAREIDINVALIDFORKEY:
        sresult = "EP_ERROR_HARDWAREIDINVALIDFORKEY";
        break;
      case EP_ERROR_PROJECTFILENOTFOUND:
        sresult = "EP_ERROR_PROJECTFILENOTFOUND";
        break;
      case EP_ERROR_INVALIDPROJECTFILE:
        sresult = "EP_ERROR_INVALIDPROJECTFILE";
        break;
      case EP_ERROR_EXECUTIONSNUMBERINVALID:
        mKey->Text = "EP_ERROR_EXECUTIONSNUMBERINVALID";
        break;
      case EP_ERROR_DAYSNUMBERINVALID:
        mKey->Text = "EP_ERROR_DAYSNUMBERINVALID";
        break;
      case EP_ERROR_COUNTRYCODEINVALID:
        mKey->Text = "EP_ERROR_COUNTRYCODEINVALID";
        break;
      case EP_ERROR_RUNTIMEINVALID:
        mKey->Text = "EP_ERROR_RUNTIMEINVALID";
        break;
      case EP_ERROR_GLOBALTIMEINVALID:
        mKey->Text = "EP_ERROR_GLOBALTIMEINVALID";
        break;
      case EP_ERROR_INSTALLBEFOREINVALID:
        mKey->Text = "EP_ERROR_INSTALLBEFOREINVALID";
        break;
      case EP_ERROR_INSTALLAFTERINVALID:
        mKey->Text = "EP_ERROR_INSTALLAFTERINVALID";
        break;
      default:
        sresult = "Unknown error";
        break;
    }
  }
  ShowMessage(sresult);
}
//---------------------------------------------------------------------------

void __fastcall TfrmMain::btnVerifyFPClick(TObject *Sender)
{
  if (eUserInfo->Text == "")
  {
    ShowMessage("Registration info is empty!");
    return;
  }
  if (mKey->Text == "")
  {
    ShowMessage("Registration key is empty!");
    return;
  }

  TKeyVerifyParams kv;
  WORD wYear;
  WORD wDay;
  WORD wMonth;
  DWORD dwResult;

  // Clear params buffer
  memset(&kv, 0, sizeof(kv));

  // Fill key buffer
  char keybuf[2048];
  memset(&keybuf, 0, sizeof(keybuf));
  memcpy(&keybuf, mKey->Text.c_str(), mKey->Text.Length());
  kv.Key = keybuf;
  kv.KeyLen = mKey->Text.Length();

  // Fill User Info
  char namebuf[255];
  memset(&namebuf, 0, sizeof(namebuf));
  memcpy(&namebuf, eUserInfo->Text.c_str(), eUserInfo->Text.Length());
  kv.RegInfo = namebuf;
  kv.RegInfoLen = eUserInfo->Text.Length();

  // Hardware ID
  kv.UseHardwareLocking = cbHardware->Checked;
  if (kv.UseHardwareLocking)
  {
    char hdbuf[255];
    memset(&hdbuf, 0, sizeof(hdbuf));
    memcpy(&hdbuf, eHardware->Text.c_str(), eHardware->Text.Length());
    kv.HardwareID = hdbuf;
  }

  dwResult = KG_VerifyRegistrationInfoFromProject("default.enigma", &kv);

  AnsiString sresult;
  if (dwResult == EP_NO_ERROR)
  {
    sresult = "Valid registration key!\n";
    sresult += Format("Creation date %d/%d/%d\n", ARRAYOFCONST(((int)kv.CreationYear, (int)kv.CreationMonth, (int)kv.CreationDay)));
    if (kv.UseKeyExpiration)
    {
      sresult += Format("Expiration date %d/%d/%d\n", ARRAYOFCONST(((int)kv.ExpirationYear, (int)kv.ExpirationMonth, (int)kv.ExpirationDay)));
    }
    if (kv.UseRegisterAfter)
    {
      sresult += Format("Register After date %d/%d/%d\n", ARRAYOFCONST(((int)kv.RegisterAfterYear, (int)kv.RegisterAfterMonth, (int)kv.RegisterAfterDay)));
    }
    if (kv.UseRegisterBefore)
    {
      sresult += Format("Register Before date %d/%d/%d\n", ARRAYOFCONST(((int)kv.RegisterBeforeYear, (int)kv.RegisterBeforeMonth, (int)kv.RegisterBeforeDay)));
    }
    if (kv.UseExecutionsLimit)
    {
      sresult += Format("Executions %d\n", ARRAYOFCONST(((int)kv.ExecutionsCount)));
    }
    if (kv.UseDaysLimit)
    {
      sresult += Format("Days %d\n", ARRAYOFCONST(((int)kv.DaysCount)));
    }
    if (kv.UseRunTimeLimit)
    {
      sresult += Format("Run-time minutes %d\n", ARRAYOFCONST(((int)kv.RunTimeMinutes)));
    }
    if (kv.UseGlobalTimeLimit)
    {
      sresult += Format("Global Time minutes %d\n", ARRAYOFCONST(((int)kv.GlobalTimeMinutes)));
    }
    if (kv.UseCountyLimit)
    {
      for (int i = 0; i < ARRAYSIZE(KEY_COUNTRIES); i++)
      {
        if (kv.CountryCode == KEY_COUNTRIES[i].Code)
        {
          sresult += Format("Country Lock %s\n", ARRAYOFCONST((KEY_COUNTRIES[i].Name)));
          break;
        }
      }
    }    
    for (int i = 0; i < NUMBER_OF_CRYPTED_SECTIONS; i++)
    {
      if (kv.EncryptedSections[i])
      {
        sresult += Format("Unlocked section #%d\n", ARRAYOFCONST((i + 1)));
      }
    }
  } else
  {
    switch (dwResult)
    {
      case EP_ERROR_UNKNOWN:
        sresult = "EP_ERROR_UNKNOWN";
        break;
      case EP_ERROR_KEYBUFFEREMPTY:
        sresult = "EP_ERROR_KEYBUFFEREMPTY";
        break;
      case EP_ERROR_KEYBUFFERISLESS:
        sresult = "EP_ERROR_KEYBUFFERISLESS";
        break;
      case EP_ERROR_REGINFOEMPTY:
        sresult = "EP_ERROR_REGINFOEMPTY";
        break;
      case EP_ERROR_REGINFOTOOLARGE:
        sresult = "EP_ERROR_REGINFOTOOLARGE";
        break;
      case EP_ERROR_PRIVATEKEYISNOTSET:
        sresult = "EP_ERROR_PRIVATEKEYISNOTSET";
        break;
      case EP_ERROR_PUBLICKEYISNOTSET:
        sresult = "EP_ERROR_PUBLICKEYISNOTSET";
        break;
      case EP_ERROR_PRIVATEKEYISINVALID:
        sresult = "EP_ERROR_PRIVATEKEYISINVALID";
        break;
      case EP_ERROR_PUBLICKEYISINVALID:
        sresult = "EP_ERROR_PUBLICKEYISINVALID";
        break;
      case EP_ERROR_KEYMODEISINVALID:
        sresult = "EP_ERROR_KEYMODEISINVALID";
        break;
      case EP_ERROR_KEYBASEISINVALID:
        sresult = "EP_ERROR_KEYBASEISINVALID";
        break;
      case EP_ERROR_CURRENTDATEISINVALID:
        sresult = "EP_ERROR_CURRENTDATEISINVALID";
        break;
      case EP_ERROR_EXPIRATIONDATEISINVALID:
        sresult = "EP_ERROR_EXPIRATIONDATEISINVALID";
        break;
      case EP_ERROR_KEYISINVALID:
        sresult = "EP_ERROR_KEYISINVALID";
        break;
      case EP_ERROR_HARDWAREID:
        sresult = "EP_ERROR_HARDWAREID";
        break;
      case EP_ERROR_HARDWAREBUFFEREMPTY:
        sresult = "EP_ERROR_HARDWAREBUFFEREMPTY";
        break;
      case EP_ERROR_HARDWAREIDINVALIDFORKEY:
        sresult = "EP_ERROR_HARDWAREIDINVALIDFORKEY";
        break;
      case EP_ERROR_PROJECTFILENOTFOUND:
        sresult = "EP_ERROR_PROJECTFILENOTFOUND";
        break;
      case EP_ERROR_INVALIDPROJECTFILE:
        sresult = "EP_ERROR_INVALIDPROJECTFILE";
        break;
      case EP_ERROR_EXECUTIONSNUMBERINVALID:
        mKey->Text = "EP_ERROR_EXECUTIONSNUMBERINVALID";
        break;
      case EP_ERROR_DAYSNUMBERINVALID:
        mKey->Text = "EP_ERROR_DAYSNUMBERINVALID";
        break;
      case EP_ERROR_COUNTRYCODEINVALID:
        mKey->Text = "EP_ERROR_COUNTRYCODEINVALID";
        break;
      case EP_ERROR_RUNTIMEINVALID:
        mKey->Text = "EP_ERROR_RUNTIMEINVALID";
        break;
      case EP_ERROR_GLOBALTIMEINVALID:
        mKey->Text = "EP_ERROR_GLOBALTIMEINVALID";
        break;
      case EP_ERROR_INSTALLBEFOREINVALID:
        mKey->Text = "EP_ERROR_INSTALLBEFOREINVALID";
        break;
      case EP_ERROR_INSTALLAFTERINVALID:
        mKey->Text = "EP_ERROR_INSTALLAFTERINVALID";
        break;        
      default:
        sresult = "Unknown error";
        break;
    }
  }
  ShowMessage(sresult);
}
//---------------------------------------------------------------------------

void __fastcall TfrmMain::cbRegisterAfterClick(TObject *Sender)
{
  dtpRegisterAfter->Enabled = cbRegisterAfter->Checked;        
}
//---------------------------------------------------------------------------

void __fastcall TfrmMain::cbRegisterBeforeClick(TObject *Sender)
{
  dtpRegisterBefore->Enabled = cbRegisterBefore->Checked;        
}
//---------------------------------------------------------------------------

void __fastcall TfrmMain::cbCountryClick(TObject *Sender)
{
  comCountry->Enabled = cbCountry->Checked;
}
//---------------------------------------------------------------------------

void __fastcall TfrmMain::cbExecutionsClick(TObject *Sender)
{
  seExecutions->Enabled = cbExecutions->Checked;
}
//---------------------------------------------------------------------------

void __fastcall TfrmMain::cbDaysClick(TObject *Sender)
{
  seDays->Enabled = cbDays->Checked;        
}
//---------------------------------------------------------------------------

void __fastcall TfrmMain::cbRuntimeClick(TObject *Sender)
{
  seRuntime->Enabled = cbRuntime->Checked;
}
//---------------------------------------------------------------------------

void __fastcall TfrmMain::cbGlobalTimeClick(TObject *Sender)
{
  seGlobalTime->Enabled = cbGlobalTime->Checked;
}
//---------------------------------------------------------------------------

