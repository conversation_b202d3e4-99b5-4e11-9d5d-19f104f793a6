#ifndef __ENGINE_H
#define __ENGINE_H

#include <Windows.h>

namespace Engine
{
	#pragma pack(push, 1) // ensure no padding
	struct Packet
	{
		unsigned short Size;
		unsigned char Type;
		char Data[8000];
	};
	#pragma pack(pop)

	namespace Auction
	{
		static int (__thiscall *Enable)(void *Window) = (int (__thiscall*)(void*))0x0054B530;
		static int (__thiscall *Update)(void *Window) = (int (__thiscall*)(void*))0x0054AE90;
	}

	namespace Music
	{
		static int (__cdecl *Stop)() = (int (__cdecl*)())0x00526720;
		static int (__cdecl *Play)(char *Name) = (int (__cdecl*)(char*))0x00526580;
		static int (__cdecl *BackPlay)(char *Name, int Position, int Value) = (int (__cdecl*)(char*,int,int))0x00526800;
	}

	namespace Move
	{
		static int (__cdecl *MoveCheck)(int Value, int Argument) = (int (__cdecl*)(int,int))0x00711340;
		static int (__thiscall *SetMovement)(int Value, HWND hWnd, UINT Msg, int wParam, LPARAM lParam) = (int (__thiscall*)(int,HWND,UINT,int,LPARAM))0x00480AC0;
	}

	namespace CEditor
	{
		static int (__cdecl *AddPKPWD)() = (int (__cdecl*)())0x00835200;
		static int (__cdecl *PWDPK)(int a1, signed int a2, signed int a3, signed int a4, const char *a5) = (int (__cdecl*)(int, signed int, signed int, signed int,const char*))0x00802C60;
		static void (__thiscall *Clear)(void* _this) = (void (__thiscall*)(void*))0x00763890;
		static void (__thiscall *Load)(void* _this, const char* Text) = (void (__thiscall*)(void*, const char*))0x00764980;
	}

	namespace KGameSys
	{
		static int (__thiscall *DemonGongErase)(int Window, int Item) = (int (__thiscall*)(int,int))0x005CDB40;
		static int (__stdcall *xDecode)(int chCodeNum, int ptr, int Len) = (int (__stdcall*)(int,int,int))0x0048B2E0;
		static int (__stdcall *Decode)(int chCodeNum, int ptr, int Len, int Arg) = (int (__stdcall*)(int,int,int,int))0x0048B320;
		static int (__cdecl *RidingEnable)(int ID, int Type) = (int (__cdecl*)(int,int))0x00692C50;
		static int (__cdecl *RidingDisable)(int ID, int Type) = (int (__cdecl*)(int,int))0x00692DD0;
		static int (__cdecl *AddMsg)(int a1, const char *a2) = (int (__cdecl*)(int,const char*))0x00407A10;
		static int (__cdecl *ArmorGrade)(int a1) = (int (__cdecl*)(int))0x004DA630;
		static void *(__thiscall *ItemMsg)(void *a, const char *a1) = (void *(__thiscall*)(void*,const char*))0x004049B0;
		static int (__cdecl *RebirthTalisman)(int Argument, int Value, char Data) = (int (__cdecl*)(int,int,char))0x006794B0;
		static int (__cdecl *Scenario3_3)(unsigned int Value, int Data) = (int (__cdecl*)(unsigned int,int))0x006F27C0;
		static int (__thiscall *LatestScreenMsg)(void *Argument, const char *a2) = (int (__thiscall*)(void*,const char*))0x00632CF0;
		static int (__thiscall *HellTime)(int Argument, int Value) = (int (__thiscall*)(int,int))0x0066CBE0;
		static char (__cdecl *SetHellTime)(int Time, char Value) = (char (__cdecl*)(int,char))0x006A1480;
		static DWORD (__thiscall *RefreshHellTime)(int Window) = (DWORD (__thiscall*)(int))0x006A1440;
		static char (__thiscall *ScreenOverlay)(int a, int a2, int a3, int a4, char a5) = (char (__thiscall*)(int,int,int,int,char))0x00443800;
		static int (__cdecl *MBox)(int pText, int nType, int nAction, int nDest, int nDest2) = (int (__cdecl*)(int, int, int, int, int))0x006DDC30;
		static int (__thiscall *OnCancel)(void *Argument) = (int (__thiscall*)(void*))0x005D3770;
		static int (__thiscall *OnOk)(void *Argument) = (int (__thiscall*)(void*))0x005D3D40;
		static unsigned long *m_pChattingControl = (unsigned long*)0x009EF908;
		static void (__cdecl *SetScreenInfo)(char Type, void *msg, char value) = (void (__cdecl*)(char, void*, char))0x006EFB70;
		static void (__cdecl *AddChattingMessage)(char Type, const char* Message, int Color) = (void (__cdecl*)(char, const char*, int))0x006E72C0;
		static void (__cdecl *AddInfoMessage)(const char* Message, int Color, int type) = (void (__cdecl*)(const char*, int, int))0x006E72F0;
		static int (__cdecl *OpenWindow)(const char *name, int Argument, int Value, int Type, int nForce, int x) = (int (__cdecl*)(const char*,int,int,int,int,int))0x006DACC0;
		static int (__cdecl *OtherOpenWindow)(const char *name, int Argument, int Value, int Type, int nForce) = (int (__cdecl*)(const char*,int,int,int,int))0x006DD7A0;
		static int (__cdecl *CloseWindow)(char *name) = (int (__cdecl*)(char*))0x006DD400;
		static int (__cdecl *WindowCheck)(const char *name) = (int (__cdecl*)(const char*))0x006DD500;
		static int (__thiscall *SetBuff)(void *a, int a2, int a3, signed __int64 a4, char a5, signed __int64 a6, int a7, int a8, int a9, signed int a10) = (int (__thiscall*)(void*,int,int,signed __int64,char,signed __int64,int,int,int,signed int))0x00787510;
		static int (__thiscall *SetIcon)(void *a, int Key, int Time, int a4, int nMsg) = (int (__thiscall*)(void*,int,int,int,int))0x00792EE0;
		static int (__thiscall *RemoveIcon)(void *a, int Key, int nMsg) = (int (__thiscall*)(void*,int,int))0x00792C60;
		static int (__cdecl *CallBuff)(int a1, int a2, signed __int64 a3, char a4, signed __int64 a5, int a6, int a7, int a8, int a9) = (int (__cdecl*)(int,int,signed __int64,char,signed __int64,int,int,int,int))0x006E4EC0;
		static int (__thiscall *ShowBuffIcon)(int a) = (int (__thiscall*)(int))0x0042D4A0;
		static int (__thiscall *MakeTip)(void *a, int a2, int a3, signed int a4, char a5, int a6, int a7, int a8) = (int (__thiscall*)(void*,int,int,signed int,char,int,int,int))0x004C9C40;
		static int (__cdecl *FindItemFromIID)(int a2) = (int (__cdecl*)(int))0x006AF1B0;
		static int (__cdecl *FindItemFromIIDEx)(int a2) = (int (__cdecl*)(int))0x006AF270;
		static int (__cdecl *FindItemFromIIDEx2)(int a2) = (int (__cdecl*)(int))0x006AF590;
		static signed int (__cdecl *PressKey)(int a1) = (signed int (__cdecl*)(int))0x006E6890;
		static int PressedKey = (int)0x009EF990;
		static int (__cdecl *Check)() = (int (__cdecl*)())0x00762100;
		static int (__cdecl *KMsgGet)(int SysMsgIndex) = (int (__cdecl*)(int))0x004B62B0;
		static int (__thiscall *SetChangedText)(int Item, int TextName, int NewMsg) = (int (__thiscall*)(int,int,int))0x00779CF0;
		static int (__thiscall *SetDssWindow)(void *Window, int Packet) = (int (__thiscall*)(void*,int))0x005A9C90;
		static int (__thiscall *DssFuseWindow)(void *Window, int Packet) = (int (__thiscall*)(void*,int))0x005A96B0;
		static int (__thiscall *SetQigoong)(int Window, int Packet) = (int (__thiscall*)(int,int))0x005AA890;
		static void (__cdecl *DemonGongStone)(int Value, int Argument, int Packet) = (void (__cdecl*)(int,int,int))0x006856E0;
		static int (__thiscall *SetDemonGongWindow)(void *Window, int Packet) = (int (__thiscall*)(void*,int))0x005CEE90;
		static void (__thiscall *SendIdle)(int Object) = (void (__thiscall*)(int))0x0041E3F0;
	}
	
	namespace KWindowCollector
	{
		static void (__cdecl *ProcessDestroy)() = (void (__cdecl*)())0x006DD810;
	}

	namespace CD3DApplication
	{
		static HRESULT (__thiscall *Render3DEnvironment)(void* _this) = (HRESULT (__thiscall*)(void*))0x00482D90;
	}
	
	namespace KCommand
	{
		static int *m_bShowFrame = (int*)0x009F18B4;
		static int *m_bHideUI = (int*)0x009F18BC;
		static int *m_bHideMe = (int*)0x009F18C0;
		static int *m_bHideTerrain = (int*)0x009F18C4;
		static int *m_bHideModel = (int*)0x009F18C8;
		static int *m_bHideFog = (int*)0x009F18CC;
		static int *m_fFogEnd = (int*)0x009F18E0;
		static int *m_bHideWater = (int*)0x009F18E8;
		static bool (__cdecl *ProcessCommand)(const char* Text) = (bool (__cdecl*)(const char*))0x006FFF10;
	}
	
	namespace KSunLight
	{
		static unsigned long *m_dwTime = (unsigned long*)0x008D4C68;
	}
	
	namespace KSocket
	{
		static int *g_lpClient = (int*)0x009DBF24;
		static int (__thiscall *SendPacket)(void* _this, const char* Buffer, int Length) = (int (__thiscall*)(void*, const char*, int))0x006708B0;
		static int (__cdecl *OnRecv)(Packet *Data) = (int (__cdecl*)(Packet*))0x00652F50;
		static float *m_fWriteTime = (float*)0x009DBF2C;
	}
	
	namespace CGame_Character
	{
		static unsigned long *m_Master = (unsigned long*)0x00906824;
		static unsigned long (__cdecl *FindCharacter)(unsigned int nID, int) = (unsigned long (__cdecl*)(unsigned int, int))0x00428490;
	}
	
	namespace KBattle_UI
	{
		static void (__thiscall *DrawOnCharacter)(void *_this, unsigned char Type, int Number, int CharacterID, int a) = (void (__thiscall*)(void*, unsigned char, int, int, int))0x0079FAE0;
	}
	
	namespace CGameMonster
	{
		static signed int (__thiscall *OnChangeGState)(int a) = (signed int (__thiscall*)(int))0x00451D80;
		static char (__thiscall *ModelSkill)(int a, char a2, int a3, unsigned __int8 a4, unsigned __int16 a5, unsigned __int16 a6, char a7, char a8) = (char (__thiscall*)(int, char, int, unsigned __int8, unsigned __int16, unsigned __int16, char, char))0x004516A0;
	}
	
	namespace CFxEntityManager
	{
		static signed int (__thiscall *AddFxToTarget)(void *player, const char* name, int targetID, char autoRemove, char continued, signed int nForced, int nTargetType, int a) = (signed int (__thiscall*)(void*, const char*, int, char, char, signed int, int, int))0x007DEBE0;
	}

	namespace Honor
	{
		static int *Range = (int*)0x009EFABC;
		static int *PrevRange = (int*)0x009EFAC8;
		static int *Point = (int*)0x009EFAB4;
		static int *Kill = (int*)0x009EFACC;
		static int *Die = (int*)0x009EFAD0;
		static int *DKPWin = (int*)0x009EFAD8;
		static int *DKPFull = (int*)0x009EFAD4;
		static int *PLWin = (int*)0x009EFAE0;
		static int *PLFull = (int*)0x009EFADC;
		static int *SVWin = (int*)0x009EFAE8;
		static int *SVFull = (int*)0x009EFAE4;
		static int *Reward = (int*)0x009EFAB8;
	}

	namespace XFileZip
	{
		static signed int (__thiscall *Open)(void *Buffer, const char *Ptr) = (signed int (__thiscall*)(void*,const char*))0x0049E640;
	}

	static float *g_fCurrentTime = (float*)0x00A104C8;
	static void *AddFx = (void*)0x00A0EE30;
}

int __stdcall MyxDecode(int chCodeNum, int ptr, int Len);
int __stdcall MyDecode(int chCodeNum, int ptr, int Len, int Arg);

#endif