// Microsoft Visual C++ generated resource script.
//
#include "resource.h"

#define APSTUDIO_READONLY_SYMBOLS
/////////////////////////////////////////////////////////////////////////////
//
// Generated from the TEXTINCLUDE 2 resource.
//
#include "afxres.h"

/////////////////////////////////////////////////////////////////////////////
#undef APSTUDIO_READONLY_SYMBOLS

/////////////////////////////////////////////////////////////////////////////
// Russian resources

#if !defined(AFX_RESOURCE_DLL) || defined(AFX_TARG_RUS)
#ifdef _WIN32
LANGUAGE LANG_RUSSIAN, SUBLANG_DEFAULT
#pragma code_page(1251)
#endif //_WIN32

#ifdef APSTUDIO_INVOKED
/////////////////////////////////////////////////////////////////////////////
//
// TEXTINCLUDE
//

1 TEXTINCLUDE 
BEGIN
    "resource.h\0"
END

2 TEXTINCLUDE 
BEGIN
    "#include ""afxres.h""\r\n"
    "\0"
END

3 TEXTINCLUDE 
BEGIN
    "#define _AFX_NO_SPLITTER_RESOURCES\r\n"
    "#define _AFX_NO_OLE_RESOURCES\r\n"
    "#define _AFX_NO_TRACKER_RESOURCES\r\n"
    "#define _AFX_NO_PROPERTY_RESOURCES\r\n"
    "\r\n"
    "#if !defined(AFX_RESOURCE_DLL) || defined(AFX_TARG_ENU)\r\n"
    "#ifdef _WIN32\r\n"
    "LANGUAGE 9, 1\r\n"
    "#pragma code_page(1252)\r\n"
    "#endif //_WIN32\r\n"
    "#include ""keygen.rc2""  // non-Microsoft Visual C++ edited resources\r\n"
    "#include ""afxres.rc""         // Standard components\r\n"
    "#endif\r\n"
    "\0"
END

#endif    // APSTUDIO_INVOKED


/////////////////////////////////////////////////////////////////////////////
//
// Icon
//

// Icon with lowest ID value placed first to ensure application icon
// remains consistent on all systems.
IDR_MAINFRAME           ICON                    "keygen.ico"
#endif    // Russian resources
/////////////////////////////////////////////////////////////////////////////


/////////////////////////////////////////////////////////////////////////////
// English (U.S.) resources

#if !defined(AFX_RESOURCE_DLL) || defined(AFX_TARG_ENU)
#ifdef _WIN32
LANGUAGE LANG_ENGLISH, SUBLANG_ENGLISH_US
#pragma code_page(1252)
#endif //_WIN32

/////////////////////////////////////////////////////////////////////////////
//
// Dialog
//

IDD_ABOUTBOX DIALOG  0, 0, 235, 55
STYLE DS_SETFONT | DS_MODALFRAME | WS_POPUP | WS_CAPTION | WS_SYSMENU
CAPTION "About keygen"
FONT 8, "MS Sans Serif"
BEGIN
    ICON            IDR_MAINFRAME,IDC_STATIC,11,17,20,20
    LTEXT           "keygen Version 1.0",IDC_STATIC,40,10,119,8,SS_NOPREFIX
    LTEXT           "Copyright (C) 2007",IDC_STATIC,40,25,119,8
    DEFPUSHBUTTON   "OK",IDOK,178,7,50,14,WS_GROUP
END

IDD_KEYGEN_DIALOG DIALOGEX 0, 0, 454, 273
STYLE DS_SETFONT | DS_MODALFRAME | WS_POPUP | WS_VISIBLE | WS_CAPTION | WS_SYSMENU
EXSTYLE WS_EX_APPWINDOW
CAPTION "The Enigma Protector Keys Generator"
FONT 8, "MS Sans Serif", 0, 0, 0x1
BEGIN
    CONTROL         "Hyphens",IDC_CHECKHYPHENS,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,24,54,41,8
    EDITTEXT        IDC_EDITNAME,12,18,306,14,ES_AUTOHSCROLL
    LTEXT           "User Information",IDC_STATIC,12,6,52,8
    GROUPBOX        "Type of Registration Key",IDC_STATIC,12,36,306,42
    COMBOBOX        IDC_COMBOKMODE,84,60,96,72,CBS_DROPDOWNLIST | WS_VSCROLL | WS_TABSTOP
    COMBOBOX        IDC_COMBOKBASE,192,60,84,84,CBS_DROPDOWNLIST | CBS_NOINTEGRALHEIGHT | WS_VSCROLL | WS_TABSTOP
    LTEXT           "Key Mode",IDC_STATIC,84,48,33,8
    LTEXT           "Key Base",IDC_STATIC,192,48,31,8
    GROUPBOX        "Key Restrictions",IDC_STATIC,12,84,306,108
    CONTROL         "Expiration Date",IDC_CHECKEXPIRATION,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,24,102,66,10
    CONTROL         "",IDC_DTPEXPIRATION,"SysDateTimePick32",DTS_RIGHTALIGN | WS_DISABLED | WS_TABSTOP,96,102,100,15
    CONTROL         "Register After",IDC_CHECKREGAFTER,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,24,120,60,10
    CONTROL         "",IDC_DTPREGAFTER,"SysDateTimePick32",DTS_RIGHTALIGN | WS_DISABLED | WS_TABSTOP,96,120,100,15
    CONTROL         "",IDC_DTPREGBEFORE,"SysDateTimePick32",DTS_RIGHTALIGN | WS_DISABLED | WS_TABSTOP,96,138,100,15
    CONTROL         "Register Before",IDC_CHECKREGBEFORE,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,24,138,66,10
    CONTROL         "Country Lock",IDC_CHECKCOUNTRY,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,24,156,60,10
    COMBOBOX        IDC_COMBOCOUNTRIES,96,156,102,84,CBS_DROPDOWNLIST | WS_DISABLED | WS_VSCROLL | WS_TABSTOP
    CONTROL         "Executions",IDC_CHECKEXECS,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,204,102,48,10
    CONTROL         "Days",IDC_CHECKDAYS,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,204,120,32,10
    CONTROL         "Run-time",IDC_CHECKRTIME,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,204,138,44,10
    CONTROL         "Global Time",IDC_CHECKGTIME,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,204,156,53,10
    EDITTEXT        IDC_EDITEXECS,264,102,40,14,ES_AUTOHSCROLL | ES_NUMBER | WS_DISABLED
    EDITTEXT        IDC_EDITRTIME,264,138,40,14,ES_AUTOHSCROLL | ES_NUMBER | WS_DISABLED
    EDITTEXT        IDC_EDITDAYS,264,120,40,14,ES_AUTOHSCROLL | ES_NUMBER | WS_DISABLED
    EDITTEXT        IDC_EDITGTIME,264,156,40,14,ES_AUTOHSCROLL | ES_NUMBER | WS_DISABLED
    GROUPBOX        "Sections",IDC_STATIC,324,18,120,174
    CONTROL         "Section 1",IDC_CHECKS1,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,336,30,46,10
    CONTROL         "Section 3",IDC_CHECKS3,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,336,66,46,10
    CONTROL         "Section 2",IDC_CHECKS2,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,336,48,46,10
    CONTROL         "Section 4",IDC_CHECKS4,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,336,84,46,10
    CONTROL         "Section 5",IDC_CHECKS5,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,336,102,46,10
    CONTROL         "Section 6",IDC_CHECKS6,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,336,120,46,10
    CONTROL         "Section 7",IDC_CHECKS7,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,336,138,46,10
    CONTROL         "Section 8",IDC_CHECKS8,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,336,156,46,10
    CONTROL         "Section 9",IDC_CHECKS9,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,390,30,46,10
    CONTROL         "Section 11",IDC_CHECKS11,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,390,66,50,10
    CONTROL         "Section 10",IDC_CHECKS10,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,390,48,50,10
    CONTROL         "Section 12",IDC_CHECKS12,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,390,84,50,10
    CONTROL         "Section 13",IDC_CHECKS13,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,390,102,50,10
    CONTROL         "Section 14",IDC_CHECKS14,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,390,120,50,10
    CONTROL         "Section 15",IDC_CHECKS15,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,390,138,50,10
    CONTROL         "Section 16",IDC_CHECKS16,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,390,156,50,10
    PUSHBUTTON      "Generate",IDC_BUTTONGEN,12,198,96,14
    PUSHBUTTON      "Generate from Project",IDC_BUTTONGENFP,114,198,96,14
    PUSHBUTTON      "Verify",IDC_BUTTONVER,246,199,96,14
    PUSHBUTTON      "Verify from Prtoject",IDC_BUTTONVERFP,348,198,96,14
    EDITTEXT        IDC_EDITKEY,12,216,330,48,ES_MULTILINE | WS_VSCROLL
    PUSHBUTTON      "Close",IDC_BUTTONCLOSE,348,216,96,48
    CONTROL         "Hardware ID",IDC_CHECKHD,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,24,174,56,10
    EDITTEXT        IDC_EDITHD,96,174,210,14,ES_AUTOHSCROLL | WS_DISABLED
END


/////////////////////////////////////////////////////////////////////////////
//
// Version
//

VS_VERSION_INFO VERSIONINFO
 FILEVERSION 1,0,0,1
 PRODUCTVERSION 1,0,0,1
 FILEFLAGSMASK 0x3fL
#ifdef _DEBUG
 FILEFLAGS 0x1L
#else
 FILEFLAGS 0x0L
#endif
 FILEOS 0x4L
 FILETYPE 0x1L
 FILESUBTYPE 0x0L
BEGIN
    BLOCK "StringFileInfo"
    BEGIN
        BLOCK "040904B0"
        BEGIN
            VALUE "FileDescription", "keygen MFC Application"
            VALUE "FileVersion", "1, 0, 0, 1"
            VALUE "InternalName", "keygen"
            VALUE "LegalCopyright", "Copyright (C) 2007"
            VALUE "OriginalFilename", "keygen.EXE"
            VALUE "ProductName", "keygen Application"
            VALUE "ProductVersion", "1, 0, 0, 1"
        END
    END
    BLOCK "VarFileInfo"
    BEGIN
        VALUE "Translation", 0x409, 1200
    END
END


/////////////////////////////////////////////////////////////////////////////
//
// DESIGNINFO
//

#ifdef APSTUDIO_INVOKED
GUIDELINES DESIGNINFO 
BEGIN
    IDD_ABOUTBOX, DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 228
        TOPMARGIN, 7
        BOTTOMMARGIN, 48
    END

    IDD_KEYGEN_DIALOG, DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 447
        VERTGUIDE, 17
        VERTGUIDE, 64
        VERTGUIDE, 70
        VERTGUIDE, 118
        VERTGUIDE, 128
        VERTGUIDE, 178
        VERTGUIDE, 186
        VERTGUIDE, 236
        TOPMARGIN, 7
        BOTTOMMARGIN, 266
        HORZGUIDE, 97
    END
END
#endif    // APSTUDIO_INVOKED


/////////////////////////////////////////////////////////////////////////////
//
// Dialog Info
//

IDD_KEYGEN_DIALOG DLGINIT
BEGIN
    IDC_COMBOKMODE, 0x403, 10, 0
0x207e, 0x5352, 0x2041, 0x3135, 0x0032, 
    IDC_COMBOKMODE, 0x403, 10, 0
0x207e, 0x5352, 0x2041, 0x3637, 0x0038, 
    IDC_COMBOKMODE, 0x403, 11, 0
0x207e, 0x5352, 0x2041, 0x3031, 0x3432, "\000" 
    IDC_COMBOKMODE, 0x403, 11, 0
0x207e, 0x5352, 0x2041, 0x3032, 0x3834, "\000" 
    IDC_COMBOKMODE, 0x403, 11, 0
0x207e, 0x5352, 0x2041, 0x3033, 0x3237, "\000" 
    IDC_COMBOKMODE, 0x403, 11, 0
0x207e, 0x5352, 0x2041, 0x3034, 0x3639, "\000" 
    IDC_COMBOKBASE, 0x403, 7, 0
0x6142, 0x6573, 0x3220, "\000" 
    IDC_COMBOKBASE, 0x403, 7, 0
0x6142, 0x6573, 0x3820, "\000" 
    IDC_COMBOKBASE, 0x403, 8, 0
0x6142, 0x6573, 0x3120, 0x0036, 
    IDC_COMBOKBASE, 0x403, 8, 0
0x6142, 0x6573, 0x3320, 0x0032, 
    IDC_COMBOKBASE, 0x403, 8, 0
0x6142, 0x6573, 0x3620, 0x0034, 
    0
END


/////////////////////////////////////////////////////////////////////////////
//
// String Table
//

STRINGTABLE 
BEGIN
    IDS_ABOUTBOX            "&About keygen..."
END

#endif    // English (U.S.) resources
/////////////////////////////////////////////////////////////////////////////



#ifndef APSTUDIO_INVOKED
/////////////////////////////////////////////////////////////////////////////
//
// Generated from the TEXTINCLUDE 3 resource.
//
#define _AFX_NO_SPLITTER_RESOURCES
#define _AFX_NO_OLE_RESOURCES
#define _AFX_NO_TRACKER_RESOURCES
#define _AFX_NO_PROPERTY_RESOURCES

#if !defined(AFX_RESOURCE_DLL) || defined(AFX_TARG_ENU)
#ifdef _WIN32
LANGUAGE 9, 1
#pragma code_page(1252)
#endif //_WIN32
#include "keygen.rc2"  // non-Microsoft Visual C++ edited resources
#include "afxres.rc"         // Standard components
#endif

/////////////////////////////////////////////////////////////////////////////
#endif    // not APSTUDIO_INVOKED

