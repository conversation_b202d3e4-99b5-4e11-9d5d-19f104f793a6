The Enigma Protector v1.51 Build 20080715
- Added console version
- Added possibility to use message designer variables in Registration Dialog Designer
- Added feature to reset local special info for debugging puporses (for example, to reset local startup password information)
- Added feature that allows to ask startup password after defined number of days
- Added possibility to change startup password
- Added feature to hide/unhide symbols of startup password
- Added feature that allows execution only if the module is registered
- Added variables %AppName% %AppVers% for dialog designer that return Application Name and Version
- Added Hardware Lock type that depends on Windows Serial Key
- Added Enigma API EP_RegKeyCreationDate that returns the date when the key was created
- Added OnClose event for form in Registration Dialog Designer
- Added protection against memory breakpoints
- Added feature to add/view matermarks in the protected file
- Added function to show key expiration reminder
- Added fast help link on the settings pages
- Improved algorithm of loading Virtual Machine functions from project file, it is faster now
- Remade startup password window
- Bug fixed Entry Point Protection feature when protecting executable files compiled with Visual Studio 8+
- Bug fixed when protected file contains import dll which does not import any function
- Bug fixed with Files Attachment function if embed already protected dll files
- Bug fixed with loading of project that contains files attachment feature
- Bug fixed in Registration Dialog Designer, width did not save for label components

The Enigma Protector v1.50 Build 20080601
- Added possibility to show custom registration dialog
- Added few new variables for message designer, see manual
- Added checkup of Virtualization Tools (Virtual Machines) like VMWare, Virtual PC, Sandboxie
- Added checkup of Windows Version
- Added checkup of executed processes (by file name, window text, window class)
- Added checkup of external files
- Added File Attachment function
- Added possibility to send emails to selected users in Enigma Mailer
- Added possibility to generate emails for selected users in Enigma Mailer
- Added emulating of LoadLibraryA function for dinamic Enigma API calling
- Added mass functions selecting for Virtual Machine
- Remade struct of project file, some data may be lost if you open older project (no event registration data lose)
- Improved plugin for file analyzing
- Improved displaying of Virtual Machine functions
- Improved MAP file parsing algorithm
- Improved protection against Original Entry Point finding
- Bug fixed running on Windows Vista
- Bug fixed running protected files on Windows 2008 Server Windows 2003 Server
- Bug fixed running protected files on Windows 9x under VMWare
- Bug fixed with antidebugger protection
- Bug fixed with files attachment if the attached files are compressed
- Bug fixed with protecting files that do not support SEH
- Bug fixed with dynamic calling API's
- Bug fixed registration info reading APIs were failed on Windows Vista with minimum privileges
- Bug fixed with Decrypt_On_Execute marker (protected file sometimes crashed)
- Bug fixed with file entropy for files with overlays
- Bug fixed with Trial Limitation of Eexuction Time (expired even if registered)
- Bug fixed with Enigma API while dinamically calling
- Bug fixed with keygen.dll VerifyRegistrationInfo (remade TVerifyKey type)

The Enigma Protector v1.40 Build 20080126
- Added Enigma Protector Registration Manager
- Added Enigma Protector Mailer
- Added Czech GUI translation
- Added possibility to mark registration keys as stolen
- Added trial limitation from date to date, Enigma API EP_TrialDateTillDate that returns start and end dates of trial
- Added support of Visual Studio 2008 map files
- Added KG_VerifyRegistrationInfo function in keygen.dll for verifying and extracting values from registration keys
- Bug fixed with Virtual Machine, processing instruction mov [reg32 + imm8], imm32
- Bug fixed, trial does not expire immediately if the execution time sets to 0 minutes
- Bug fixed, Enigma Protector manual sometimes did not open from main manu.

The Enigma Protector v1.37 Build 20071226
- Added various output registration key types (10, 16, 32, 64 digs)
- Added short type of registration key (reg. keys size is up to 41 symbols)
- Bug fixed with Virtual Machine, processing instruction cmp [reg32], imm32
- Bug fixed with Enigma Protector executing under Windows Vista

The Enigma Protector v1.35 Build 20071012
- Added Virtual Machine protection feature for extended protection! Supports Delphi, CBulder, VC.
- Solved DEP problem. Protected applications are started now without DEP exception.
- Added example programs for C#.
- Added Enigma API support for .NET applications. 
- Bug fixed with Enigma API function EP_RegKeyExpirationDate for Visual Basic.

The Enigma Protector v1.33 Build 20070810
- Added message variables, you are able to use special strings to show some numerical values.
- Added trial feature which allows to open file when trial has expired
- Added registration feature to allow protected module only time limited registration keys
- Added registration feature to allow protected module only hardware locked registration keys
- Added Enigma API function EP_RegKeyExpirationDate - returns key expiration date in case the registration key is valid and time limited.
- Added checkup of executed copies of protected module. 
- Added status icons on the main settings tree
- Added multi-languages support
- Added file information panel in the Input settings tab. It provides with special information about file type, size, PE header values.
- Added trial limiation feature "Limit of execution time". You are able to limit time of module work within execution. Added Enigma API function EP_TrialExecutionTime to return parameters of this limitation. Added function examples.
- Added .NET executables support
- Minor bugs fixed with trial Enigma API
- Minor bugs fixed with Enigma API for Visual Basic compiled executables.

The Enigma Protector v1.31 Build 20070615
- Added dimensions of the loader messages are proportional to its content now
- Added features for run-time trial controling, if the protected application will work much time without termination and trial will expire while application works then expiration event will be processed as well
- Added feature for deleting of the local trial and registration information for debugging purpose.
- Rewritten main options tree of GUI
- Rewritten algorithm of CPU Hardware Lock
- Bug fixed with trial limitation of expiration date
- Bug fixed in Computer Name Hardware Lock
- Bug fixed something also with command line
- Bug fixed with "File entry point obfuscation"

The Enigma Protector v1.31 Build 20070604
- Bugs fixed with command line supporting
- Bugs fixed in trial settings saving

The Enigma Protector v1.30 Build 20070528
- Renamed Enigma API
- Bugs fixed with "Anti-debugger protection" for vmwares

The Enigma Protector v1.30 Build 20070223
- Added builded Registration Keys Generator
- Added feature for checking of hardware breakpoints
- Bugs fixed in trials...
- Bugs fixed in "Anti-debugger protection" for Win 9x
- Bugs fixed with Virtual Machine for emulation of functions from map file

The Enigma Protector v1.30 Build 20070131
- Bugs fixed with Enigma API

The Enigma Protector v1.30 Build 20070130

- Added several instructions for EP emulation
- Bug fixed with CRC checking
- Bug fixed with EP import recovering