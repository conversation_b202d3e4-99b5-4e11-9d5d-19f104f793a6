#include "OnSend.h"
#include "Interface.h"
#include "Hooks.h"
#include "Tools.h"

CREATE_INTERFACE(IOnSend)

unsigned long IOnSend::Patches[] =
{
	0x0041CD64, 0x0041CE23, 0x0042D131, 0x0042D167, 0x0042D242, 0x0042D275, 0x0042DB91, 0x0042DC15, 0x0042DCF3, 0x0042DD76,
	0x0042DDFA, 0x0042F4B2, 0x0042F6C0, 0x00430D59, 0x004311CB, 0x0043147F, 0x00431709, 0x0043173D, 0x004317DE, 0x00431895,
	0x00431907, 0x00431CE0, 0x00431D4D, 0x00431EE3, 0x0043203C, 0x00432067, 0x0043207D, 0x004337A4, 0x004339EE, 0x00433CDF,
	0x0045775E, 0x0049151C, 0x004D8F64, 0x004D8FA7, 0x00547231, 0x0054AF73, 0x0054AF97, 0x0054AFBB, 0x0054AFDF, 0x0054B003,
	0x0054B027, 0x0054B04B, 0x0054B06F, 0x0054B090, 0x0054B0B1, 0x0054B0D2, 0x0054BF9D, 0x0054BFC4, 0x0054BFEB, 0x0054C012,
	0x0054C039, 0x0054C060, 0x0054C084, 0x0054C0A8, 0x0054C0CC, 0x0054C10A, 0x0054C148, 0x0054C1F5, 0x0054C21C, 0x0054C243,
	0x0054C26A, 0x0054C291, 0x0054C2B8, 0x0054C2DC, 0x0054C300, 0x0054C324, 0x0054C36D, 0x0054C3B3, 0x0054D5DC, 0x0054EF9F,
	0x0054F5EF, 0x00554178, 0x0055493A, 0x00554983, 0x00554DE9, 0x00554EC7, 0x00554F10, 0x0055534F, 0x0055576B, 0x00556413,
	0x005565E3, 0x00556625, 0x00556864, 0x005569F2, 0x00557534, 0x00558D36, 0x005590BE, 0x005591F1, 0x005594D7, 0x00559C95,
	0x0055AA8F, 0x0055AAD2, 0x0055AB15, 0x0055AB53, 0x0055ABD8, 0x0055AD54, 0x0055AFCC, 0x0055B08B, 0x0055B0CD, 0x0055BDDB,
	0x0055BE37, 0x0055BEA9, 0x0055E273, 0x0055E2B3, 0x0055FC6A, 0x00562508, 0x00562FFF, 0x00563694, 0x005638C9, 0x0056475B,
	0x0056560B, 0x00566625, 0x005666E3, 0x00569652, 0x0056975A, 0x005697B4, 0x0056C1C2, 0x0056E697, 0x0056E6F9, 0x0056E758,
	0x00571AA5, 0x00571C2B, 0x0057C644, 0x005807A3, 0x005807E7, 0x00583103, 0x00583450, 0x00588CAA, 0x005898BE, 0x00589AD2,
	0x00589BD0, 0x00589D98, 0x0058C0CB, 0x0058CCF0, 0x0058D0E1, 0x0058DACD, 0x0058DB06, 0x0058DB6F, 0x0058DBA6, 0x0058DBD1,
	0x0058DC2E, 0x0058DCA6, 0x0058DCCA, 0x0058DD41, 0x0058DD96, 0x0058E32E, 0x0058E357, 0x0058E399, 0x0058E3C9, 0x0058E6FC,
	0x0058E82A, 0x0058F475, 0x0058FDF5, 0x00590053, 0x005900F6, 0x00591C6F, 0x005922CF, 0x0059230A, 0x00593A42, 0x00594090,
	0x005A17EB, 0x005A1837, 0x005A3A9C, 0x005A64F2, 0x005A9167, 0x005A9B4C, 0x005AA4B2, 0x005ACFF8, 0x005AD074, 0x005AD117,
	0x005AD1BA, 0x005AE58C, 0x005AE5AC, 0x005AE5CC, 0x005AE5EC, 0x005AE609, 0x005AE684, 0x005AE6A4, 0x005AE6DC, 0x005AE71B,
	0x005AE75A, 0x005AEBBE, 0x005AEC46, 0x005AEC72, 0x005AECD1, 0x005AED2D, 0x005AF59A, 0x005B1816, 0x005B19EF, 0x005B1C21,
	0x005B56B8, 0x005B64E7, 0x005B808F, 0x005B9051, 0x005BF999, 0x005C1B71, 0x005C1C81, 0x005C49A5, 0x005C53BE, 0x005C53D7,
	0x005C71FD, 0x005C7224, 0x005C94E4, 0x005C9AEB, 0x005C9BBD, 0x005C9C05, 0x005CA57A, 0x005CA5B3, 0x005CB0B6, 0x005CB642,
	0x005CB89E, 0x005CBCCC, 0x005CC0FA, 0x005CCB3F, 0x005CCC13, 0x005CD7E1, 0x005CE874, 0x005CF6D3, 0x005D11A3, 0x005D24BC,
	0x005D3617, 0x005D3647, 0x005D367A, 0x005D36A7, 0x005D36D7, 0x005D37E2, 0x005D381F, 0x005D386C, 0x005D38E8, 0x005D3E67,
	0x005D3E9E, 0x005D3FBD, 0x005D3FF9, 0x005D4032, 0x005D406B, 0x005D40A4, 0x005D40E0, 0x005D4119, 0x005D4152, 0x005D418E,
	0x005D41C7, 0x005D4202, 0x005D423B, 0x005D4964, 0x005D4A2E, 0x005D4A67, 0x005D4B2E, 0x005D4BF3, 0x005D4CB8, 0x005D4DAA,
	0x005D4FEA, 0x005D5042, 0x005D507B, 0x005D50CC, 0x005D53D7, 0x005D5446, 0x005D5833, 0x005D5A4C, 0x005D5C14, 0x005D5CA8,
	0x005D5F0E, 0x005D5F3C, 0x005D5F6A, 0x005D5F98, 0x005D5FC6, 0x005D5FF4, 0x005D6022, 0x005D6050, 0x005D607E, 0x005D60AC,
	0x005D60DA, 0x005D6108, 0x005D6136, 0x005D6164, 0x005D6192, 0x005D620B, 0x005D6235, 0x005D6288, 0x005D639F, 0x005D644A,
	0x005D6486, 0x005D64CC, 0x005D64E8, 0x005D673D, 0x005D6774, 0x005DC444, 0x005DC4A4, 0x005DC4E3, 0x005DC522, 0x005DC55E,
	0x005DC59A, 0x005DCA1C, 0x005DCE96, 0x005EF07B, 0x005EFC60, 0x005EFC8B, 0x005EFD2A, 0x005F16B7, 0x005F19AE, 0x005F19C2,
	0x005F2E63, 0x005F3F4F, 0x005F4041, 0x005F40B1, 0x005F4DDE, 0x005F4E51, 0x005F5413, 0x005F542E, 0x005F55C3, 0x005F7D15,
	0x005F8B5C, 0x005F8D9C, 0x005F8FD5, 0x005F8FFC, 0x005F9066, 0x005F92E8, 0x005F9864, 0x005F9BA2, 0x005F9C1A, 0x005FACB3,
	0x005FAE96, 0x005FC038, 0x005FC836, 0x005FCF95, 0x005FD1BD, 0x00601A89, 0x00601BBE, 0x00601CF9, 0x00601E54, 0x00601EB4,
	0x00601F11, 0x00602091, 0x006020C7, 0x00602104, 0x0060213E, 0x00602491, 0x006025AA, 0x00602616, 0x006026F3, 0x0060287C,
	0x00602A3D, 0x00602B83, 0x00602EA5, 0x006032E6, 0x00603B2C, 0x00603DB4, 0x00603F43, 0x00604034, 0x0060472C, 0x006048C1,
	0x00604EC3, 0x00605161, 0x00605759, 0x0060586D, 0x006063E2, 0x006063F1, 0x0060679B, 0x006067AA, 0x00606C06, 0x00606C15,
	0x00606CD7, 0x00606CE6, 0x00607F4E, 0x00612785, 0x006137E6, 0x00614254, 0x00615A8C, 0x00615DDE, 0x00615E5A, 0x00615EF4,
	0x00615F70, 0x0061600A, 0x00616086, 0x00616221, 0x00616244, 0x00616267, 0x00616DC0, 0x0061707A, 0x0061AEEE, 0x0061D53A,
	0x0062095D, 0x0062097F, 0x00620C25, 0x006214F9, 0x0062152B, 0x00621591, 0x006215A9, 0x00621605, 0x00621645, 0x00624877,
	0x00624978, 0x006249B1, 0x006249D2, 0x00626F72, 0x00633EF6, 0x0063A727, 0x0063AA06, 0x0063AA5D, 0x0063CA3C, 0x0063D402,
	0x0063D623, 0x0063E994, 0x0063EA5E, 0x0063EF30, 0x0063F4D5, 0x006400BC, 0x0064052F, 0x00640599, 0x00640AC0, 0x00641701,
	0x00641769, 0x0064EA45, 0x00650176, 0x006501C6, 0x00653F1A, 0x00653F91, 0x0065421E, 0x00654299, 0x006543C0, 0x00654418,
	0x006544AA, 0x00657F6A, 0x0065A91D, 0x0065AB28, 0x0065AD57, 0x0065F73A, 0x00660C3C, 0x00665425, 0x006656F5, 0x00666901,
	0x0066724D, 0x0066750A, 0x0066A7CA, 0x0066B115, 0x0066B81E, 0x006794A4, 0x00684110, 0x006905A5, 0x00693BA4, 0x00693C58,
	0x00698CAC, 0x00699A17, 0x00699A40, 0x00699A69, 0x00699ABE, 0x00699B72, 0x0069A650, 0x0069BCAB, 0x0069BCC4, 0x0069BCDD,
	0x0069BFB8, 0x0069C131, 0x0069CC14, 0x0069D557, 0x0069DE2B, 0x0069DE44, 0x0069DE5D, 0x0069E1B1, 0x0069E3D3, 0x0069E851,
	0x006A0186, 0x006A026A, 0x006A033C, 0x006A0400, 0x006A0427, 0x006A047F, 0x006A0FF3, 0x006B2ABE, 0x006CEF16, 0x006D159A,
	0x006D1808, 0x006D1A7E, 0x006D1BD1, 0x006D1E59, 0x006D1F90, 0x006D2017, 0x006D20E3, 0x006D2107, 0x006D2128, 0x006D2148,
	0x006D2169, 0x006D219C, 0x006D21D0, 0x006D2209, 0x006D2241, 0x006D227A, 0x006D22B3, 0x006D22EB, 0x006D2324, 0x006D23B7,
	0x006D23EB, 0x006D241E, 0x006D2452, 0x006D24A0, 0x006D24D4, 0x006D2534, 0x006D264A, 0x006D27BB, 0x006D284C, 0x006D29BD,
	0x006D2A51, 0x006D2B4A, 0x006D2C45, 0x006D2C78, 0x006D2CAC, 0x006D2D76, 0x006D2D97, 0x006D2DB7, 0x006D2DD8, 0x006D2DF9,
	0x006D2E19, 0x006D2E37, 0x006D2E55, 0x006D2E72, 0x006D2E90, 0x006D32F0, 0x006D432C, 0x006D4355, 0x006D437E, 0x006D447F,
	0x006D459D, 0x006D45EF, 0x006D47FD, 0x006D484D, 0x006D487B, 0x006D48FE, 0x006D4945, 0x006D4AD7, 0x006D4B53, 0x006D4BBE,
	0x006D4FB6, 0x006D52C9, 0x006D607C, 0x006D60AE, 0x006D60DF, 0x006D68C8, 0x006D68FB, 0x006D69C0, 0x006D69F3, 0x006D6DF3,
	0x006D6E5B, 0x006D6EC3, 0x006D6F2C, 0x006D6F58, 0x006DFE5E, 0x006DFF8A, 0x006E1241, 0x006E136D, 0x006E15CF, 0x006E165D,
	0x006E1678, 0x006E16C8, 0x006E1866, 0x006E1CF3, 0x006E20EB, 0x006E24F4, 0x006E2517, 0x006E27F5, 0x006E2B20, 0x006E2CAE,
	0x006E3300, 0x006E38E5, 0x006E3E11, 0x006E3EEA, 0x006E64C2, 0x006E6F34, 0x006ECE58, 0x006EF7A6, 0x006F2850, 0x006F2BB0,
	0x006F3BAB, 0x006F3E19, 0x006F40EF, 0x007000D1, 0x00700C93, 0x00700D1D, 0x00700DA7, 0x00700DE4, 0x00700EA5, 0x0070101B,
	0x007014CC, 0x00701D65, 0x00701FB6, 0x007020D2, 0x00703219, 0x00703455, 0x00703B5F, 0x00706001, 0x0070BCE2, 0x0070BCF5,
	0x0070BD08, 0x0074DF8B, 0x0074E06B, 0x0075048A, 0x00750755, 0x007525A3, 0x007525EA, 0x007526B9, 0x007527FE, 0x0075284B,
	0x00752938, 0x00752966, 0x007536D2, 0x007537D4, 0x00753892, 0x00754063, 0x0075408B, 0x00759FDC, 0x0075A00A, 0x0075A07A,
	0x0075A09C, 0x0075A0DF, 0x0075A104, 0x0077F614, 0x0077FE19, 0x007970F4, 0x007E9242, 0x005759AE, 0x00575AA0, 0x00575D20,
	0x00575E9F, 0x00575ED1, 0x00575F1F, 0x00575F51, 0x005760EA, 0x0057A300, 0x0057A321, 0x0057A342, 0x0057A363, 0x0057A381,
	0x0057A39F, 0x0057A3BD, 0x0057A3DB, 0x0057A41F, 0x0057A440, 0x0057A461, 0x0057A482, 0x0057A4A0, 0x0057A4BE, 0x0057A4DC,
	0x0057A4FA, 0x0057A53E, 0x0057A55F, 0x0057A580, 0x0057A5A1, 0x0057A5BF, 0x0057A5DD, 0x0057A5FB, 0x0057A619, 0x0057A65D,
	0x0057A67E, 0x0057A69F, 0x0057A6C0, 0x0057A6DE, 0x0057A6FC, 0x0057A71A, 0x0057A738, 0x0057A77C, 0x0057A79D, 0x0057A7BE,
	0x0057A7DF, 0x0057A7FD, 0x0057A81B, 0x0057A839, 0x0057A857, 0x0057A89B, 0x0057A8BC, 0x0057A8DA, 0x0057A8F8, 0x0057A916,
	0x0057A934, 0x0057A96B, 0x0057A9A2, 0x005968D8, 0x00596A16, 0x00596AD1, 0x00596B89, 0x00597832, 0x0059794E, 0x0059A8A7,
	0x005D559A, 0x005D55E6, 0x00601F6C, 0x006021A0, 0x00628D2B, 0x0062DA12, 0x0062DAC7, 0x0062DCC2, 0x006D5302, 0x006D532F,
	0x006D535C, 0x006D5389, 0x006D53B6, 0x006D53E3, 0x006D5410, 0x006D543D, 0x006D546A, 0x006D5497, 0x006D54C4, 0x006D54F1,
	0x006D551E, 0x006D554B, 0x006D5578, 0x006D55A5, 0x006D55D2, 0x006D55FF, 0x006D562C, 0x006D5659, 0x006D5686, 0x006D6C79,
	0x006D6D24, 0x006D6D56, 0x006E6B34, 0x006E9608, 0x006E9D4C, 0x006E9DC8, 0x0074E09C, 0x0074E0C7, 0x0074E0F4, 0x0074E121,
	0x0074E14C, 0x0074E179
};

void IOnSend::Hook()
{
	Interface<ITools> Tools;

	for (size_t i=0; i < sizeof this->Patches / sizeof(unsigned long); i++)
		Tools->Intercept(ITools::_I_CALL, (void*)IOnSend::Patches[i], Hooks::KSocket::WritePacketAutoCrc, 5);
}