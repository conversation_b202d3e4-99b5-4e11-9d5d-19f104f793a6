<?xml version="1.0" encoding="UTF-8"?>
<EnigmaProject Version="1.60" Modification="560">
  <Compatibility>
    <Level1>True</Level1>
    <Level2>True</Level2>
    <Level3>False</Level3>
    <Level4>False</Level4>
    <Level5>False</Level5>
    <Level6>False</Level6>
    <Level7>False</Level7>
    <Level8>False</Level8>
    <Level9>False</Level9>
  </Compatibility>
  <EnigmaProjectDetails>
    <FullNameOfProject>KalOnline Add-On</FullNameOfProject>
    <ProjectDescription/>
  </EnigmaProjectDetails>
  <KeysGenerator>
    <EnableLogging>False</EnableLogging>
  </KeysGenerator>
  <Input>
    <FileName>Release\Engine.dll</FileName>
    <ProductName>KalOnline Add-On by Zelda</ProductName>
    <VersionInfo/>
    <ProductInfoFromResources>False</ProductInfoFromResources>
  </Input>
  <Output>
    <SameAsInputFile>True</SameAsInputFile>
    <FileName>Release\Engine.dll</FileName>
    <DoNotBackup>True</DoNotBackup>
  </Output>
  <AdvanceInput>
    <ProtectAfter>False</ProtectAfter>
    <Files Count="0"/>
  </AdvanceInput>
  <Compression>
    <Type>Best</Type>
    <Mode>AllResourcesExceptIconAndVersion</Mode>
  </Compression>
  <OnlineServices>
    <Common>
      <IsConfigured>False</IsConfigured>
      <ProductId/>
      <ActivationLinkId/>
      <ActivationUrlHost/>
      <ActivationUrlPort>0</ActivationUrlPort>
      <ActivationUrlEndPoint/>
      <ProductName/>
      <ActivationLinkName/>
      <SiteUrl/>
      <AdministratorLogin/>
      <AdministratorPassword/>
      <TestOrderUrl/>
    </Common>
    <OnlineActivationDialog>
      <Enabled>False</Enabled>
      <ShowOnUnregistered>True</ShowOnUnregistered>
      <ShowSucceededMessage>False</ShowSucceededMessage>
      <ShowFailedMessage>False</ShowFailedMessage>
      <MessageSucceeded>
        <Icon>0</Icon>
        <Title/>
        <Text/>
        <UseHL>False</UseHL>
        <HLSentence/>
        <HLURL/>
        <AutoClose>False</AutoClose>
        <CloseDelay>0</CloseDelay>
        <Disable>False</Disable>
        <DisableDelay>0</DisableDelay>
        <FontName/>
        <FontCharset>0</FontCharset>
        <FontColor>0</FontColor>
        <FontSize>0</FontSize>
        <FontBold>False</FontBold>
        <FontItalic>False</FontItalic>
        <FontUnderline>False</FontUnderline>
        <FontStrikeout>False</FontStrikeout>
      </MessageSucceeded>
      <MessageFailed>
        <Icon>0</Icon>
        <Title/>
        <Text/>
        <UseHL>False</UseHL>
        <HLSentence/>
        <HLURL/>
        <AutoClose>False</AutoClose>
        <CloseDelay>0</CloseDelay>
        <Disable>False</Disable>
        <DisableDelay>0</DisableDelay>
        <FontName/>
        <FontCharset>0</FontCharset>
        <FontColor>0</FontColor>
        <FontSize>0</FontSize>
        <FontBold>False</FontBold>
        <FontItalic>False</FontItalic>
        <FontUnderline>False</FontUnderline>
        <FontStrikeout>False</FontStrikeout>
      </MessageFailed>
      <Form>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</Form>
    </OnlineActivationDialog>
    <OnlineActivationSettings>
      <ActivateEveryExecution>False</ActivateEveryExecution>
      <SystemProxy>False</SystemProxy>
    </OnlineActivationSettings>
  </OnlineServices>
  <RegistrationFeatures>
    <RegistrationDataStoring>
      <Storage>SystemRegistry</Storage>
      <Registry>
        <Base>CurrentUser</Base>
        <Path>\SOFTWARE\KalOnline Add-On\</Path>
      </Registry>
      <FileSystem>
        <Base>MyDocumentsFolder</Base>
        <Path>\KalOnline Add-On\license.dat</Path>
        <SetFileAttributes>False</SetFileAttributes>
        <FileAttributesArchive>False</FileAttributesArchive>
        <FileAttributesReadOnly>True</FileAttributesReadOnly>
        <FileAttributesHidden>False</FileAttributesHidden>
        <FileAttributesSystem>False</FileAttributesSystem>
      </FileSystem>
      <EncryptRegInfo>False</EncryptRegInfo>
      <EncryptWithHardwareId>True</EncryptWithHardwareId>
    </RegistrationDataStoring>
    <Common>
      <Unicode>False</Unicode>
      <AllowHardwareLockedKeys>False</AllowHardwareLockedKeys>
      <AllowTimeLimitedKeys>False</AllowTimeLimitedKeys>
      <AllowExecOnlyIfRegistered>False</AllowExecOnlyIfRegistered>
      <KeyMode>5</KeyMode>
      <KeyBase>4</KeyBase>
      <EncryptApplication>False</EncryptApplication>
      <AllowRegisterAfterKeys>False</AllowRegisterAfterKeys>
      <AllowRegisterBeforeKeys>False</AllowRegisterBeforeKeys>
      <AllowExecutionsKeys>False</AllowExecutionsKeys>
      <AllowDaysKeys>False</AllowDaysKeys>
      <AllowRunTimeKeys>False</AllowRunTimeKeys>
      <AllowGlobalTimeKeys>False</AllowGlobalTimeKeys>
      <AllowCountryLockedKeys>False</AllowCountryLockedKeys>
    </Common>
    <HardwareAssociation>
      <UseVolumeSerialDrive>True</UseVolumeSerialDrive>
      <UseSystemVolumeName>False</UseSystemVolumeName>
      <UseComputerName>False</UseComputerName>
      <UseCPUType>True</UseCPUType>
      <UseWindowsKey>False</UseWindowsKey>
      <UseMotherboard>True</UseMotherboard>
      <UseHDDSerial>True</UseHDDSerial>
      <UseUserName>False</UseUserName>
      <VolumeSerialDriveChange>0</VolumeSerialDriveChange>
      <SystemVolumeNameChange>0</SystemVolumeNameChange>
      <ComputerNameChange>0</ComputerNameChange>
      <CPUTypeChange>0</CPUTypeChange>
      <MotherboardChange>0</MotherboardChange>
      <WindowsKeyChange>0</WindowsKeyChange>
      <HDDSerialChange>0</HDDSerialChange>
      <UserNameChange>0</UserNameChange>
    </HardwareAssociation>
    <Constants>
      <EncryptedConstant>-666593862</EncryptedConstant>
      <Secure1>423BC6A188BF5B85FAC004CF55A88F3E</Secure1>
      <WatermarkConstant>14CDFA785F5D416E0C7EC665D5A88DA7</WatermarkConstant>
      <Mode512>
        <PublicKey/>
        <PrivateKey/>
      </Mode512>
      <Mode768>
        <PublicKey/>
        <PrivateKey/>
      </Mode768>
      <Mode1024>
        <PublicKey>0207A1394CC457FCF5253AE24FF5ECF8A8A0CDBDFCMTKWVDDF4FGHLR94CCN6UJ7TA86MNJ788Q5GRWXNEKA2PE44LWPKXF3A4EXC2L2XUTPLZUY2K82QLWGHRFPJ7RW9226TAYX95RXHXG6WFWRAKEHZV9XCGTLSDLE28NXFPTJDCGH2GUAFRPJP6YRDBNGYUZ5NV5BYSY6SZ6CL5WLDSWJ34GP8QDSHLSRUWM9XZBP2F6KQX0CCZA693MS2MPLMQWT27SKKR6MUJCZ3DACB5QRV2EXNNJYM8ZSFBN74K2DEV6ZHPCAHTZYMVXNHQQZE24ABSWAT6HYTFUGXYVVZX29WAHNNLLMXKRS4HLQPHUBX3VU7SK9F4FPTUGADKBWTAM29W2H6XGJG7BJR9T65AHAZHS32YCYKC7YHV9SUGJJCQ3KRBDWNBB4LQ2CLAK960CDQSSKDDM7XQEM2K5XZJ3SRS9NR8KH679B5VMLF6EGND7T387CBZ77YXWW4JR4N2WT3SFK6BPU2HYF776TDNHUPAC5A6YB7HSPZNRA2WFEJELH7WNHNDPKR9ZMJ72M9UBF3Y7KTEX2WTWWQ4ECUCB5Q268H7HZWVGWFZ2TGSLBC2YH88QY8Y7C3EV7NXRESSYSNEU9MS5KVQXBK</PublicKey>
        <PrivateKey>020FMH5R9XS7EZCTZMSC6B48XJQEGD5MEFX0ADT774LYJ9XBL5DLAKEVQSVK2Z256FKZM3RVLMJSSBW5A3CFJ53W8TYMADJ5LJVWDQE5B9JBDTC3BSP6HZ5XULA3TBCSHCG5RBAPRTHXLAMEA9RYPMNTTP6WS3JCH5XEG3NACJAJTHNXWTBLMW5RG5TR5TPGLCGBWD59RGHWY33T8VJ020362VU5LGUEJANC6GTNBUSCEYTG6FNVA3020362VU5LGUEJANC6GTNBUSCEYTG6FNVA3</PrivateKey>
      </Mode1024>
      <Mode2048>
        <PublicKey/>
        <PrivateKey/>
      </Mode2048>
      <Mode3072>
        <PublicKey/>
        <PrivateKey/>
      </Mode3072>
      <Mode4096>
        <PublicKey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ublicKey>
        <PrivateKey>066VLBPEYFT4SWQYWS42MT8BQ5YMEAGNT6MYQ3G4HS8E7FCY48E3PHQMEGPCEZBEFJTDA7QR82G5RBTCXK8Z7Z2GWMKUSP8RX7XENMPZZ2CDZ5GEPQT33HR4KBUCKHGN9ZKAGKLQT9KZEC36TVTRDNS2XNXSSSDUEDD4WTXAJF84WSAFB73SE8XKMRYG2ZG2YN87SFH7UGGFFF7FCL9E5CP6SW3FXJN35MMA8A587TXSZM46ZH6XRWQAT4CJYEKPVN4F9CDYXBEEXD544ZBE46LV9HKAEJEUK985NQY5PPPCN5JHRVRZA9M3XEF4MWD3HK8RJBVW45WL3LJ84QH4REQ4566JW859PRUC43WPKEKJKJPH9PWV5GX4YLXJA5MLE9LXSWENLRNCJH94UPGMBW7QCWZKYNF5H6V6TZCANC9KF8ZN9Z7JK8L4K8NZKTKNG8BLJLHBPV3H32G3MT4WZCJBZ9BMLL5WSWFX36AVEMATXDJ44TQHHJ4JASEL8MY64X2K7WMUUHBGF9RV6ER9PU5CSPUUZD24M6JQ29U3F52AEMX34QGW3CG8TR7F6EVS3UM5RWTRDPVFZL37NXKXEGD32K7KWG6GD5BPDLV8KW3KZE69DMJBFM3CLPF9UY2BLEHZZ7CYSNQGTEQEW8MRN2G2ZWW46QKDG65GWGYKCFCMQQJSG7ASAFED8UJFWAFLDEJ5GEKQ2KK4LQHZS4PGNZK5AGJ9WCU7MXU44F4URWW9WP3R9FBKJUUM89NR8GRKLM5K7AD7U6NZDZ22LUFX35ZJ9Q9G5ZQES8YD3QER7K2XZTN5GV92VH8SJ067CSUYRLRBAKPPSLYDA7TQKVU7MLG49U8DR4SX5SBKVUDWVZ2Y8T3MS3GSRV27XYQ4XA9VLAW47R56CVZMVZ5W23YF2YKL4NJJJC2VMN7067CSUYRLRBAKPPSLYDA7TQKVU7MLG49U8DR4SX5SBKVUDWVZ2Y8T3MS3GSRV27XYQ4XA9VLAW47R56CVZMVZ5W23YF2YKL4NJJJC2VMN7</PrivateKey>
      </Mode4096>
      <HardwareConstant>7A1394CC457FCF5253AE24FF5ECF8A8A</HardwareConstant>
    </Constants>
    <RegistrationDialog>
      <Form>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</Form>
      <Enabled>False</Enabled>
      <ShowOnTrialExpire>False</ShowOnTrialExpire>
      <ShowOnUnregistered>True</ShowOnUnregistered>
      <ShowCorrectMessage>True</ShowCorrectMessage>
      <ShowIncorrectMessage>True</ShowIncorrectMessage>
      <MessageCorrect>
        <Title>The Registration Succeeded</Title>
        <Text>Thanks for registration!</Text>
        <UseHL>False</UseHL>
        <HLSentence>The Enigma Protector</HLSentence>
        <HLURL>http://www.enigmaprotector.com/</HLURL>
        <Icon>1</Icon>
        <AutoClose>False</AutoClose>
        <CloseDelay>0</CloseDelay>
        <Disable>False</Disable>
        <DisableDelay>0</DisableDelay>
        <FontName/>
        <FontCharset>0</FontCharset>
        <FontColor>0</FontColor>
        <FontSize>0</FontSize>
        <FontBold>False</FontBold>
        <FontItalic>False</FontItalic>
        <FontUnderline>False</FontUnderline>
        <FontStrikeout>False</FontStrikeout>
      </MessageCorrect>
      <MessageIncorrect>
        <Title>The Registration did not succeeded</Title>
        <Text>Your registration information is incorrect!</Text>
        <UseHL>False</UseHL>
        <HLSentence>The Enigma Protector</HLSentence>
        <HLURL>http://www.enigmaprotector.com/</HLURL>
        <Icon>3</Icon>
        <AutoClose>False</AutoClose>
        <CloseDelay>0</CloseDelay>
        <Disable>False</Disable>
        <DisableDelay>0</DisableDelay>
        <FontName/>
        <FontCharset>0</FontCharset>
        <FontColor>0</FontColor>
        <FontSize>0</FontSize>
        <FontBold>False</FontBold>
        <FontItalic>False</FontItalic>
        <FontUnderline>False</FontUnderline>
        <FontStrikeout>False</FontStrikeout>
      </MessageIncorrect>
      <ShowBeforeKeyExpiresInDays>False</ShowBeforeKeyExpiresInDays>
      <ShowBeforeKeyExpiresDays>5</ShowBeforeKeyExpiresDays>
      <ShowBeforeKeyExpiresInExecutions>False</ShowBeforeKeyExpiresInExecutions>
      <ShowBeforeKeyExpiresExecutions>5</ShowBeforeKeyExpiresExecutions>
      <ShowBeforeKeyExpiresInMinutes>False</ShowBeforeKeyExpiresInMinutes>
      <ShowBeforeKeyExpiresMinutes>5</ShowBeforeKeyExpiresMinutes>
    </RegistrationDialog>
    <KeyExpirationReminder>
      <Enabled>False</Enabled>
      <DaysBeforeExpiration>30</DaysBeforeExpiration>
      <Message>
        <Title>Registration Key Expiration Reminder</Title>
        <Text>Your registration key will expire in %DaysToKeyExp% days. 
Please contact to our support team to obtain new one!
Use the link below to visit our web site.</Text>
        <UseHL>False</UseHL>
        <HLSentence>The Enigma Protector</HLSentence>
        <HLURL>http://enigmaprotector.com/</HLURL>
        <Icon>0</Icon>
        <AutoClose>False</AutoClose>
        <CloseDelay>0</CloseDelay>
        <Disable>False</Disable>
        <DisableDelay>0</DisableDelay>
        <FontName/>
        <FontCharset>0</FontCharset>
        <FontColor>0</FontColor>
        <FontSize>0</FontSize>
        <FontBold>False</FontBold>
        <FontItalic>False</FontItalic>
        <FontUnderline>False</FontUnderline>
        <FontStrikeout>False</FontStrikeout>
      </Message>
    </KeyExpirationReminder>
    <EncryptWithHardwareID>
      <Enabled>False</Enabled>
      <HardwareID/>
    </EncryptWithHardwareID>
  </RegistrationFeatures>
  <CheckUp>
    <AntiDebugger>
      <Enabled>False</Enabled>
      <ShowMessageBeforeTermination>True</ShowMessageBeforeTermination>
      <Message>
        <Title>Debugger warning</Title>
        <Text>A debugger is detected on this computer.
Applications such as WinIce/SoftIce, OllyDbg, TWD could be executing.
This program will not execute while debuggers are running.</Text>
        <UseHL>False</UseHL>
        <HLSentence>Click here for help</HLSentence>
        <HLURL>http://www.yourcompany.com/help/</HLURL>
        <Icon>0</Icon>
        <AutoClose>False</AutoClose>
        <CloseDelay>0</CloseDelay>
        <Disable>False</Disable>
        <DisableDelay>0</DisableDelay>
        <FontName/>
        <FontCharset>0</FontCharset>
        <FontColor>0</FontColor>
        <FontSize>0</FontSize>
        <FontBold>False</FontBold>
        <FontItalic>False</FontItalic>
        <FontUnderline>False</FontUnderline>
        <FontStrikeout>False</FontStrikeout>
      </Message>
      <CheckAtRuntime>True</CheckAtRuntime>
    </AntiDebugger>
    <ControlSum>
      <Enabled>False</Enabled>
      <FileIntegrityChecking>True</FileIntegrityChecking>
      <RTIntegrityChecking>True</RTIntegrityChecking>
      <ShowMessageBeforeTermination>True</ShowMessageBeforeTermination>
      <Message>
        <Title>Virus warning</Title>
        <Text>Program integrity check failed. 
Please check for viruses on your computer. 
If program is not infected, it had been patched 
or tampered  - please re-download and re-install. 
This program will now close.</Text>
        <UseHL>False</UseHL>
        <HLSentence>Click here for help</HLSentence>
        <HLURL>http://www.yourcompany.com/help/</HLURL>
        <Icon>0</Icon>
        <AutoClose>False</AutoClose>
        <CloseDelay>0</CloseDelay>
        <Disable>False</Disable>
        <DisableDelay>0</DisableDelay>
        <FontName/>
        <FontCharset>0</FontCharset>
        <FontColor>0</FontColor>
        <FontSize>0</FontSize>
        <FontBold>False</FontBold>
        <FontItalic>False</FontItalic>
        <FontUnderline>False</FontUnderline>
        <FontStrikeout>False</FontStrikeout>
      </Message>
    </ControlSum>
    <StartupPassword>
      <Enabled>False</Enabled>
      <AlwaysAsk>True</AlwaysAsk>
      <AskFirstTime>False</AskFirstTime>
      <AskInDays>False</AskInDays>
      <DaysAfterAsk>30</DaysAfterAsk>
      <HidePassword>True</HidePassword>
      <AllowChange>False</AllowChange>
      <Message>
        <Title>Invalid password</Title>
        <Text>You have entered an invalid startup password.
Try to check it again or contact the software developers:</Text>
        <UseHL>False</UseHL>
        <HLSentence>Click here for registration</HLSentence>
        <HLURL>http://www.yourcompany.com/</HLURL>
        <Icon>0</Icon>
        <AutoClose>False</AutoClose>
        <CloseDelay>0</CloseDelay>
        <Disable>False</Disable>
        <DisableDelay>0</DisableDelay>
        <FontName/>
        <FontCharset>0</FontCharset>
        <FontColor>0</FontColor>
        <FontSize>0</FontSize>
        <FontBold>False</FontBold>
        <FontItalic>False</FontItalic>
        <FontUnderline>False</FontUnderline>
        <FontStrikeout>False</FontStrikeout>
      </Message>
      <Passwords Count="0"/>
    </StartupPassword>
    <FileName>
      <Enabled>False</Enabled>
      <OriginalFileName>notepad_protected.exe</OriginalFileName>
      <ShowMessageBeforeTermination>True</ShowMessageBeforeTermination>
      <Message>
        <Title>Invalid File Name</Title>
        <Text>The software&apos;s file name had been changed.  
Please restore the original file name. 
This program will now close.</Text>
        <UseHL>False</UseHL>
        <HLSentence>Press here to download official version!</HLSentence>
        <HLURL>http://www.yourcompany.com/downloads/</HLURL>
        <Icon>0</Icon>
        <AutoClose>False</AutoClose>
        <CloseDelay>0</CloseDelay>
        <Disable>False</Disable>
        <DisableDelay>0</DisableDelay>
        <FontName/>
        <FontCharset>0</FontCharset>
        <FontColor>0</FontColor>
        <FontSize>0</FontSize>
        <FontBold>False</FontBold>
        <FontItalic>False</FontItalic>
        <FontUnderline>False</FontUnderline>
        <FontStrikeout>False</FontStrikeout>
      </Message>
      <TakeProtectedName>True</TakeProtectedName>
    </FileName>
    <DiskDrive>
      <Enabled>False</Enabled>
      <Selection>
        <Removable>True</Removable>
        <Fixed>True</Fixed>
        <Remote>True</Remote>
        <CDROM>True</CDROM>
        <RAMDisk>True</RAMDisk>
      </Selection>
      <ShowMessageBeforeTermination>True</ShowMessageBeforeTermination>
      <Message>
        <Title>Invalid drive type</Title>
        <Text>The software has checked impossible disk drive type!
Try to contact to developers or download newest version of this software at:</Text>
        <UseHL>False</UseHL>
        <HLSentence>Click here for newest version!</HLSentence>
        <HLURL>http://www.yourcompany.com/downloads/</HLURL>
        <Icon>0</Icon>
        <AutoClose>False</AutoClose>
        <CloseDelay>0</CloseDelay>
        <Disable>False</Disable>
        <DisableDelay>0</DisableDelay>
        <FontName/>
        <FontCharset>0</FontCharset>
        <FontColor>0</FontColor>
        <FontSize>0</FontSize>
        <FontBold>False</FontBold>
        <FontItalic>False</FontItalic>
        <FontUnderline>False</FontUnderline>
        <FontStrikeout>False</FontStrikeout>
      </Message>
    </DiskDrive>
    <ExecutedCopies>
      <Enabled>False</Enabled>
      <AllowCopies>1</AllowCopies>
      <ShowMessageIfCopiesCountIsExceeded>True</ShowMessageIfCopiesCountIsExceeded>
      <Message>
        <Title>Warning</Title>
        <Text>You have exceeded maximal number of executed copies. 
Application won&apos;t be started.</Text>
        <UseHL>False</UseHL>
        <HLSentence>Software information</HLSentence>
        <HLURL>http://www.yourcompany.com/</HLURL>
        <Icon>0</Icon>
        <AutoClose>False</AutoClose>
        <CloseDelay>0</CloseDelay>
        <Disable>False</Disable>
        <DisableDelay>0</DisableDelay>
        <FontName/>
        <FontCharset>0</FontCharset>
        <FontColor>0</FontColor>
        <FontSize>0</FontSize>
        <FontBold>False</FontBold>
        <FontItalic>False</FontItalic>
        <FontUnderline>False</FontUnderline>
        <FontStrikeout>False</FontStrikeout>
      </Message>
      <TerminateExecution>True</TerminateExecution>
    </ExecutedCopies>
    <UserLanguage>
      <Enabled>False</Enabled>
      <ALB>True</ALB>
      <DZA>True</DZA>
      <ARG>True</ARG>
      <ARM>True</ARM>
      <AUS>True</AUS>
      <AUT>True</AUT>
      <AZE>True</AZE>
      <BHR>True</BHR>
      <BLR>True</BLR>
      <BEL>True</BEL>
      <BLZ>True</BLZ>
      <BOL>True</BOL>
      <BRA>True</BRA>
      <BRN>True</BRN>
      <BGR>True</BGR>
      <KHM>True</KHM>
      <CAN>True</CAN>
      <CAF>True</CAF>
      <TCD>True</TCD>
      <CHL>True</CHL>
      <CHN>True</CHN>
      <COL>True</COL>
      <CRI>True</CRI>
      <HRV>True</HRV>
      <CZE>True</CZE>
      <DNK>True</DNK>
      <DOM>True</DOM>
      <ECU>True</ECU>
      <EGY>True</EGY>
      <SLV>True</SLV>
      <EST>True</EST>
      <FRO>True</FRO>
      <FIN>True</FIN>
      <FRA>True</FRA>
      <GEO>True</GEO>
      <DEU>True</DEU>
      <GRC>True</GRC>
      <GTM>True</GTM>
      <HND>True</HND>
      <HKG>True</HKG>
      <HUN>True</HUN>
      <ISL>True</ISL>
      <IND>True</IND>
      <IDN>True</IDN>
      <IRN>True</IRN>
      <IRQ>True</IRQ>
      <IRL>True</IRL>
      <ISR>True</ISR>
      <ITA>True</ITA>
      <JAM>True</JAM>
      <JPN>True</JPN>
      <JOR>True</JOR>
      <KAZ>True</KAZ>
      <KEN>True</KEN>
      <PRK>True</PRK>
      <KOR>True</KOR>
      <KWT>True</KWT>
      <KGZ>True</KGZ>
      <LVA>True</LVA>
      <LBN>True</LBN>
      <LBR>True</LBR>
      <LIE>True</LIE>
      <LTU>True</LTU>
      <LUX>True</LUX>
      <MAC>True</MAC>
      <MKD>True</MKD>
      <MYS>True</MYS>
      <MEX>True</MEX>
      <MDA>True</MDA>
      <MCO>True</MCO>
      <MNG>True</MNG>
      <MAR>True</MAR>
      <NLD>True</NLD>
      <NZL>True</NZL>
      <NIC>True</NIC>
      <NOR>True</NOR>
      <OMN>True</OMN>
      <PAK>True</PAK>
      <PAN>True</PAN>
      <PRY>True</PRY>
      <PER>True</PER>
      <PHL>True</PHL>
      <POL>True</POL>
      <PRT>True</PRT>
      <PRI>True</PRI>
      <QAT>True</QAT>
      <ROU>True</ROU>
      <RUS>True</RUS>
      <SAU>True</SAU>
      <SCG>True</SCG>
      <SGP>True</SGP>
      <SVK>True</SVK>
      <SVN>True</SVN>
      <ZAF>True</ZAF>
      <ESP>True</ESP>
      <SWE>True</SWE>
      <CHE>True</CHE>
      <TJK>True</TJK>
      <THA>True</THA>
      <TTO>True</TTO>
      <TUN>True</TUN>
      <TUR>True</TUR>
      <TWN>True</TWN>
      <UKR>True</UKR>
      <ARE>True</ARE>
      <GBR>True</GBR>
      <USA>True</USA>
      <URY>True</URY>
      <UZB>True</UZB>
      <VEN>True</VEN>
      <VNM>True</VNM>
      <YEM>True</YEM>
      <ZWE>True</ZWE>
      <AFG>True</AFG>
      <BGD>True</BGD>
      <BIH>True</BIH>
      <CAR>True</CAR>
      <ETH>True</ETH>
      <GRL>True</GRL>
      <LAO>True</LAO>
      <LBY>True</LBY>
      <MDV>True</MDV>
      <MLT>True</MLT>
      <MNE>True</MNE>
      <NEP>True</NEP>
      <NGA>True</NGA>
      <ROM>True</ROM>
      <RWA>True</RWA>
      <SEN>True</SEN>
      <SRB>True</SRB>
      <LKA>True</LKA>
      <SYR>True</SYR>
      <TAJ>True</TAJ>
      <TKM>True</TKM>
      <ShowMessageBeforeTermination>True</ShowMessageBeforeTermination>
      <Message>
        <Title>Warning</Title>
        <Text>You have not permissions to start this application!
Please, contact to developers.</Text>
        <UseHL>False</UseHL>
        <HLSentence>Software information</HLSentence>
        <HLURL>http://www.yourcompany.com/</HLURL>
        <Icon>0</Icon>
        <AutoClose>False</AutoClose>
        <CloseDelay>0</CloseDelay>
        <Disable>False</Disable>
        <DisableDelay>0</DisableDelay>
        <FontName/>
        <FontCharset>0</FontCharset>
        <FontColor>0</FontColor>
        <FontSize>0</FontSize>
        <FontBold>False</FontBold>
        <FontItalic>False</FontItalic>
        <FontUnderline>False</FontUnderline>
        <FontStrikeout>False</FontStrikeout>
      </Message>
    </UserLanguage>
    <ExternalFiles>
      <Enabled>False</Enabled>
      <ShowMessageBeforeTermination>True</ShowMessageBeforeTermination>
      <Message>
        <Title>Warning</Title>
        <Text>The installation package had beed modified! 
File: %CU_EXTFILES% is corrupted!
Please reinstall application!</Text>
        <UseHL>False</UseHL>
        <HLSentence>Enigma Protector</HLSentence>
        <HLURL>http://www.enigmaprotector.com/</HLURL>
        <Icon>0</Icon>
        <AutoClose>False</AutoClose>
        <CloseDelay>0</CloseDelay>
        <Disable>False</Disable>
        <DisableDelay>0</DisableDelay>
        <FontName/>
        <FontCharset>0</FontCharset>
        <FontColor>0</FontColor>
        <FontSize>0</FontSize>
        <FontBold>False</FontBold>
        <FontItalic>False</FontItalic>
        <FontUnderline>False</FontUnderline>
        <FontStrikeout>False</FontStrikeout>
      </Message>
      <Files/>
    </ExternalFiles>
    <ExecutedProcesses>
      <Enabled>False</Enabled>
      <ShowMessageBeforeTermination>True</ShowMessageBeforeTermination>
      <Message>
        <Title>Warning</Title>
        <Text>Spyware tool is executed on this PC. 
Please close the application %CU_EXECPR% try again!</Text>
        <UseHL>False</UseHL>
        <HLSentence>Enigma Protector</HLSentence>
        <HLURL>http://www.enigmaprotector.com/</HLURL>
        <Icon>0</Icon>
        <AutoClose>False</AutoClose>
        <CloseDelay>0</CloseDelay>
        <Disable>False</Disable>
        <DisableDelay>0</DisableDelay>
        <FontName/>
        <FontCharset>0</FontCharset>
        <FontColor>0</FontColor>
        <FontSize>0</FontSize>
        <FontBold>False</FontBold>
        <FontItalic>False</FontItalic>
        <FontUnderline>False</FontUnderline>
        <FontStrikeout>False</FontStrikeout>
      </Message>
      <CheckEndless>True</CheckEndless>
      <CheckDelay>2</CheckDelay>
      <CheckHiddenProcess>False</CheckHiddenProcess>
      <Processes Count="0"/>
    </ExecutedProcesses>
    <LoadedDrivers>
      <Enabled>False</Enabled>
      <ShowMessageBeforeTermination>False</ShowMessageBeforeTermination>
      <Message>
        <Title>Warning</Title>
        <Text>Spyware tool is executed on this PC. 
Please close this tool try again!</Text>
        <UseHL>False</UseHL>
        <HLSentence>Enigma Protector</HLSentence>
        <HLURL>http://www.enigmaprotector.com/</HLURL>
        <Icon>0</Icon>
        <AutoClose>False</AutoClose>
        <CloseDelay>0</CloseDelay>
        <Disable>False</Disable>
        <DisableDelay>0</DisableDelay>
        <FontName/>
        <FontCharset>0</FontCharset>
        <FontColor>0</FontColor>
        <FontSize>0</FontSize>
        <FontBold>False</FontBold>
        <FontItalic>False</FontItalic>
        <FontUnderline>False</FontUnderline>
        <FontStrikeout>False</FontStrikeout>
      </Message>
      <Drivers Count="0"/>
    </LoadedDrivers>
    <InstalledServices>
      <Enabled>False</Enabled>
      <ShowMessageBeforeTermination>True</ShowMessageBeforeTermination>
      <Message>
        <Title>Warning</Title>
        <Text>Spyware tool is installed on this PC. 
Uninstall service &quot;%CU_INSTSERV%&quot; and try again!</Text>
        <UseHL>False</UseHL>
        <HLSentence>Enigma Protector</HLSentence>
        <HLURL>http://www.enigmaprotector.com/</HLURL>
        <Icon>0</Icon>
        <AutoClose>False</AutoClose>
        <CloseDelay>0</CloseDelay>
        <Disable>False</Disable>
        <DisableDelay>0</DisableDelay>
        <FontName/>
        <FontCharset>0</FontCharset>
        <FontColor>0</FontColor>
        <FontSize>0</FontSize>
        <FontBold>False</FontBold>
        <FontItalic>False</FontItalic>
        <FontUnderline>False</FontUnderline>
        <FontStrikeout>False</FontStrikeout>
      </Message>
      <Services Count="0"/>
    </InstalledServices>
    <WindowsVersion>
      <Enabled>False</Enabled>
      <WIN_VER_WINDOWS95>False</WIN_VER_WINDOWS95>
      <WIN_VER_WINDOWS95OSR2>False</WIN_VER_WINDOWS95OSR2>
      <WIN_VER_WINDOWS98>False</WIN_VER_WINDOWS98>
      <WIN_VER_WINDOWS98SE>False</WIN_VER_WINDOWS98SE>
      <WIN_VER_WINDOWSME>False</WIN_VER_WINDOWSME>
      <WIN_VER_WINDOWS2000>False</WIN_VER_WINDOWS2000>
      <WIN_VER_WINDOWS2000PROF>False</WIN_VER_WINDOWS2000PROF>
      <WIN_VER_WINDOWS2000DATASERVER>False</WIN_VER_WINDOWS2000DATASERVER>
      <WIN_VER_WINDOWS2000ADVSERVER>False</WIN_VER_WINDOWS2000ADVSERVER>
      <WIN_VER_WINDOWS2000SERVER>False</WIN_VER_WINDOWS2000SERVER>
      <WIN_VER_WINDOWSXP>False</WIN_VER_WINDOWSXP>
      <WIN_VER_WINDOWSXPHOME>False</WIN_VER_WINDOWSXPHOME>
      <WIN_VER_WINDOWSXPPROF>False</WIN_VER_WINDOWSXPPROF>
      <WIN_VER_WINDOWSXPPROFx64>False</WIN_VER_WINDOWSXPPROFx64>
      <WIN_VER_WINDOWSXPPROFDATACENTERx64>False</WIN_VER_WINDOWSXPPROFDATACENTERx64>
      <WIN_VER_WINDOWSXPPROFENERPRICEx64>False</WIN_VER_WINDOWSXPPROFENERPRICEx64>
      <WIN_VER_WINDOWSXPPROFSTANDARTx64>False</WIN_VER_WINDOWSXPPROFSTANDARTx64>
      <WIN_VER_WINDOWS2003SERVER>False</WIN_VER_WINDOWS2003SERVER>
      <WIN_VER_WINDOWS2003SERVERR2>False</WIN_VER_WINDOWS2003SERVERR2>
      <WIN_VER_WINDOWS2003STORAGESERVER>False</WIN_VER_WINDOWS2003STORAGESERVER>
      <WIN_VER_WINDOWS2003DATACENTERITANIUM>False</WIN_VER_WINDOWS2003DATACENTERITANIUM>
      <WIN_VER_WINDOWS2003ENTERPRICEITANIUM>False</WIN_VER_WINDOWS2003ENTERPRICEITANIUM>
      <WIN_VER_WINDOWS2003DATACENTERx64>False</WIN_VER_WINDOWS2003DATACENTERx64>
      <WIN_VER_WINDOWS2003ENERPRICEx64>False</WIN_VER_WINDOWS2003ENERPRICEx64>
      <WIN_VER_WINDOWS2003STANDARTx64>False</WIN_VER_WINDOWS2003STANDARTx64>
      <WIN_VER_WINDOWS2003COMPUTE>False</WIN_VER_WINDOWS2003COMPUTE>
      <WIN_VER_WINDOWS2003DATACENTER>False</WIN_VER_WINDOWS2003DATACENTER>
      <WIN_VER_WINDOWS2003ENTERPRICE>False</WIN_VER_WINDOWS2003ENTERPRICE>
      <WIN_VER_WINDOWS2003WEB>False</WIN_VER_WINDOWS2003WEB>
      <WIN_VER_WINDOWS2003STANDART>False</WIN_VER_WINDOWS2003STANDART>
      <WIN_VER_WINDOWSVISTA>False</WIN_VER_WINDOWSVISTA>
      <WIN_VER_WINDOWSVISTA_BUSINESS>False</WIN_VER_WINDOWSVISTA_BUSINESS>
      <WIN_VER_WINDOWSVISTA_CLUSTER_SERVER>False</WIN_VER_WINDOWSVISTA_CLUSTER_SERVER>
      <WIN_VER_WINDOWSVISTA_DATACENTER_SERVER>False</WIN_VER_WINDOWSVISTA_DATACENTER_SERVER>
      <WIN_VER_WINDOWSVISTA_DATACENTER_SERVER_CORE>False</WIN_VER_WINDOWSVISTA_DATACENTER_SERVER_CORE>
      <WIN_VER_WINDOWSVISTA_DATACENTER_SERVER_CORE_V>False</WIN_VER_WINDOWSVISTA_DATACENTER_SERVER_CORE_V>
      <WIN_VER_WINDOWSVISTA_DATACENTER_SERVER_V>False</WIN_VER_WINDOWSVISTA_DATACENTER_SERVER_V>
      <WIN_VER_WINDOWSVISTA_ENTERPRICE>False</WIN_VER_WINDOWSVISTA_ENTERPRICE>
      <WIN_VER_WINDOWSVISTA_ENTERPRISE_SERVER>False</WIN_VER_WINDOWSVISTA_ENTERPRISE_SERVER>
      <WIN_VER_WINDOWSVISTA_ENTERPRISE_SERVER_CORE>False</WIN_VER_WINDOWSVISTA_ENTERPRISE_SERVER_CORE>
      <WIN_VER_WINDOWSVISTA_ENTERPRISE_SERVER_V>False</WIN_VER_WINDOWSVISTA_ENTERPRISE_SERVER_V>
      <WIN_VER_WINDOWSVISTA_ENTERPRISE_SERVER_CORE_V>False</WIN_VER_WINDOWSVISTA_ENTERPRISE_SERVER_CORE_V>
      <WIN_VER_WINDOWSVISTA_ENTERPRISE_SERVER_IA64>False</WIN_VER_WINDOWSVISTA_ENTERPRISE_SERVER_IA64>
      <WIN_VER_WINDOWSVISTA_HOME_BASIC>False</WIN_VER_WINDOWSVISTA_HOME_BASIC>
      <WIN_VER_WINDOWSVISTA_HOME_PREMIUM>False</WIN_VER_WINDOWSVISTA_HOME_PREMIUM>
      <WIN_VER_WINDOWSVISTA_HOME_SERVER>False</WIN_VER_WINDOWSVISTA_HOME_SERVER>
      <WIN_VER_WINDOWSVISTA_SERVER_FOR_SMALLBUSINESS>False</WIN_VER_WINDOWSVISTA_SERVER_FOR_SMALLBUSINESS>
      <WIN_VER_WINDOWSVISTA_SMALLBUSINESS_SERVER>False</WIN_VER_WINDOWSVISTA_SMALLBUSINESS_SERVER>
      <WIN_VER_WINDOWSVISTA_SMALLBUSINESS_SERVER_PREMIUM>False</WIN_VER_WINDOWSVISTA_SMALLBUSINESS_SERVER_PREMIUM>
      <WIN_VER_WINDOWSVISTA_MEDIUMBUSINESS_SERVER_MANAGEMENT>False</WIN_VER_WINDOWSVISTA_MEDIUMBUSINESS_SERVER_MANAGEMENT>
      <WIN_VER_WINDOWSVISTA_MEDIUMBUSINESS_SERVER_MESSAGING>False</WIN_VER_WINDOWSVISTA_MEDIUMBUSINESS_SERVER_MESSAGING>
      <WIN_VER_WINDOWSVISTA_MEDIUMBUSINESS_SERVER_SECURITY>False</WIN_VER_WINDOWSVISTA_MEDIUMBUSINESS_SERVER_SECURITY>
      <WIN_VER_WINDOWSVISTA_STANDARD_SERVER>False</WIN_VER_WINDOWSVISTA_STANDARD_SERVER>
      <WIN_VER_WINDOWSVISTA_STANDARD_SERVER_V>False</WIN_VER_WINDOWSVISTA_STANDARD_SERVER_V>
      <WIN_VER_WINDOWSVISTA_STANDARD_SERVER_CORE>False</WIN_VER_WINDOWSVISTA_STANDARD_SERVER_CORE>
      <WIN_VER_WINDOWSVISTA_STANDARD_SERVER_CORE_V>False</WIN_VER_WINDOWSVISTA_STANDARD_SERVER_CORE_V>
      <WIN_VER_WINDOWSVISTA_STARTER>False</WIN_VER_WINDOWSVISTA_STARTER>
      <WIN_VER_WINDOWSVISTA_STORAGE_ENTERPRISE_SERVER>False</WIN_VER_WINDOWSVISTA_STORAGE_ENTERPRISE_SERVER>
      <WIN_VER_WINDOWSVISTA_STORAGE_EXPRESS_SERVER>False</WIN_VER_WINDOWSVISTA_STORAGE_EXPRESS_SERVER>
      <WIN_VER_WINDOWSVISTA_STORAGE_STANDARD_SERVER>False</WIN_VER_WINDOWSVISTA_STORAGE_STANDARD_SERVER>
      <WIN_VER_WINDOWSVISTA_STORAGE_WORKGROUP_SERVER>False</WIN_VER_WINDOWSVISTA_STORAGE_WORKGROUP_SERVER>
      <WIN_VER_WINDOWSVISTA_UNDEFINED>False</WIN_VER_WINDOWSVISTA_UNDEFINED>
      <WIN_VER_WINDOWSVISTA_ULTIMATE>False</WIN_VER_WINDOWSVISTA_ULTIMATE>
      <WIN_VER_WINDOWSVISTA_WEB_SERVER>False</WIN_VER_WINDOWSVISTA_WEB_SERVER>
      <WIN_VER_WINDOWSVISTA_WEB_SERVER_CORE>False</WIN_VER_WINDOWSVISTA_WEB_SERVER_CORE>
      <WIN_VER_WINDOWSVISTA_UNLICENSED>False</WIN_VER_WINDOWSVISTA_UNLICENSED>
      <WIN_VER_WINDOWS2008>False</WIN_VER_WINDOWS2008>
      <WIN_VER_WINDOWS2008_BUSINESS>False</WIN_VER_WINDOWS2008_BUSINESS>
      <WIN_VER_WINDOWS2008_CLUSTER_SERVER>False</WIN_VER_WINDOWS2008_CLUSTER_SERVER>
      <WIN_VER_WINDOWS2008_DATACENTER_SERVER>False</WIN_VER_WINDOWS2008_DATACENTER_SERVER>
      <WIN_VER_WINDOWS2008_DATACENTER_SERVER_CORE>False</WIN_VER_WINDOWS2008_DATACENTER_SERVER_CORE>
      <WIN_VER_WINDOWS2008_DATACENTER_SERVER_CORE_V>False</WIN_VER_WINDOWS2008_DATACENTER_SERVER_CORE_V>
      <WIN_VER_WINDOWS2008_DATACENTER_SERVER_V>False</WIN_VER_WINDOWS2008_DATACENTER_SERVER_V>
      <WIN_VER_WINDOWS2008_ENTERPRICE>False</WIN_VER_WINDOWS2008_ENTERPRICE>
      <WIN_VER_WINDOWS2008_ENTERPRISE_SERVER>False</WIN_VER_WINDOWS2008_ENTERPRISE_SERVER>
      <WIN_VER_WINDOWS2008_ENTERPRISE_SERVER_CORE>False</WIN_VER_WINDOWS2008_ENTERPRISE_SERVER_CORE>
      <WIN_VER_WINDOWS2008_ENTERPRISE_SERVER_V>False</WIN_VER_WINDOWS2008_ENTERPRISE_SERVER_V>
      <WIN_VER_WINDOWS2008_ENTERPRISE_SERVER_CORE_V>False</WIN_VER_WINDOWS2008_ENTERPRISE_SERVER_CORE_V>
      <WIN_VER_WINDOWS2008_ENTERPRISE_SERVER_IA64>False</WIN_VER_WINDOWS2008_ENTERPRISE_SERVER_IA64>
      <WIN_VER_WINDOWS2008_HOME_BASIC>False</WIN_VER_WINDOWS2008_HOME_BASIC>
      <WIN_VER_WINDOWS2008_HOME_PREMIUM>False</WIN_VER_WINDOWS2008_HOME_PREMIUM>
      <WIN_VER_WINDOWS2008_HOME_SERVER>False</WIN_VER_WINDOWS2008_HOME_SERVER>
      <WIN_VER_WINDOWS2008_SERVER_FOR_SMALLBUSINESS>False</WIN_VER_WINDOWS2008_SERVER_FOR_SMALLBUSINESS>
      <WIN_VER_WINDOWS2008_SMALLBUSINESS_SERVER>False</WIN_VER_WINDOWS2008_SMALLBUSINESS_SERVER>
      <WIN_VER_WINDOWS2008_SMALLBUSINESS_SERVER_PREMIUM>False</WIN_VER_WINDOWS2008_SMALLBUSINESS_SERVER_PREMIUM>
      <WIN_VER_WINDOWS2008_MEDIUMBUSINESS_SERVER_MANAGEMENT>False</WIN_VER_WINDOWS2008_MEDIUMBUSINESS_SERVER_MANAGEMENT>
      <WIN_VER_WINDOWS2008_MEDIUMBUSINESS_SERVER_MESSAGING>False</WIN_VER_WINDOWS2008_MEDIUMBUSINESS_SERVER_MESSAGING>
      <WIN_VER_WINDOWS2008_MEDIUMBUSINESS_SERVER_SECURITY>False</WIN_VER_WINDOWS2008_MEDIUMBUSINESS_SERVER_SECURITY>
      <WIN_VER_WINDOWS2008_STANDARD_SERVER>False</WIN_VER_WINDOWS2008_STANDARD_SERVER>
      <WIN_VER_WINDOWS2008_STANDARD_SERVER_V>False</WIN_VER_WINDOWS2008_STANDARD_SERVER_V>
      <WIN_VER_WINDOWS2008_STANDARD_SERVER_CORE>False</WIN_VER_WINDOWS2008_STANDARD_SERVER_CORE>
      <WIN_VER_WINDOWS2008_STANDARD_SERVER_CORE_V>False</WIN_VER_WINDOWS2008_STANDARD_SERVER_CORE_V>
      <WIN_VER_WINDOWS2008_STARTER>False</WIN_VER_WINDOWS2008_STARTER>
      <WIN_VER_WINDOWS2008_STORAGE_ENTERPRISE_SERVER>False</WIN_VER_WINDOWS2008_STORAGE_ENTERPRISE_SERVER>
      <WIN_VER_WINDOWS2008_STORAGE_EXPRESS_SERVER>False</WIN_VER_WINDOWS2008_STORAGE_EXPRESS_SERVER>
      <WIN_VER_WINDOWS2008_STORAGE_STANDARD_SERVER>False</WIN_VER_WINDOWS2008_STORAGE_STANDARD_SERVER>
      <WIN_VER_WINDOWS2008_STORAGE_WORKGROUP_SERVER>False</WIN_VER_WINDOWS2008_STORAGE_WORKGROUP_SERVER>
      <WIN_VER_WINDOWS2008_UNDEFINED>False</WIN_VER_WINDOWS2008_UNDEFINED>
      <WIN_VER_WINDOWS2008_ULTIMATE>False</WIN_VER_WINDOWS2008_ULTIMATE>
      <WIN_VER_WINDOWS2008_WEB_SERVER>False</WIN_VER_WINDOWS2008_WEB_SERVER>
      <WIN_VER_WINDOWS2008_WEB_SERVER_CORE>False</WIN_VER_WINDOWS2008_WEB_SERVER_CORE>
      <WIN_VER_WINDOWS2008_UNLICENSED>False</WIN_VER_WINDOWS2008_UNLICENSED>
      <WIN_VER_WINDOWS2008R2>False</WIN_VER_WINDOWS2008R2>
      <WIN_VER_WINDOWS2008R2_BUSINESS>False</WIN_VER_WINDOWS2008R2_BUSINESS>
      <WIN_VER_WINDOWS2008R2_CLUSTER_SERVER>False</WIN_VER_WINDOWS2008R2_CLUSTER_SERVER>
      <WIN_VER_WINDOWS2008R2_DATACENTER_SERVER>False</WIN_VER_WINDOWS2008R2_DATACENTER_SERVER>
      <WIN_VER_WINDOWS2008R2_DATACENTER_SERVER_CORE>False</WIN_VER_WINDOWS2008R2_DATACENTER_SERVER_CORE>
      <WIN_VER_WINDOWS2008R2_DATACENTER_SERVER_CORE_V>False</WIN_VER_WINDOWS2008R2_DATACENTER_SERVER_CORE_V>
      <WIN_VER_WINDOWS2008R2_DATACENTER_SERVER_V>False</WIN_VER_WINDOWS2008R2_DATACENTER_SERVER_V>
      <WIN_VER_WINDOWS2008R2_ENTERPRICE>False</WIN_VER_WINDOWS2008R2_ENTERPRICE>
      <WIN_VER_WINDOWS2008R2_ENTERPRISE_SERVER>False</WIN_VER_WINDOWS2008R2_ENTERPRISE_SERVER>
      <WIN_VER_WINDOWS2008R2_ENTERPRISE_SERVER_CORE>False</WIN_VER_WINDOWS2008R2_ENTERPRISE_SERVER_CORE>
      <WIN_VER_WINDOWS2008R2_ENTERPRISE_SERVER_V>False</WIN_VER_WINDOWS2008R2_ENTERPRISE_SERVER_V>
      <WIN_VER_WINDOWS2008R2_ENTERPRISE_SERVER_CORE_V>False</WIN_VER_WINDOWS2008R2_ENTERPRISE_SERVER_CORE_V>
      <WIN_VER_WINDOWS2008R2_ENTERPRISE_SERVER_IA64>False</WIN_VER_WINDOWS2008R2_ENTERPRISE_SERVER_IA64>
      <WIN_VER_WINDOWS2008R2_HOME_BASIC>False</WIN_VER_WINDOWS2008R2_HOME_BASIC>
      <WIN_VER_WINDOWS2008R2_HOME_PREMIUM>False</WIN_VER_WINDOWS2008R2_HOME_PREMIUM>
      <WIN_VER_WINDOWS2008R2_HOME_SERVER>False</WIN_VER_WINDOWS2008R2_HOME_SERVER>
      <WIN_VER_WINDOWS2008R2_SERVER_FOR_SMALLBUSINESS>False</WIN_VER_WINDOWS2008R2_SERVER_FOR_SMALLBUSINESS>
      <WIN_VER_WINDOWS2008R2_SMALLBUSINESS_SERVER>False</WIN_VER_WINDOWS2008R2_SMALLBUSINESS_SERVER>
      <WIN_VER_WINDOWS2008R2_SMALLBUSINESS_SERVER_PREMIUM>False</WIN_VER_WINDOWS2008R2_SMALLBUSINESS_SERVER_PREMIUM>
      <WIN_VER_WINDOWS2008R2_MEDIUMBUSINESS_SERVER_MANAGEMENT>False</WIN_VER_WINDOWS2008R2_MEDIUMBUSINESS_SERVER_MANAGEMENT>
      <WIN_VER_WINDOWS2008R2_MEDIUMBUSINESS_SERVER_MESSAGING>False</WIN_VER_WINDOWS2008R2_MEDIUMBUSINESS_SERVER_MESSAGING>
      <WIN_VER_WINDOWS2008R2_MEDIUMBUSINESS_SERVER_SECURITY>False</WIN_VER_WINDOWS2008R2_MEDIUMBUSINESS_SERVER_SECURITY>
      <WIN_VER_WINDOWS2008R2_STANDARD_SERVER>False</WIN_VER_WINDOWS2008R2_STANDARD_SERVER>
      <WIN_VER_WINDOWS2008R2_STANDARD_SERVER_V>False</WIN_VER_WINDOWS2008R2_STANDARD_SERVER_V>
      <WIN_VER_WINDOWS2008R2_STANDARD_SERVER_CORE>False</WIN_VER_WINDOWS2008R2_STANDARD_SERVER_CORE>
      <WIN_VER_WINDOWS2008R2_STANDARD_SERVER_CORE_V>False</WIN_VER_WINDOWS2008R2_STANDARD_SERVER_CORE_V>
      <WIN_VER_WINDOWS2008R2_STARTER>False</WIN_VER_WINDOWS2008R2_STARTER>
      <WIN_VER_WINDOWS2008R2_STORAGE_ENTERPRISE_SERVER>False</WIN_VER_WINDOWS2008R2_STORAGE_ENTERPRISE_SERVER>
      <WIN_VER_WINDOWS2008R2_STORAGE_EXPRESS_SERVER>False</WIN_VER_WINDOWS2008R2_STORAGE_EXPRESS_SERVER>
      <WIN_VER_WINDOWS2008R2_STORAGE_STANDARD_SERVER>False</WIN_VER_WINDOWS2008R2_STORAGE_STANDARD_SERVER>
      <WIN_VER_WINDOWS2008R2_STORAGE_WORKGROUP_SERVER>False</WIN_VER_WINDOWS2008R2_STORAGE_WORKGROUP_SERVER>
      <WIN_VER_WINDOWS2008R2_UNDEFINED>False</WIN_VER_WINDOWS2008R2_UNDEFINED>
      <WIN_VER_WINDOWS2008R2_ULTIMATE>False</WIN_VER_WINDOWS2008R2_ULTIMATE>
      <WIN_VER_WINDOWS2008R2_WEB_SERVER>False</WIN_VER_WINDOWS2008R2_WEB_SERVER>
      <WIN_VER_WINDOWS2008R2_WEB_SERVER_CORE>False</WIN_VER_WINDOWS2008R2_WEB_SERVER_CORE>
      <WIN_VER_WINDOWS2008R2_UNLICENSED>False</WIN_VER_WINDOWS2008R2_UNLICENSED>
      <WIN_VER_WINDOWSSEVEN>False</WIN_VER_WINDOWSSEVEN>
      <WIN_VER_WINDOWSSEVEN_BUSINESS>False</WIN_VER_WINDOWSSEVEN_BUSINESS>
      <WIN_VER_WINDOWSSEVEN_CLUSTER_SERVER>False</WIN_VER_WINDOWSSEVEN_CLUSTER_SERVER>
      <WIN_VER_WINDOWSSEVEN_DATACENTER_SERVER>False</WIN_VER_WINDOWSSEVEN_DATACENTER_SERVER>
      <WIN_VER_WINDOWSSEVEN_DATACENTER_SERVER_CORE>False</WIN_VER_WINDOWSSEVEN_DATACENTER_SERVER_CORE>
      <WIN_VER_WINDOWSSEVEN_DATACENTER_SERVER_CORE_V>False</WIN_VER_WINDOWSSEVEN_DATACENTER_SERVER_CORE_V>
      <WIN_VER_WINDOWSSEVEN_DATACENTER_SERVER_V>False</WIN_VER_WINDOWSSEVEN_DATACENTER_SERVER_V>
      <WIN_VER_WINDOWSSEVEN_ENTERPRICE>False</WIN_VER_WINDOWSSEVEN_ENTERPRICE>
      <WIN_VER_WINDOWSSEVEN_ENTERPRISE_SERVER>False</WIN_VER_WINDOWSSEVEN_ENTERPRISE_SERVER>
      <WIN_VER_WINDOWSSEVEN_ENTERPRISE_SERVER_CORE>False</WIN_VER_WINDOWSSEVEN_ENTERPRISE_SERVER_CORE>
      <WIN_VER_WINDOWSSEVEN_ENTERPRISE_SERVER_V>False</WIN_VER_WINDOWSSEVEN_ENTERPRISE_SERVER_V>
      <WIN_VER_WINDOWSSEVEN_ENTERPRISE_SERVER_CORE_V>False</WIN_VER_WINDOWSSEVEN_ENTERPRISE_SERVER_CORE_V>
      <WIN_VER_WINDOWSSEVEN_ENTERPRISE_SERVER_IA64>False</WIN_VER_WINDOWSSEVEN_ENTERPRISE_SERVER_IA64>
      <WIN_VER_WINDOWSSEVEN_HOME_BASIC>False</WIN_VER_WINDOWSSEVEN_HOME_BASIC>
      <WIN_VER_WINDOWSSEVEN_HOME_PREMIUM>False</WIN_VER_WINDOWSSEVEN_HOME_PREMIUM>
      <WIN_VER_WINDOWSSEVEN_HOME_SERVER>False</WIN_VER_WINDOWSSEVEN_HOME_SERVER>
      <WIN_VER_WINDOWSSEVEN_SERVER_FOR_SMALLBUSINESS>False</WIN_VER_WINDOWSSEVEN_SERVER_FOR_SMALLBUSINESS>
      <WIN_VER_WINDOWSSEVEN_SMALLBUSINESS_SERVER>False</WIN_VER_WINDOWSSEVEN_SMALLBUSINESS_SERVER>
      <WIN_VER_WINDOWSSEVEN_SMALLBUSINESS_SERVER_PREMIUM>False</WIN_VER_WINDOWSSEVEN_SMALLBUSINESS_SERVER_PREMIUM>
      <WIN_VER_WINDOWSSEVEN_MEDIUMBUSINESS_SERVER_MANAGEMENT>False</WIN_VER_WINDOWSSEVEN_MEDIUMBUSINESS_SERVER_MANAGEMENT>
      <WIN_VER_WINDOWSSEVEN_MEDIUMBUSINESS_SERVER_MESSAGING>False</WIN_VER_WINDOWSSEVEN_MEDIUMBUSINESS_SERVER_MESSAGING>
      <WIN_VER_WINDOWSSEVEN_MEDIUMBUSINESS_SERVER_SECURITY>False</WIN_VER_WINDOWSSEVEN_MEDIUMBUSINESS_SERVER_SECURITY>
      <WIN_VER_WINDOWSSEVEN_STANDARD_SERVER>False</WIN_VER_WINDOWSSEVEN_STANDARD_SERVER>
      <WIN_VER_WINDOWSSEVEN_STANDARD_SERVER_V>False</WIN_VER_WINDOWSSEVEN_STANDARD_SERVER_V>
      <WIN_VER_WINDOWSSEVEN_STANDARD_SERVER_CORE>False</WIN_VER_WINDOWSSEVEN_STANDARD_SERVER_CORE>
      <WIN_VER_WINDOWSSEVEN_STANDARD_SERVER_CORE_V>False</WIN_VER_WINDOWSSEVEN_STANDARD_SERVER_CORE_V>
      <WIN_VER_WINDOWSSEVEN_STARTER>False</WIN_VER_WINDOWSSEVEN_STARTER>
      <WIN_VER_WINDOWSSEVEN_STORAGE_ENTERPRISE_SERVER>False</WIN_VER_WINDOWSSEVEN_STORAGE_ENTERPRISE_SERVER>
      <WIN_VER_WINDOWSSEVEN_STORAGE_EXPRESS_SERVER>False</WIN_VER_WINDOWSSEVEN_STORAGE_EXPRESS_SERVER>
      <WIN_VER_WINDOWSSEVEN_STORAGE_STANDARD_SERVER>False</WIN_VER_WINDOWSSEVEN_STORAGE_STANDARD_SERVER>
      <WIN_VER_WINDOWSSEVEN_STORAGE_WORKGROUP_SERVER>False</WIN_VER_WINDOWSSEVEN_STORAGE_WORKGROUP_SERVER>
      <WIN_VER_WINDOWSSEVEN_UNDEFINED>False</WIN_VER_WINDOWSSEVEN_UNDEFINED>
      <WIN_VER_WINDOWSSEVEN_ULTIMATE>False</WIN_VER_WINDOWSSEVEN_ULTIMATE>
      <WIN_VER_WINDOWSSEVEN_WEB_SERVER>False</WIN_VER_WINDOWSSEVEN_WEB_SERVER>
      <WIN_VER_WINDOWSSEVEN_WEB_SERVER_CORE>False</WIN_VER_WINDOWSSEVEN_WEB_SERVER_CORE>
      <WIN_VER_WINDOWSSEVEN_UNLICENSED>False</WIN_VER_WINDOWSSEVEN_UNLICENSED>
      <ShowMessageBeforeTermination>True</ShowMessageBeforeTermination>
      <Message>
        <Title>Warning</Title>
        <Text>Current Windows Version &quot;%CU_WINVER%&quot; does not support!</Text>
        <UseHL>False</UseHL>
        <HLSentence>Enigma Protector</HLSentence>
        <HLURL>http://www.enigmaprotector.com/</HLURL>
        <Icon>0</Icon>
        <AutoClose>False</AutoClose>
        <CloseDelay>0</CloseDelay>
        <Disable>False</Disable>
        <DisableDelay>0</DisableDelay>
        <FontName/>
        <FontCharset>0</FontCharset>
        <FontColor>0</FontColor>
        <FontSize>0</FontSize>
        <FontBold>False</FontBold>
        <FontItalic>False</FontItalic>
        <FontUnderline>False</FontUnderline>
        <FontStrikeout>False</FontStrikeout>
      </Message>
      <WIN_VER_WINDOWS8>False</WIN_VER_WINDOWS8>
      <WIN_VER_WINDOWS8_UNLICENSED>False</WIN_VER_WINDOWS8_UNLICENSED>
      <WIN_VER_WINDOWS8_BUSINESS>False</WIN_VER_WINDOWS8_BUSINESS>
      <WIN_VER_WINDOWS8_CLUSTER_SERVER>False</WIN_VER_WINDOWS8_CLUSTER_SERVER>
      <WIN_VER_WINDOWS8_CLUSTER_SERVER_V>False</WIN_VER_WINDOWS8_CLUSTER_SERVER_V>
      <WIN_VER_WINDOWS8_CORE>False</WIN_VER_WINDOWS8_CORE>
      <WIN_VER_WINDOWS8_CORE_COUNTRYSPECIFIC>False</WIN_VER_WINDOWS8_CORE_COUNTRYSPECIFIC>
      <WIN_VER_WINDOWS8_CORE_SINGLELANGUAGE>False</WIN_VER_WINDOWS8_CORE_SINGLELANGUAGE>
      <WIN_VER_WINDOWS8_DATACENTER_EVALUATION_SERVER>False</WIN_VER_WINDOWS8_DATACENTER_EVALUATION_SERVER>
      <WIN_VER_WINDOWS8_DATACENTER_SERVER>False</WIN_VER_WINDOWS8_DATACENTER_SERVER>
      <WIN_VER_WINDOWS8_DATACENTER_SERVER_CORE>False</WIN_VER_WINDOWS8_DATACENTER_SERVER_CORE>
      <WIN_VER_WINDOWS8_DATACENTER_SERVER_CORE_V>False</WIN_VER_WINDOWS8_DATACENTER_SERVER_CORE_V>
      <WIN_VER_WINDOWS8_DATACENTER_SERVER_V>False</WIN_VER_WINDOWS8_DATACENTER_SERVER_V>
      <WIN_VER_WINDOWS8_ENTERPRISE>False</WIN_VER_WINDOWS8_ENTERPRISE>
      <WIN_VER_WINDOWS8_ENTERPRISE_N_EVALUATION>False</WIN_VER_WINDOWS8_ENTERPRISE_N_EVALUATION>
      <WIN_VER_WINDOWS8_ENTERPRISE_EVALUATION>False</WIN_VER_WINDOWS8_ENTERPRISE_EVALUATION>
      <WIN_VER_WINDOWS8_ENTERPRISE_SERVER>False</WIN_VER_WINDOWS8_ENTERPRISE_SERVER>
      <WIN_VER_WINDOWS8_ENTERPRISE_SERVER_CORE>False</WIN_VER_WINDOWS8_ENTERPRISE_SERVER_CORE>
      <WIN_VER_WINDOWS8_ENTERPRISE_SERVER_CORE_V>False</WIN_VER_WINDOWS8_ENTERPRISE_SERVER_CORE_V>
      <WIN_VER_WINDOWS8_ENTERPRISE_SERVER_IA64>False</WIN_VER_WINDOWS8_ENTERPRISE_SERVER_IA64>
      <WIN_VER_WINDOWS8_ENTERPRISE_SERVER_V>False</WIN_VER_WINDOWS8_ENTERPRISE_SERVER_V>
      <WIN_VER_WINDOWS8_ESSENTIALBUSINESS_SERVER_MGMT>False</WIN_VER_WINDOWS8_ESSENTIALBUSINESS_SERVER_MGMT>
      <WIN_VER_WINDOWS8_ESSENTIALBUSINESS_SERVER_ADDL>False</WIN_VER_WINDOWS8_ESSENTIALBUSINESS_SERVER_ADDL>
      <WIN_VER_WINDOWS8_ESSENTIALBUSINESS_SERVER_MGMTSVC>False</WIN_VER_WINDOWS8_ESSENTIALBUSINESS_SERVER_MGMTSVC>
      <WIN_VER_WINDOWS8_ESSENTIALBUSINESS_SERVER_ADDLSVC>False</WIN_VER_WINDOWS8_ESSENTIALBUSINESS_SERVER_ADDLSVC>
      <WIN_VER_WINDOWS8_HOME_BASIC>False</WIN_VER_WINDOWS8_HOME_BASIC>
      <WIN_VER_WINDOWS8_HOME_PREMIUM>False</WIN_VER_WINDOWS8_HOME_PREMIUM>
      <WIN_VER_WINDOWS8_HOME_PREMIUM_SERVER>False</WIN_VER_WINDOWS8_HOME_PREMIUM_SERVER>
      <WIN_VER_WINDOWS8_HOME_SERVER>False</WIN_VER_WINDOWS8_HOME_SERVER>
      <WIN_VER_WINDOWS8_HYPERV>False</WIN_VER_WINDOWS8_HYPERV>
      <WIN_VER_WINDOWS8_MEDIUMBUSINESS_SERVER_MANAGEMENT>False</WIN_VER_WINDOWS8_MEDIUMBUSINESS_SERVER_MANAGEMENT>
      <WIN_VER_WINDOWS8_MEDIUMBUSINESS_SERVER_MESSAGING>False</WIN_VER_WINDOWS8_MEDIUMBUSINESS_SERVER_MESSAGING>
      <WIN_VER_WINDOWS8_MEDIUMBUSINESS_SERVER_SECURITY>False</WIN_VER_WINDOWS8_MEDIUMBUSINESS_SERVER_SECURITY>
      <WIN_VER_WINDOWS8_MULTIPOINT_STANDARD_SERVER>False</WIN_VER_WINDOWS8_MULTIPOINT_STANDARD_SERVER>
      <WIN_VER_WINDOWS8_MULTIPOINT_PREMIUM_SERVER>False</WIN_VER_WINDOWS8_MULTIPOINT_PREMIUM_SERVER>
      <WIN_VER_WINDOWS8_PROFESSIONAL>False</WIN_VER_WINDOWS8_PROFESSIONAL>
      <WIN_VER_WINDOWS8_PROFESSIONAL_WMC>False</WIN_VER_WINDOWS8_PROFESSIONAL_WMC>
      <WIN_VER_WINDOWS8_SB_SOLUTION_SERVER_EM>False</WIN_VER_WINDOWS8_SB_SOLUTION_SERVER_EM>
      <WIN_VER_WINDOWS8_SERVER_FOR_SB_SOLUTIONS>False</WIN_VER_WINDOWS8_SERVER_FOR_SB_SOLUTIONS>
      <WIN_VER_WINDOWS8_SERVER_FOR_SB_SOLUTIONS_EM>False</WIN_VER_WINDOWS8_SERVER_FOR_SB_SOLUTIONS_EM>
      <WIN_VER_WINDOWS8_SERVER_FOR_SMALLBUSINESS>False</WIN_VER_WINDOWS8_SERVER_FOR_SMALLBUSINESS>
      <WIN_VER_WINDOWS8_SERVER_FOR_SMALLBUSINESS_V>False</WIN_VER_WINDOWS8_SERVER_FOR_SMALLBUSINESS_V>
      <WIN_VER_WINDOWS8_SERVER_FOUNDATION>False</WIN_VER_WINDOWS8_SERVER_FOUNDATION>
      <WIN_VER_WINDOWS8_SB_SOLUTION_SERVER>False</WIN_VER_WINDOWS8_SB_SOLUTION_SERVER>
      <WIN_VER_WINDOWS8_SMALLBUSINESS_SERVER>False</WIN_VER_WINDOWS8_SMALLBUSINESS_SERVER>
      <WIN_VER_WINDOWS8_SMALLBUSINESS_SERVER_PREMIUM>False</WIN_VER_WINDOWS8_SMALLBUSINESS_SERVER_PREMIUM>
      <WIN_VER_WINDOWS8_SMALLBUSINESS_SERVER_PREMIUM_CORE>False</WIN_VER_WINDOWS8_SMALLBUSINESS_SERVER_PREMIUM_CORE>
      <WIN_VER_WINDOWS8_SOLUTION_EMBEDDEDSERVER>False</WIN_VER_WINDOWS8_SOLUTION_EMBEDDEDSERVER>
      <WIN_VER_WINDOWS8_STANDARD_EVALUATION_SERVER>False</WIN_VER_WINDOWS8_STANDARD_EVALUATION_SERVER>
      <WIN_VER_WINDOWS8_STANDARD_SERVER>False</WIN_VER_WINDOWS8_STANDARD_SERVER>
      <WIN_VER_WINDOWS8_STANDARD_SERVER_CORE>False</WIN_VER_WINDOWS8_STANDARD_SERVER_CORE>
      <WIN_VER_WINDOWS8_STANDARD_SERVER_V>False</WIN_VER_WINDOWS8_STANDARD_SERVER_V>
      <WIN_VER_WINDOWS8_STANDARD_SERVER_CORE_V>False</WIN_VER_WINDOWS8_STANDARD_SERVER_CORE_V>
      <WIN_VER_WINDOWS8_STANDARD_SERVER_SOLUTIONS>False</WIN_VER_WINDOWS8_STANDARD_SERVER_SOLUTIONS>
      <WIN_VER_WINDOWS8_STANDARD_SERVER_SOLUTIONS_CORE>False</WIN_VER_WINDOWS8_STANDARD_SERVER_SOLUTIONS_CORE>
      <WIN_VER_WINDOWS8_STARTER>False</WIN_VER_WINDOWS8_STARTER>
      <WIN_VER_WINDOWS8_STORAGE_ENTERPRISE_SERVER>False</WIN_VER_WINDOWS8_STORAGE_ENTERPRISE_SERVER>
      <WIN_VER_WINDOWS8_STORAGE_ENTERPRISE_SERVER_CORE>False</WIN_VER_WINDOWS8_STORAGE_ENTERPRISE_SERVER_CORE>
      <WIN_VER_WINDOWS8_STORAGE_EXPRESS_SERVER>False</WIN_VER_WINDOWS8_STORAGE_EXPRESS_SERVER>
      <WIN_VER_WINDOWS8_STORAGE_EXPRESS_SERVER_CORE>False</WIN_VER_WINDOWS8_STORAGE_EXPRESS_SERVER_CORE>
      <WIN_VER_WINDOWS8_STORAGE_STANDARD_EVALUATION_SERVER>False</WIN_VER_WINDOWS8_STORAGE_STANDARD_EVALUATION_SERVER>
      <WIN_VER_WINDOWS8_STORAGE_STANDARD_SERVER>False</WIN_VER_WINDOWS8_STORAGE_STANDARD_SERVER>
      <WIN_VER_WINDOWS8_STORAGE_STANDARD_SERVER_CORE>False</WIN_VER_WINDOWS8_STORAGE_STANDARD_SERVER_CORE>
      <WIN_VER_WINDOWS8_STORAGE_WORKGROUP_EVALUATION_SERVER>False</WIN_VER_WINDOWS8_STORAGE_WORKGROUP_EVALUATION_SERVER>
      <WIN_VER_WINDOWS8_STORAGE_WORKGROUP_SERVER>False</WIN_VER_WINDOWS8_STORAGE_WORKGROUP_SERVER>
      <WIN_VER_WINDOWS8_STORAGE_WORKGROUP_SERVER_CORE>False</WIN_VER_WINDOWS8_STORAGE_WORKGROUP_SERVER_CORE>
      <WIN_VER_WINDOWS8_UNDEFINED>False</WIN_VER_WINDOWS8_UNDEFINED>
      <WIN_VER_WINDOWS8_ULTIMATE>False</WIN_VER_WINDOWS8_ULTIMATE>
      <WIN_VER_WINDOWS8_WEB_SERVER>False</WIN_VER_WINDOWS8_WEB_SERVER>
      <WIN_VER_WINDOWS8_WEB_SERVER_CORE>False</WIN_VER_WINDOWS8_WEB_SERVER_CORE>
      <WIN_VER_WINDOWS2012>False</WIN_VER_WINDOWS2012>
      <WIN_VER_WINDOWS2012_UNLICENSED>False</WIN_VER_WINDOWS2012_UNLICENSED>
      <WIN_VER_WINDOWS2012_BUSINESS>False</WIN_VER_WINDOWS2012_BUSINESS>
      <WIN_VER_WINDOWS2012_CLUSTER_SERVER>False</WIN_VER_WINDOWS2012_CLUSTER_SERVER>
      <WIN_VER_WINDOWS2012_CLUSTER_SERVER_V>False</WIN_VER_WINDOWS2012_CLUSTER_SERVER_V>
      <WIN_VER_WINDOWS2012_CORE>False</WIN_VER_WINDOWS2012_CORE>
      <WIN_VER_WINDOWS2012_CORE_COUNTRYSPECIFIC>False</WIN_VER_WINDOWS2012_CORE_COUNTRYSPECIFIC>
      <WIN_VER_WINDOWS2012_CORE_SINGLELANGUAGE>False</WIN_VER_WINDOWS2012_CORE_SINGLELANGUAGE>
      <WIN_VER_WINDOWS2012_DATACENTER_EVALUATION_SERVER>False</WIN_VER_WINDOWS2012_DATACENTER_EVALUATION_SERVER>
      <WIN_VER_WINDOWS2012_DATACENTER_SERVER>False</WIN_VER_WINDOWS2012_DATACENTER_SERVER>
      <WIN_VER_WINDOWS2012_DATACENTER_SERVER_CORE>False</WIN_VER_WINDOWS2012_DATACENTER_SERVER_CORE>
      <WIN_VER_WINDOWS2012_DATACENTER_SERVER_CORE_V>False</WIN_VER_WINDOWS2012_DATACENTER_SERVER_CORE_V>
      <WIN_VER_WINDOWS2012_DATACENTER_SERVER_V>False</WIN_VER_WINDOWS2012_DATACENTER_SERVER_V>
      <WIN_VER_WINDOWS2012_ENTERPRISE>False</WIN_VER_WINDOWS2012_ENTERPRISE>
      <WIN_VER_WINDOWS2012_ENTERPRISE_N_EVALUATION>False</WIN_VER_WINDOWS2012_ENTERPRISE_N_EVALUATION>
      <WIN_VER_WINDOWS2012_ENTERPRISE_EVALUATION>False</WIN_VER_WINDOWS2012_ENTERPRISE_EVALUATION>
      <WIN_VER_WINDOWS2012_ENTERPRISE_SERVER>False</WIN_VER_WINDOWS2012_ENTERPRISE_SERVER>
      <WIN_VER_WINDOWS2012_ENTERPRISE_SERVER_CORE>False</WIN_VER_WINDOWS2012_ENTERPRISE_SERVER_CORE>
      <WIN_VER_WINDOWS2012_ENTERPRISE_SERVER_CORE_V>False</WIN_VER_WINDOWS2012_ENTERPRISE_SERVER_CORE_V>
      <WIN_VER_WINDOWS2012_ENTERPRISE_SERVER_IA64>False</WIN_VER_WINDOWS2012_ENTERPRISE_SERVER_IA64>
      <WIN_VER_WINDOWS2012_ENTERPRISE_SERVER_V>False</WIN_VER_WINDOWS2012_ENTERPRISE_SERVER_V>
      <WIN_VER_WINDOWS2012_ESSENTIALBUSINESS_SERVER_MGMT>False</WIN_VER_WINDOWS2012_ESSENTIALBUSINESS_SERVER_MGMT>
      <WIN_VER_WINDOWS2012_ESSENTIALBUSINESS_SERVER_ADDL>False</WIN_VER_WINDOWS2012_ESSENTIALBUSINESS_SERVER_ADDL>
      <WIN_VER_WINDOWS2012_ESSENTIALBUSINESS_SERVER_MGMTSVC>False</WIN_VER_WINDOWS2012_ESSENTIALBUSINESS_SERVER_MGMTSVC>
      <WIN_VER_WINDOWS2012_ESSENTIALBUSINESS_SERVER_ADDLSVC>False</WIN_VER_WINDOWS2012_ESSENTIALBUSINESS_SERVER_ADDLSVC>
      <WIN_VER_WINDOWS2012_HOME_BASIC>False</WIN_VER_WINDOWS2012_HOME_BASIC>
      <WIN_VER_WINDOWS2012_HOME_PREMIUM>False</WIN_VER_WINDOWS2012_HOME_PREMIUM>
      <WIN_VER_WINDOWS2012_HOME_PREMIUM_SERVER>False</WIN_VER_WINDOWS2012_HOME_PREMIUM_SERVER>
      <WIN_VER_WINDOWS2012_HOME_SERVER>False</WIN_VER_WINDOWS2012_HOME_SERVER>
      <WIN_VER_WINDOWS2012_HYPERV>False</WIN_VER_WINDOWS2012_HYPERV>
      <WIN_VER_WINDOWS2012_MEDIUMBUSINESS_SERVER_MANAGEMENT>False</WIN_VER_WINDOWS2012_MEDIUMBUSINESS_SERVER_MANAGEMENT>
      <WIN_VER_WINDOWS2012_MEDIUMBUSINESS_SERVER_MESSAGING>False</WIN_VER_WINDOWS2012_MEDIUMBUSINESS_SERVER_MESSAGING>
      <WIN_VER_WINDOWS2012_MEDIUMBUSINESS_SERVER_SECURITY>False</WIN_VER_WINDOWS2012_MEDIUMBUSINESS_SERVER_SECURITY>
      <WIN_VER_WINDOWS2012_MULTIPOINT_STANDARD_SERVER>False</WIN_VER_WINDOWS2012_MULTIPOINT_STANDARD_SERVER>
      <WIN_VER_WINDOWS2012_MULTIPOINT_PREMIUM_SERVER>False</WIN_VER_WINDOWS2012_MULTIPOINT_PREMIUM_SERVER>
      <WIN_VER_WINDOWS2012_PROFESSIONAL>False</WIN_VER_WINDOWS2012_PROFESSIONAL>
      <WIN_VER_WINDOWS2012_PROFESSIONAL_WMC>False</WIN_VER_WINDOWS2012_PROFESSIONAL_WMC>
      <WIN_VER_WINDOWS2012_SB_SOLUTION_SERVER_EM>False</WIN_VER_WINDOWS2012_SB_SOLUTION_SERVER_EM>
      <WIN_VER_WINDOWS2012_SERVER_FOR_SB_SOLUTIONS>False</WIN_VER_WINDOWS2012_SERVER_FOR_SB_SOLUTIONS>
      <WIN_VER_WINDOWS2012_SERVER_FOR_SB_SOLUTIONS_EM>False</WIN_VER_WINDOWS2012_SERVER_FOR_SB_SOLUTIONS_EM>
      <WIN_VER_WINDOWS2012_SERVER_FOR_SMALLBUSINESS>False</WIN_VER_WINDOWS2012_SERVER_FOR_SMALLBUSINESS>
      <WIN_VER_WINDOWS2012_SERVER_FOR_SMALLBUSINESS_V>False</WIN_VER_WINDOWS2012_SERVER_FOR_SMALLBUSINESS_V>
      <WIN_VER_WINDOWS2012_SERVER_FOUNDATION>False</WIN_VER_WINDOWS2012_SERVER_FOUNDATION>
      <WIN_VER_WINDOWS2012_SB_SOLUTION_SERVER>False</WIN_VER_WINDOWS2012_SB_SOLUTION_SERVER>
      <WIN_VER_WINDOWS2012_SMALLBUSINESS_SERVER>False</WIN_VER_WINDOWS2012_SMALLBUSINESS_SERVER>
      <WIN_VER_WINDOWS2012_SMALLBUSINESS_SERVER_PREMIUM>False</WIN_VER_WINDOWS2012_SMALLBUSINESS_SERVER_PREMIUM>
      <WIN_VER_WINDOWS2012_SMALLBUSINESS_SERVER_PREMIUM_CORE>False</WIN_VER_WINDOWS2012_SMALLBUSINESS_SERVER_PREMIUM_CORE>
      <WIN_VER_WINDOWS2012_SOLUTION_EMBEDDEDSERVER>False</WIN_VER_WINDOWS2012_SOLUTION_EMBEDDEDSERVER>
      <WIN_VER_WINDOWS2012_STANDARD_EVALUATION_SERVER>False</WIN_VER_WINDOWS2012_STANDARD_EVALUATION_SERVER>
      <WIN_VER_WINDOWS2012_STANDARD_SERVER>False</WIN_VER_WINDOWS2012_STANDARD_SERVER>
      <WIN_VER_WINDOWS2012_STANDARD_SERVER_CORE>False</WIN_VER_WINDOWS2012_STANDARD_SERVER_CORE>
      <WIN_VER_WINDOWS2012_STANDARD_SERVER_V>False</WIN_VER_WINDOWS2012_STANDARD_SERVER_V>
      <WIN_VER_WINDOWS2012_STANDARD_SERVER_CORE_V>False</WIN_VER_WINDOWS2012_STANDARD_SERVER_CORE_V>
      <WIN_VER_WINDOWS2012_STANDARD_SERVER_SOLUTIONS>False</WIN_VER_WINDOWS2012_STANDARD_SERVER_SOLUTIONS>
      <WIN_VER_WINDOWS2012_STANDARD_SERVER_SOLUTIONS_CORE>False</WIN_VER_WINDOWS2012_STANDARD_SERVER_SOLUTIONS_CORE>
      <WIN_VER_WINDOWS2012_STARTER>False</WIN_VER_WINDOWS2012_STARTER>
      <WIN_VER_WINDOWS2012_STORAGE_ENTERPRISE_SERVER>False</WIN_VER_WINDOWS2012_STORAGE_ENTERPRISE_SERVER>
      <WIN_VER_WINDOWS2012_STORAGE_ENTERPRISE_SERVER_CORE>False</WIN_VER_WINDOWS2012_STORAGE_ENTERPRISE_SERVER_CORE>
      <WIN_VER_WINDOWS2012_STORAGE_EXPRESS_SERVER>False</WIN_VER_WINDOWS2012_STORAGE_EXPRESS_SERVER>
      <WIN_VER_WINDOWS2012_STORAGE_EXPRESS_SERVER_CORE>False</WIN_VER_WINDOWS2012_STORAGE_EXPRESS_SERVER_CORE>
      <WIN_VER_WINDOWS2012_STORAGE_STANDARD_EVALUATION_SERVER>False</WIN_VER_WINDOWS2012_STORAGE_STANDARD_EVALUATION_SERVER>
      <WIN_VER_WINDOWS2012_STORAGE_STANDARD_SERVER>False</WIN_VER_WINDOWS2012_STORAGE_STANDARD_SERVER>
      <WIN_VER_WINDOWS2012_STORAGE_STANDARD_SERVER_CORE>False</WIN_VER_WINDOWS2012_STORAGE_STANDARD_SERVER_CORE>
      <WIN_VER_WINDOWS2012_STORAGE_WORKGROUP_EVALUATION_SERVER>False</WIN_VER_WINDOWS2012_STORAGE_WORKGROUP_EVALUATION_SERVER>
      <WIN_VER_WINDOWS2012_STORAGE_WORKGROUP_SERVER>False</WIN_VER_WINDOWS2012_STORAGE_WORKGROUP_SERVER>
      <WIN_VER_WINDOWS2012_STORAGE_WORKGROUP_SERVER_CORE>False</WIN_VER_WINDOWS2012_STORAGE_WORKGROUP_SERVER_CORE>
      <WIN_VER_WINDOWS2012_UNDEFINED>False</WIN_VER_WINDOWS2012_UNDEFINED>
      <WIN_VER_WINDOWS2012_ULTIMATE>False</WIN_VER_WINDOWS2012_ULTIMATE>
      <WIN_VER_WINDOWS2012_WEB_SERVER>False</WIN_VER_WINDOWS2012_WEB_SERVER>
      <WIN_VER_WINDOWS2012_WEB_SERVER_CORE>False</WIN_VER_WINDOWS2012_WEB_SERVER_CORE>
      <WIN_VER_WINDOWS81>False</WIN_VER_WINDOWS81>
      <WIN_VER_WINDOWS81_UNLICENSED>False</WIN_VER_WINDOWS81_UNLICENSED>
      <WIN_VER_WINDOWS81_BUSINESS>False</WIN_VER_WINDOWS81_BUSINESS>
      <WIN_VER_WINDOWS81_CLUSTER_SERVER>False</WIN_VER_WINDOWS81_CLUSTER_SERVER>
      <WIN_VER_WINDOWS81_CLUSTER_SERVER_V>False</WIN_VER_WINDOWS81_CLUSTER_SERVER_V>
      <WIN_VER_WINDOWS81_CORE>False</WIN_VER_WINDOWS81_CORE>
      <WIN_VER_WINDOWS81_CORE_COUNTRYSPECIFIC>False</WIN_VER_WINDOWS81_CORE_COUNTRYSPECIFIC>
      <WIN_VER_WINDOWS81_CORE_SINGLELANGUAGE>False</WIN_VER_WINDOWS81_CORE_SINGLELANGUAGE>
      <WIN_VER_WINDOWS81_DATACENTER_EVALUATION_SERVER>False</WIN_VER_WINDOWS81_DATACENTER_EVALUATION_SERVER>
      <WIN_VER_WINDOWS81_DATACENTER_SERVER>False</WIN_VER_WINDOWS81_DATACENTER_SERVER>
      <WIN_VER_WINDOWS81_DATACENTER_SERVER_CORE>False</WIN_VER_WINDOWS81_DATACENTER_SERVER_CORE>
      <WIN_VER_WINDOWS81_DATACENTER_SERVER_CORE_V>False</WIN_VER_WINDOWS81_DATACENTER_SERVER_CORE_V>
      <WIN_VER_WINDOWS81_DATACENTER_SERVER_V>False</WIN_VER_WINDOWS81_DATACENTER_SERVER_V>
      <WIN_VER_WINDOWS81_ENTERPRISE>False</WIN_VER_WINDOWS81_ENTERPRISE>
      <WIN_VER_WINDOWS81_ENTERPRISE_N_EVALUATION>False</WIN_VER_WINDOWS81_ENTERPRISE_N_EVALUATION>
      <WIN_VER_WINDOWS81_ENTERPRISE_EVALUATION>False</WIN_VER_WINDOWS81_ENTERPRISE_EVALUATION>
      <WIN_VER_WINDOWS81_ENTERPRISE_SERVER>False</WIN_VER_WINDOWS81_ENTERPRISE_SERVER>
      <WIN_VER_WINDOWS81_ENTERPRISE_SERVER_CORE>False</WIN_VER_WINDOWS81_ENTERPRISE_SERVER_CORE>
      <WIN_VER_WINDOWS81_ENTERPRISE_SERVER_CORE_V>False</WIN_VER_WINDOWS81_ENTERPRISE_SERVER_CORE_V>
      <WIN_VER_WINDOWS81_ENTERPRISE_SERVER_IA64>False</WIN_VER_WINDOWS81_ENTERPRISE_SERVER_IA64>
      <WIN_VER_WINDOWS81_ENTERPRISE_SERVER_V>False</WIN_VER_WINDOWS81_ENTERPRISE_SERVER_V>
      <WIN_VER_WINDOWS81_ESSENTIALBUSINESS_SERVER_MGMT>False</WIN_VER_WINDOWS81_ESSENTIALBUSINESS_SERVER_MGMT>
      <WIN_VER_WINDOWS81_ESSENTIALBUSINESS_SERVER_ADDL>False</WIN_VER_WINDOWS81_ESSENTIALBUSINESS_SERVER_ADDL>
      <WIN_VER_WINDOWS81_ESSENTIALBUSINESS_SERVER_MGMTSVC>False</WIN_VER_WINDOWS81_ESSENTIALBUSINESS_SERVER_MGMTSVC>
      <WIN_VER_WINDOWS81_ESSENTIALBUSINESS_SERVER_ADDLSVC>False</WIN_VER_WINDOWS81_ESSENTIALBUSINESS_SERVER_ADDLSVC>
      <WIN_VER_WINDOWS81_HOME_BASIC>False</WIN_VER_WINDOWS81_HOME_BASIC>
      <WIN_VER_WINDOWS81_HOME_PREMIUM>False</WIN_VER_WINDOWS81_HOME_PREMIUM>
      <WIN_VER_WINDOWS81_HOME_PREMIUM_SERVER>False</WIN_VER_WINDOWS81_HOME_PREMIUM_SERVER>
      <WIN_VER_WINDOWS81_HOME_SERVER>False</WIN_VER_WINDOWS81_HOME_SERVER>
      <WIN_VER_WINDOWS81_HYPERV>False</WIN_VER_WINDOWS81_HYPERV>
      <WIN_VER_WINDOWS81_MEDIUMBUSINESS_SERVER_MANAGEMENT>False</WIN_VER_WINDOWS81_MEDIUMBUSINESS_SERVER_MANAGEMENT>
      <WIN_VER_WINDOWS81_MEDIUMBUSINESS_SERVER_MESSAGING>False</WIN_VER_WINDOWS81_MEDIUMBUSINESS_SERVER_MESSAGING>
      <WIN_VER_WINDOWS81_MEDIUMBUSINESS_SERVER_SECURITY>False</WIN_VER_WINDOWS81_MEDIUMBUSINESS_SERVER_SECURITY>
      <WIN_VER_WINDOWS81_MULTIPOINT_STANDARD_SERVER>False</WIN_VER_WINDOWS81_MULTIPOINT_STANDARD_SERVER>
      <WIN_VER_WINDOWS81_MULTIPOINT_PREMIUM_SERVER>False</WIN_VER_WINDOWS81_MULTIPOINT_PREMIUM_SERVER>
      <WIN_VER_WINDOWS81_PROFESSIONAL>False</WIN_VER_WINDOWS81_PROFESSIONAL>
      <WIN_VER_WINDOWS81_PROFESSIONAL_WMC>False</WIN_VER_WINDOWS81_PROFESSIONAL_WMC>
      <WIN_VER_WINDOWS81_SB_SOLUTION_SERVER_EM>False</WIN_VER_WINDOWS81_SB_SOLUTION_SERVER_EM>
      <WIN_VER_WINDOWS81_SERVER_FOR_SB_SOLUTIONS>False</WIN_VER_WINDOWS81_SERVER_FOR_SB_SOLUTIONS>
      <WIN_VER_WINDOWS81_SERVER_FOR_SB_SOLUTIONS_EM>False</WIN_VER_WINDOWS81_SERVER_FOR_SB_SOLUTIONS_EM>
      <WIN_VER_WINDOWS81_SERVER_FOR_SMALLBUSINESS>False</WIN_VER_WINDOWS81_SERVER_FOR_SMALLBUSINESS>
      <WIN_VER_WINDOWS81_SERVER_FOR_SMALLBUSINESS_V>False</WIN_VER_WINDOWS81_SERVER_FOR_SMALLBUSINESS_V>
      <WIN_VER_WINDOWS81_SERVER_FOUNDATION>False</WIN_VER_WINDOWS81_SERVER_FOUNDATION>
      <WIN_VER_WINDOWS81_SB_SOLUTION_SERVER>False</WIN_VER_WINDOWS81_SB_SOLUTION_SERVER>
      <WIN_VER_WINDOWS81_SMALLBUSINESS_SERVER>False</WIN_VER_WINDOWS81_SMALLBUSINESS_SERVER>
      <WIN_VER_WINDOWS81_SMALLBUSINESS_SERVER_PREMIUM>False</WIN_VER_WINDOWS81_SMALLBUSINESS_SERVER_PREMIUM>
      <WIN_VER_WINDOWS81_SMALLBUSINESS_SERVER_PREMIUM_CORE>False</WIN_VER_WINDOWS81_SMALLBUSINESS_SERVER_PREMIUM_CORE>
      <WIN_VER_WINDOWS81_SOLUTION_EMBEDDEDSERVER>False</WIN_VER_WINDOWS81_SOLUTION_EMBEDDEDSERVER>
      <WIN_VER_WINDOWS81_STANDARD_EVALUATION_SERVER>False</WIN_VER_WINDOWS81_STANDARD_EVALUATION_SERVER>
      <WIN_VER_WINDOWS81_STANDARD_SERVER>False</WIN_VER_WINDOWS81_STANDARD_SERVER>
      <WIN_VER_WINDOWS81_STANDARD_SERVER_CORE>False</WIN_VER_WINDOWS81_STANDARD_SERVER_CORE>
      <WIN_VER_WINDOWS81_STANDARD_SERVER_V>False</WIN_VER_WINDOWS81_STANDARD_SERVER_V>
      <WIN_VER_WINDOWS81_STANDARD_SERVER_CORE_V>False</WIN_VER_WINDOWS81_STANDARD_SERVER_CORE_V>
      <WIN_VER_WINDOWS81_STANDARD_SERVER_SOLUTIONS>False</WIN_VER_WINDOWS81_STANDARD_SERVER_SOLUTIONS>
      <WIN_VER_WINDOWS81_STANDARD_SERVER_SOLUTIONS_CORE>False</WIN_VER_WINDOWS81_STANDARD_SERVER_SOLUTIONS_CORE>
      <WIN_VER_WINDOWS81_STARTER>False</WIN_VER_WINDOWS81_STARTER>
      <WIN_VER_WINDOWS81_STORAGE_ENTERPRISE_SERVER>False</WIN_VER_WINDOWS81_STORAGE_ENTERPRISE_SERVER>
      <WIN_VER_WINDOWS81_STORAGE_ENTERPRISE_SERVER_CORE>False</WIN_VER_WINDOWS81_STORAGE_ENTERPRISE_SERVER_CORE>
      <WIN_VER_WINDOWS81_STORAGE_EXPRESS_SERVER>False</WIN_VER_WINDOWS81_STORAGE_EXPRESS_SERVER>
      <WIN_VER_WINDOWS81_STORAGE_EXPRESS_SERVER_CORE>False</WIN_VER_WINDOWS81_STORAGE_EXPRESS_SERVER_CORE>
      <WIN_VER_WINDOWS81_STORAGE_STANDARD_EVALUATION_SERVER>False</WIN_VER_WINDOWS81_STORAGE_STANDARD_EVALUATION_SERVER>
      <WIN_VER_WINDOWS81_STORAGE_STANDARD_SERVER>False</WIN_VER_WINDOWS81_STORAGE_STANDARD_SERVER>
      <WIN_VER_WINDOWS81_STORAGE_STANDARD_SERVER_CORE>False</WIN_VER_WINDOWS81_STORAGE_STANDARD_SERVER_CORE>
      <WIN_VER_WINDOWS81_STORAGE_WORKGROUP_EVALUATION_SERVER>False</WIN_VER_WINDOWS81_STORAGE_WORKGROUP_EVALUATION_SERVER>
      <WIN_VER_WINDOWS81_STORAGE_WORKGROUP_SERVER>False</WIN_VER_WINDOWS81_STORAGE_WORKGROUP_SERVER>
      <WIN_VER_WINDOWS81_STORAGE_WORKGROUP_SERVER_CORE>False</WIN_VER_WINDOWS81_STORAGE_WORKGROUP_SERVER_CORE>
      <WIN_VER_WINDOWS81_UNDEFINED>False</WIN_VER_WINDOWS81_UNDEFINED>
      <WIN_VER_WINDOWS81_ULTIMATE>False</WIN_VER_WINDOWS81_ULTIMATE>
      <WIN_VER_WINDOWS81_WEB_SERVER>False</WIN_VER_WINDOWS81_WEB_SERVER>
      <WIN_VER_WINDOWS81_WEB_SERVER_CORE>False</WIN_VER_WINDOWS81_WEB_SERVER_CORE>
      <WIN_VER_WINDOWS2012R2>False</WIN_VER_WINDOWS2012R2>
      <WIN_VER_WINDOWS2012R2_UNLICENSED>False</WIN_VER_WINDOWS2012R2_UNLICENSED>
      <WIN_VER_WINDOWS2012R2_BUSINESS>False</WIN_VER_WINDOWS2012R2_BUSINESS>
      <WIN_VER_WINDOWS2012R2_CLUSTER_SERVER>False</WIN_VER_WINDOWS2012R2_CLUSTER_SERVER>
      <WIN_VER_WINDOWS2012R2_CLUSTER_SERVER_V>False</WIN_VER_WINDOWS2012R2_CLUSTER_SERVER_V>
      <WIN_VER_WINDOWS2012R2_CORE>False</WIN_VER_WINDOWS2012R2_CORE>
      <WIN_VER_WINDOWS2012R2_CORE_COUNTRYSPECIFIC>False</WIN_VER_WINDOWS2012R2_CORE_COUNTRYSPECIFIC>
      <WIN_VER_WINDOWS2012R2_CORE_SINGLELANGUAGE>False</WIN_VER_WINDOWS2012R2_CORE_SINGLELANGUAGE>
      <WIN_VER_WINDOWS2012R2_DATACENTER_EVALUATION_SERVER>False</WIN_VER_WINDOWS2012R2_DATACENTER_EVALUATION_SERVER>
      <WIN_VER_WINDOWS2012R2_DATACENTER_SERVER>False</WIN_VER_WINDOWS2012R2_DATACENTER_SERVER>
      <WIN_VER_WINDOWS2012R2_DATACENTER_SERVER_CORE>False</WIN_VER_WINDOWS2012R2_DATACENTER_SERVER_CORE>
      <WIN_VER_WINDOWS2012R2_DATACENTER_SERVER_CORE_V>False</WIN_VER_WINDOWS2012R2_DATACENTER_SERVER_CORE_V>
      <WIN_VER_WINDOWS2012R2_DATACENTER_SERVER_V>False</WIN_VER_WINDOWS2012R2_DATACENTER_SERVER_V>
      <WIN_VER_WINDOWS2012R2_ENTERPRISE>False</WIN_VER_WINDOWS2012R2_ENTERPRISE>
      <WIN_VER_WINDOWS2012R2_ENTERPRISE_N_EVALUATION>False</WIN_VER_WINDOWS2012R2_ENTERPRISE_N_EVALUATION>
      <WIN_VER_WINDOWS2012R2_ENTERPRISE_EVALUATION>False</WIN_VER_WINDOWS2012R2_ENTERPRISE_EVALUATION>
      <WIN_VER_WINDOWS2012R2_ENTERPRISE_SERVER>False</WIN_VER_WINDOWS2012R2_ENTERPRISE_SERVER>
      <WIN_VER_WINDOWS2012R2_ENTERPRISE_SERVER_CORE>False</WIN_VER_WINDOWS2012R2_ENTERPRISE_SERVER_CORE>
      <WIN_VER_WINDOWS2012R2_ENTERPRISE_SERVER_CORE_V>False</WIN_VER_WINDOWS2012R2_ENTERPRISE_SERVER_CORE_V>
      <WIN_VER_WINDOWS2012R2_ENTERPRISE_SERVER_IA64>False</WIN_VER_WINDOWS2012R2_ENTERPRISE_SERVER_IA64>
      <WIN_VER_WINDOWS2012R2_ENTERPRISE_SERVER_V>False</WIN_VER_WINDOWS2012R2_ENTERPRISE_SERVER_V>
      <WIN_VER_WINDOWS2012R2_ESSENTIALBUSINESS_SERVER_MGMT>False</WIN_VER_WINDOWS2012R2_ESSENTIALBUSINESS_SERVER_MGMT>
      <WIN_VER_WINDOWS2012R2_ESSENTIALBUSINESS_SERVER_ADDL>False</WIN_VER_WINDOWS2012R2_ESSENTIALBUSINESS_SERVER_ADDL>
      <WIN_VER_WINDOWS2012R2_ESSENTIALBUSINESS_SERVER_MGMTSVC>False</WIN_VER_WINDOWS2012R2_ESSENTIALBUSINESS_SERVER_MGMTSVC>
      <WIN_VER_WINDOWS2012R2_ESSENTIALBUSINESS_SERVER_ADDLSVC>False</WIN_VER_WINDOWS2012R2_ESSENTIALBUSINESS_SERVER_ADDLSVC>
      <WIN_VER_WINDOWS2012R2_HOME_BASIC>False</WIN_VER_WINDOWS2012R2_HOME_BASIC>
      <WIN_VER_WINDOWS2012R2_HOME_PREMIUM>False</WIN_VER_WINDOWS2012R2_HOME_PREMIUM>
      <WIN_VER_WINDOWS2012R2_HOME_PREMIUM_SERVER>False</WIN_VER_WINDOWS2012R2_HOME_PREMIUM_SERVER>
      <WIN_VER_WINDOWS2012R2_HOME_SERVER>False</WIN_VER_WINDOWS2012R2_HOME_SERVER>
      <WIN_VER_WINDOWS2012R2_HYPERV>False</WIN_VER_WINDOWS2012R2_HYPERV>
      <WIN_VER_WINDOWS2012R2_MEDIUMBUSINESS_SERVER_MANAGEMENT>False</WIN_VER_WINDOWS2012R2_MEDIUMBUSINESS_SERVER_MANAGEMENT>
      <WIN_VER_WINDOWS2012R2_MEDIUMBUSINESS_SERVER_MESSAGING>False</WIN_VER_WINDOWS2012R2_MEDIUMBUSINESS_SERVER_MESSAGING>
      <WIN_VER_WINDOWS2012R2_MEDIUMBUSINESS_SERVER_SECURITY>False</WIN_VER_WINDOWS2012R2_MEDIUMBUSINESS_SERVER_SECURITY>
      <WIN_VER_WINDOWS2012R2_MULTIPOINT_STANDARD_SERVER>False</WIN_VER_WINDOWS2012R2_MULTIPOINT_STANDARD_SERVER>
      <WIN_VER_WINDOWS2012R2_MULTIPOINT_PREMIUM_SERVER>False</WIN_VER_WINDOWS2012R2_MULTIPOINT_PREMIUM_SERVER>
      <WIN_VER_WINDOWS2012R2_PROFESSIONAL>False</WIN_VER_WINDOWS2012R2_PROFESSIONAL>
      <WIN_VER_WINDOWS2012R2_PROFESSIONAL_WMC>False</WIN_VER_WINDOWS2012R2_PROFESSIONAL_WMC>
      <WIN_VER_WINDOWS2012R2_SB_SOLUTION_SERVER_EM>False</WIN_VER_WINDOWS2012R2_SB_SOLUTION_SERVER_EM>
      <WIN_VER_WINDOWS2012R2_SERVER_FOR_SB_SOLUTIONS>False</WIN_VER_WINDOWS2012R2_SERVER_FOR_SB_SOLUTIONS>
      <WIN_VER_WINDOWS2012R2_SERVER_FOR_SB_SOLUTIONS_EM>False</WIN_VER_WINDOWS2012R2_SERVER_FOR_SB_SOLUTIONS_EM>
      <WIN_VER_WINDOWS2012R2_SERVER_FOR_SMALLBUSINESS>False</WIN_VER_WINDOWS2012R2_SERVER_FOR_SMALLBUSINESS>
      <WIN_VER_WINDOWS2012R2_SERVER_FOR_SMALLBUSINESS_V>False</WIN_VER_WINDOWS2012R2_SERVER_FOR_SMALLBUSINESS_V>
      <WIN_VER_WINDOWS2012R2_SERVER_FOUNDATION>False</WIN_VER_WINDOWS2012R2_SERVER_FOUNDATION>
      <WIN_VER_WINDOWS2012R2_SB_SOLUTION_SERVER>False</WIN_VER_WINDOWS2012R2_SB_SOLUTION_SERVER>
      <WIN_VER_WINDOWS2012R2_SMALLBUSINESS_SERVER>False</WIN_VER_WINDOWS2012R2_SMALLBUSINESS_SERVER>
      <WIN_VER_WINDOWS2012R2_SMALLBUSINESS_SERVER_PREMIUM>False</WIN_VER_WINDOWS2012R2_SMALLBUSINESS_SERVER_PREMIUM>
      <WIN_VER_WINDOWS2012R2_SMALLBUSINESS_SERVER_PREMIUM_CORE>False</WIN_VER_WINDOWS2012R2_SMALLBUSINESS_SERVER_PREMIUM_CORE>
      <WIN_VER_WINDOWS2012R2_SOLUTION_EMBEDDEDSERVER>False</WIN_VER_WINDOWS2012R2_SOLUTION_EMBEDDEDSERVER>
      <WIN_VER_WINDOWS2012R2_STANDARD_EVALUATION_SERVER>False</WIN_VER_WINDOWS2012R2_STANDARD_EVALUATION_SERVER>
      <WIN_VER_WINDOWS2012R2_STANDARD_SERVER>False</WIN_VER_WINDOWS2012R2_STANDARD_SERVER>
      <WIN_VER_WINDOWS2012R2_STANDARD_SERVER_CORE>False</WIN_VER_WINDOWS2012R2_STANDARD_SERVER_CORE>
      <WIN_VER_WINDOWS2012R2_STANDARD_SERVER_V>False</WIN_VER_WINDOWS2012R2_STANDARD_SERVER_V>
      <WIN_VER_WINDOWS2012R2_STANDARD_SERVER_CORE_V>False</WIN_VER_WINDOWS2012R2_STANDARD_SERVER_CORE_V>
      <WIN_VER_WINDOWS2012R2_STANDARD_SERVER_SOLUTIONS>False</WIN_VER_WINDOWS2012R2_STANDARD_SERVER_SOLUTIONS>
      <WIN_VER_WINDOWS2012R2_STANDARD_SERVER_SOLUTIONS_CORE>False</WIN_VER_WINDOWS2012R2_STANDARD_SERVER_SOLUTIONS_CORE>
      <WIN_VER_WINDOWS2012R2_STARTER>False</WIN_VER_WINDOWS2012R2_STARTER>
      <WIN_VER_WINDOWS2012R2_STORAGE_ENTERPRISE_SERVER>False</WIN_VER_WINDOWS2012R2_STORAGE_ENTERPRISE_SERVER>
      <WIN_VER_WINDOWS2012R2_STORAGE_ENTERPRISE_SERVER_CORE>False</WIN_VER_WINDOWS2012R2_STORAGE_ENTERPRISE_SERVER_CORE>
      <WIN_VER_WINDOWS2012R2_STORAGE_EXPRESS_SERVER>False</WIN_VER_WINDOWS2012R2_STORAGE_EXPRESS_SERVER>
      <WIN_VER_WINDOWS2012R2_STORAGE_EXPRESS_SERVER_CORE>False</WIN_VER_WINDOWS2012R2_STORAGE_EXPRESS_SERVER_CORE>
      <WIN_VER_WINDOWS2012R2_STORAGE_STANDARD_EVALUATION_SERVER>False</WIN_VER_WINDOWS2012R2_STORAGE_STANDARD_EVALUATION_SERVER>
      <WIN_VER_WINDOWS2012R2_STORAGE_STANDARD_SERVER>False</WIN_VER_WINDOWS2012R2_STORAGE_STANDARD_SERVER>
      <WIN_VER_WINDOWS2012R2_STORAGE_STANDARD_SERVER_CORE>False</WIN_VER_WINDOWS2012R2_STORAGE_STANDARD_SERVER_CORE>
      <WIN_VER_WINDOWS2012R2_STORAGE_WORKGROUP_EVALUATION_SERVER>False</WIN_VER_WINDOWS2012R2_STORAGE_WORKGROUP_EVALUATION_SERVER>
      <WIN_VER_WINDOWS2012R2_STORAGE_WORKGROUP_SERVER>False</WIN_VER_WINDOWS2012R2_STORAGE_WORKGROUP_SERVER>
      <WIN_VER_WINDOWS2012R2_STORAGE_WORKGROUP_SERVER_CORE>False</WIN_VER_WINDOWS2012R2_STORAGE_WORKGROUP_SERVER_CORE>
      <WIN_VER_WINDOWS2012R2_UNDEFINED>False</WIN_VER_WINDOWS2012R2_UNDEFINED>
      <WIN_VER_WINDOWS2012R2_ULTIMATE>False</WIN_VER_WINDOWS2012R2_ULTIMATE>
      <WIN_VER_WINDOWS2012R2_WEB_SERVER>False</WIN_VER_WINDOWS2012R2_WEB_SERVER>
      <WIN_VER_WINDOWS2012R2_WEB_SERVER_CORE>False</WIN_VER_WINDOWS2012R2_WEB_SERVER_CORE>
      <WIN_VER_WINDOWS10>False</WIN_VER_WINDOWS10>
      <WIN_VER_WINDOWS10_UNLICENSED>False</WIN_VER_WINDOWS10_UNLICENSED>
      <WIN_VER_WINDOWS10_BUSINESS>False</WIN_VER_WINDOWS10_BUSINESS>
      <WIN_VER_WINDOWS10_CLUSTER_SERVER>False</WIN_VER_WINDOWS10_CLUSTER_SERVER>
      <WIN_VER_WINDOWS10_CLUSTER_SERVER_V>False</WIN_VER_WINDOWS10_CLUSTER_SERVER_V>
      <WIN_VER_WINDOWS10_CORE>False</WIN_VER_WINDOWS10_CORE>
      <WIN_VER_WINDOWS10_CORE_COUNTRYSPECIFIC>False</WIN_VER_WINDOWS10_CORE_COUNTRYSPECIFIC>
      <WIN_VER_WINDOWS10_CORE_SINGLELANGUAGE>False</WIN_VER_WINDOWS10_CORE_SINGLELANGUAGE>
      <WIN_VER_WINDOWS10_DATACENTER_EVALUATION_SERVER>False</WIN_VER_WINDOWS10_DATACENTER_EVALUATION_SERVER>
      <WIN_VER_WINDOWS10_DATACENTER_SERVER>False</WIN_VER_WINDOWS10_DATACENTER_SERVER>
      <WIN_VER_WINDOWS10_DATACENTER_SERVER_CORE>False</WIN_VER_WINDOWS10_DATACENTER_SERVER_CORE>
      <WIN_VER_WINDOWS10_DATACENTER_SERVER_CORE_V>False</WIN_VER_WINDOWS10_DATACENTER_SERVER_CORE_V>
      <WIN_VER_WINDOWS10_DATACENTER_SERVER_V>False</WIN_VER_WINDOWS10_DATACENTER_SERVER_V>
      <WIN_VER_WINDOWS10_ENTERPRISE>False</WIN_VER_WINDOWS10_ENTERPRISE>
      <WIN_VER_WINDOWS10_ENTERPRISE_N_EVALUATION>False</WIN_VER_WINDOWS10_ENTERPRISE_N_EVALUATION>
      <WIN_VER_WINDOWS10_ENTERPRISE_EVALUATION>False</WIN_VER_WINDOWS10_ENTERPRISE_EVALUATION>
      <WIN_VER_WINDOWS10_ENTERPRISE_SERVER>False</WIN_VER_WINDOWS10_ENTERPRISE_SERVER>
      <WIN_VER_WINDOWS10_ENTERPRISE_SERVER_CORE>False</WIN_VER_WINDOWS10_ENTERPRISE_SERVER_CORE>
      <WIN_VER_WINDOWS10_ENTERPRISE_SERVER_CORE_V>False</WIN_VER_WINDOWS10_ENTERPRISE_SERVER_CORE_V>
      <WIN_VER_WINDOWS10_ENTERPRISE_SERVER_IA64>False</WIN_VER_WINDOWS10_ENTERPRISE_SERVER_IA64>
      <WIN_VER_WINDOWS10_ENTERPRISE_SERVER_V>False</WIN_VER_WINDOWS10_ENTERPRISE_SERVER_V>
      <WIN_VER_WINDOWS10_ESSENTIALBUSINESS_SERVER_MGMT>False</WIN_VER_WINDOWS10_ESSENTIALBUSINESS_SERVER_MGMT>
      <WIN_VER_WINDOWS10_ESSENTIALBUSINESS_SERVER_ADDL>False</WIN_VER_WINDOWS10_ESSENTIALBUSINESS_SERVER_ADDL>
      <WIN_VER_WINDOWS10_ESSENTIALBUSINESS_SERVER_MGMTSVC>False</WIN_VER_WINDOWS10_ESSENTIALBUSINESS_SERVER_MGMTSVC>
      <WIN_VER_WINDOWS10_ESSENTIALBUSINESS_SERVER_ADDLSVC>False</WIN_VER_WINDOWS10_ESSENTIALBUSINESS_SERVER_ADDLSVC>
      <WIN_VER_WINDOWS10_HOME_BASIC>False</WIN_VER_WINDOWS10_HOME_BASIC>
      <WIN_VER_WINDOWS10_HOME_PREMIUM>False</WIN_VER_WINDOWS10_HOME_PREMIUM>
      <WIN_VER_WINDOWS10_HOME_PREMIUM_SERVER>False</WIN_VER_WINDOWS10_HOME_PREMIUM_SERVER>
      <WIN_VER_WINDOWS10_HOME_SERVER>False</WIN_VER_WINDOWS10_HOME_SERVER>
      <WIN_VER_WINDOWS10_HYPERV>False</WIN_VER_WINDOWS10_HYPERV>
      <WIN_VER_WINDOWS10_MEDIUMBUSINESS_SERVER_MANAGEMENT>False</WIN_VER_WINDOWS10_MEDIUMBUSINESS_SERVER_MANAGEMENT>
      <WIN_VER_WINDOWS10_MEDIUMBUSINESS_SERVER_MESSAGING>False</WIN_VER_WINDOWS10_MEDIUMBUSINESS_SERVER_MESSAGING>
      <WIN_VER_WINDOWS10_MEDIUMBUSINESS_SERVER_SECURITY>False</WIN_VER_WINDOWS10_MEDIUMBUSINESS_SERVER_SECURITY>
      <WIN_VER_WINDOWS10_MULTIPOINT_STANDARD_SERVER>False</WIN_VER_WINDOWS10_MULTIPOINT_STANDARD_SERVER>
      <WIN_VER_WINDOWS10_MULTIPOINT_PREMIUM_SERVER>False</WIN_VER_WINDOWS10_MULTIPOINT_PREMIUM_SERVER>
      <WIN_VER_WINDOWS10_PROFESSIONAL>False</WIN_VER_WINDOWS10_PROFESSIONAL>
      <WIN_VER_WINDOWS10_PROFESSIONAL_WMC>False</WIN_VER_WINDOWS10_PROFESSIONAL_WMC>
      <WIN_VER_WINDOWS10_SB_SOLUTION_SERVER_EM>False</WIN_VER_WINDOWS10_SB_SOLUTION_SERVER_EM>
      <WIN_VER_WINDOWS10_SERVER_FOR_SB_SOLUTIONS>False</WIN_VER_WINDOWS10_SERVER_FOR_SB_SOLUTIONS>
      <WIN_VER_WINDOWS10_SERVER_FOR_SB_SOLUTIONS_EM>False</WIN_VER_WINDOWS10_SERVER_FOR_SB_SOLUTIONS_EM>
      <WIN_VER_WINDOWS10_SERVER_FOR_SMALLBUSINESS>False</WIN_VER_WINDOWS10_SERVER_FOR_SMALLBUSINESS>
      <WIN_VER_WINDOWS10_SERVER_FOR_SMALLBUSINESS_V>False</WIN_VER_WINDOWS10_SERVER_FOR_SMALLBUSINESS_V>
      <WIN_VER_WINDOWS10_SERVER_FOUNDATION>False</WIN_VER_WINDOWS10_SERVER_FOUNDATION>
      <WIN_VER_WINDOWS10_SB_SOLUTION_SERVER>False</WIN_VER_WINDOWS10_SB_SOLUTION_SERVER>
      <WIN_VER_WINDOWS10_SMALLBUSINESS_SERVER>False</WIN_VER_WINDOWS10_SMALLBUSINESS_SERVER>
      <WIN_VER_WINDOWS10_SMALLBUSINESS_SERVER_PREMIUM>False</WIN_VER_WINDOWS10_SMALLBUSINESS_SERVER_PREMIUM>
      <WIN_VER_WINDOWS10_SMALLBUSINESS_SERVER_PREMIUM_CORE>False</WIN_VER_WINDOWS10_SMALLBUSINESS_SERVER_PREMIUM_CORE>
      <WIN_VER_WINDOWS10_SOLUTION_EMBEDDEDSERVER>False</WIN_VER_WINDOWS10_SOLUTION_EMBEDDEDSERVER>
      <WIN_VER_WINDOWS10_STANDARD_EVALUATION_SERVER>False</WIN_VER_WINDOWS10_STANDARD_EVALUATION_SERVER>
      <WIN_VER_WINDOWS10_STANDARD_SERVER>False</WIN_VER_WINDOWS10_STANDARD_SERVER>
      <WIN_VER_WINDOWS10_STANDARD_SERVER_CORE>False</WIN_VER_WINDOWS10_STANDARD_SERVER_CORE>
      <WIN_VER_WINDOWS10_STANDARD_SERVER_V>False</WIN_VER_WINDOWS10_STANDARD_SERVER_V>
      <WIN_VER_WINDOWS10_STANDARD_SERVER_CORE_V>False</WIN_VER_WINDOWS10_STANDARD_SERVER_CORE_V>
      <WIN_VER_WINDOWS10_STANDARD_SERVER_SOLUTIONS>False</WIN_VER_WINDOWS10_STANDARD_SERVER_SOLUTIONS>
      <WIN_VER_WINDOWS10_STANDARD_SERVER_SOLUTIONS_CORE>False</WIN_VER_WINDOWS10_STANDARD_SERVER_SOLUTIONS_CORE>
      <WIN_VER_WINDOWS10_STARTER>False</WIN_VER_WINDOWS10_STARTER>
      <WIN_VER_WINDOWS10_STORAGE_ENTERPRISE_SERVER>False</WIN_VER_WINDOWS10_STORAGE_ENTERPRISE_SERVER>
      <WIN_VER_WINDOWS10_STORAGE_ENTERPRISE_SERVER_CORE>False</WIN_VER_WINDOWS10_STORAGE_ENTERPRISE_SERVER_CORE>
      <WIN_VER_WINDOWS10_STORAGE_EXPRESS_SERVER>False</WIN_VER_WINDOWS10_STORAGE_EXPRESS_SERVER>
      <WIN_VER_WINDOWS10_STORAGE_EXPRESS_SERVER_CORE>False</WIN_VER_WINDOWS10_STORAGE_EXPRESS_SERVER_CORE>
      <WIN_VER_WINDOWS10_STORAGE_STANDARD_EVALUATION_SERVER>False</WIN_VER_WINDOWS10_STORAGE_STANDARD_EVALUATION_SERVER>
      <WIN_VER_WINDOWS10_STORAGE_STANDARD_SERVER>False</WIN_VER_WINDOWS10_STORAGE_STANDARD_SERVER>
      <WIN_VER_WINDOWS10_STORAGE_STANDARD_SERVER_CORE>False</WIN_VER_WINDOWS10_STORAGE_STANDARD_SERVER_CORE>
      <WIN_VER_WINDOWS10_STORAGE_WORKGROUP_EVALUATION_SERVER>False</WIN_VER_WINDOWS10_STORAGE_WORKGROUP_EVALUATION_SERVER>
      <WIN_VER_WINDOWS10_STORAGE_WORKGROUP_SERVER>False</WIN_VER_WINDOWS10_STORAGE_WORKGROUP_SERVER>
      <WIN_VER_WINDOWS10_STORAGE_WORKGROUP_SERVER_CORE>False</WIN_VER_WINDOWS10_STORAGE_WORKGROUP_SERVER_CORE>
      <WIN_VER_WINDOWS10_UNDEFINED>False</WIN_VER_WINDOWS10_UNDEFINED>
      <WIN_VER_WINDOWS10_ULTIMATE>False</WIN_VER_WINDOWS10_ULTIMATE>
      <WIN_VER_WINDOWS10_WEB_SERVER>False</WIN_VER_WINDOWS10_WEB_SERVER>
      <WIN_VER_WINDOWS10_WEB_SERVER_CORE>False</WIN_VER_WINDOWS10_WEB_SERVER_CORE>
      <WIN_VER_WINDOWS10_MOBILE_CORE>False</WIN_VER_WINDOWS10_MOBILE_CORE>
      <WIN_VER_WINDOWS10_EMBEDDED_INDUSTRY_EVAL>False</WIN_VER_WINDOWS10_EMBEDDED_INDUSTRY_EVAL>
      <WIN_VER_WINDOWS10_EMBEDDED_INDUSTRY_E_EVAL>False</WIN_VER_WINDOWS10_EMBEDDED_INDUSTRY_E_EVAL>
      <WIN_VER_WINDOWS10_EMBEDDED_EVAL>False</WIN_VER_WINDOWS10_EMBEDDED_EVAL>
      <WIN_VER_WINDOWS10_EMBEDDED_E_EVAL>False</WIN_VER_WINDOWS10_EMBEDDED_E_EVAL>
      <WIN_VER_WINDOWS10_NANO_SERVER>False</WIN_VER_WINDOWS10_NANO_SERVER>
      <WIN_VER_WINDOWS10_CLOUD_STORAGE_SERVER>False</WIN_VER_WINDOWS10_CLOUD_STORAGE_SERVER>
      <WIN_VER_WINDOWS10_CORE_CONNECTED>False</WIN_VER_WINDOWS10_CORE_CONNECTED>
      <WIN_VER_WINDOWS10_PROFESSIONAL_STUDENT>False</WIN_VER_WINDOWS10_PROFESSIONAL_STUDENT>
      <WIN_VER_WINDOWS10_CORE_CONNECTED_SINGLELANGUAGE>False</WIN_VER_WINDOWS10_CORE_CONNECTED_SINGLELANGUAGE>
      <WIN_VER_WINDOWS10_CORE_CONNECTED_COUNTRYSPECIFIC>False</WIN_VER_WINDOWS10_CORE_CONNECTED_COUNTRYSPECIFIC>
      <WIN_VER_WINDOWS10_CONNECTED_CAR>False</WIN_VER_WINDOWS10_CONNECTED_CAR>
      <WIN_VER_WINDOWS10_INDUSTRY_HANDHELD>False</WIN_VER_WINDOWS10_INDUSTRY_HANDHELD>
      <WIN_VER_WINDOWS10_PPI_PRO>False</WIN_VER_WINDOWS10_PPI_PRO>
      <WIN_VER_WINDOWS10_ARM64_SERVER>False</WIN_VER_WINDOWS10_ARM64_SERVER>
      <WIN_VER_WINDOWS10_EDUCATION>False</WIN_VER_WINDOWS10_EDUCATION>
      <WIN_VER_WINDOWS10_IOTUAP>False</WIN_VER_WINDOWS10_IOTUAP>
      <WIN_VER_WINDOWS10_CLOUD_HOST_INFRASTRUCTURE_SERVER>False</WIN_VER_WINDOWS10_CLOUD_HOST_INFRASTRUCTURE_SERVER>
      <WIN_VER_WINDOWS10_ENTERPRISE_S>False</WIN_VER_WINDOWS10_ENTERPRISE_S>
      <WIN_VER_WINDOWS10_PROFESSIONAL_S>False</WIN_VER_WINDOWS10_PROFESSIONAL_S>
      <WIN_VER_WINDOWS10_ENTERPRISE_S_EVALUATION>False</WIN_VER_WINDOWS10_ENTERPRISE_S_EVALUATION>
      <WIN_VER_WINDOWS10_IOTUAPCOMMERCIAL>False</WIN_VER_WINDOWS10_IOTUAPCOMMERCIAL>
      <WIN_VER_WINDOWS10_MOBILE_ENTERPRISE>False</WIN_VER_WINDOWS10_MOBILE_ENTERPRISE>
      <WIN_VER_WINDOWS10_HOLOGRAPHIC>False</WIN_VER_WINDOWS10_HOLOGRAPHIC>
      <WIN_VER_WINDOWS10_PRO_SINGLE_LANGUAGE>False</WIN_VER_WINDOWS10_PRO_SINGLE_LANGUAGE>
      <WIN_VER_WINDOWS10_PRO_CHINA>False</WIN_VER_WINDOWS10_PRO_CHINA>
      <WIN_VER_WINDOWS10_ENTERPRISE_SUBSCRIPTION>False</WIN_VER_WINDOWS10_ENTERPRISE_SUBSCRIPTION>
      <WIN_VER_WINDOWS10_DATACENTER_NANO_SERVER>False</WIN_VER_WINDOWS10_DATACENTER_NANO_SERVER>
      <WIN_VER_WINDOWS10_STANDARD_NANO_SERVER>False</WIN_VER_WINDOWS10_STANDARD_NANO_SERVER>
      <WIN_VER_WINDOWS10_DATACENTER_A_SERVER_CORE>False</WIN_VER_WINDOWS10_DATACENTER_A_SERVER_CORE>
      <WIN_VER_WINDOWS10_STANDARD_A_SERVER_CORE>False</WIN_VER_WINDOWS10_STANDARD_A_SERVER_CORE>
      <WIN_VER_WINDOWS10_DATACENTER_WS_SERVER_CORE>False</WIN_VER_WINDOWS10_DATACENTER_WS_SERVER_CORE>
      <WIN_VER_WINDOWS10_STANDARD_WS_SERVER_CORE>False</WIN_VER_WINDOWS10_STANDARD_WS_SERVER_CORE>
      <WIN_VER_WINDOWS10_UTILITY_VM>False</WIN_VER_WINDOWS10_UTILITY_VM>
      <WIN_VER_WINDOWS10_DATACENTER_EVALUATION_SERVER_CORE>False</WIN_VER_WINDOWS10_DATACENTER_EVALUATION_SERVER_CORE>
      <WIN_VER_WINDOWS10_STANDARD_EVALUATION_SERVER_CORE>False</WIN_VER_WINDOWS10_STANDARD_EVALUATION_SERVER_CORE>
      <WIN_VER_WINDOWS10_PRO_WORKSTATION>False</WIN_VER_WINDOWS10_PRO_WORKSTATION>
      <WIN_VER_WINDOWS10_PRO_FOR_EDUCATION>False</WIN_VER_WINDOWS10_PRO_FOR_EDUCATION>
      <WIN_VER_WINDOWS10_AZURE_SERVER_CORE>False</WIN_VER_WINDOWS10_AZURE_SERVER_CORE>
      <WIN_VER_WINDOWS10_AZURE_NANO_SERVER>False</WIN_VER_WINDOWS10_AZURE_NANO_SERVER>
      <WIN_VER_WINDOWS2016>False</WIN_VER_WINDOWS2016>
      <WIN_VER_WINDOWS2016_UNLICENSED>False</WIN_VER_WINDOWS2016_UNLICENSED>
      <WIN_VER_WINDOWS2016_BUSINESS>False</WIN_VER_WINDOWS2016_BUSINESS>
      <WIN_VER_WINDOWS2016_CLUSTER_SERVER>False</WIN_VER_WINDOWS2016_CLUSTER_SERVER>
      <WIN_VER_WINDOWS2016_CLUSTER_SERVER_V>False</WIN_VER_WINDOWS2016_CLUSTER_SERVER_V>
      <WIN_VER_WINDOWS2016_CORE>False</WIN_VER_WINDOWS2016_CORE>
      <WIN_VER_WINDOWS2016_CORE_COUNTRYSPECIFIC>False</WIN_VER_WINDOWS2016_CORE_COUNTRYSPECIFIC>
      <WIN_VER_WINDOWS2016_CORE_SINGLELANGUAGE>False</WIN_VER_WINDOWS2016_CORE_SINGLELANGUAGE>
      <WIN_VER_WINDOWS2016_DATACENTER_EVALUATION_SERVER>False</WIN_VER_WINDOWS2016_DATACENTER_EVALUATION_SERVER>
      <WIN_VER_WINDOWS2016_DATACENTER_SERVER>False</WIN_VER_WINDOWS2016_DATACENTER_SERVER>
      <WIN_VER_WINDOWS2016_DATACENTER_SERVER_CORE>False</WIN_VER_WINDOWS2016_DATACENTER_SERVER_CORE>
      <WIN_VER_WINDOWS2016_DATACENTER_SERVER_CORE_V>False</WIN_VER_WINDOWS2016_DATACENTER_SERVER_CORE_V>
      <WIN_VER_WINDOWS2016_DATACENTER_SERVER_V>False</WIN_VER_WINDOWS2016_DATACENTER_SERVER_V>
      <WIN_VER_WINDOWS2016_ENTERPRISE>False</WIN_VER_WINDOWS2016_ENTERPRISE>
      <WIN_VER_WINDOWS2016_ENTERPRISE_N_EVALUATION>False</WIN_VER_WINDOWS2016_ENTERPRISE_N_EVALUATION>
      <WIN_VER_WINDOWS2016_ENTERPRISE_EVALUATION>False</WIN_VER_WINDOWS2016_ENTERPRISE_EVALUATION>
      <WIN_VER_WINDOWS2016_ENTERPRISE_SERVER>False</WIN_VER_WINDOWS2016_ENTERPRISE_SERVER>
      <WIN_VER_WINDOWS2016_ENTERPRISE_SERVER_CORE>False</WIN_VER_WINDOWS2016_ENTERPRISE_SERVER_CORE>
      <WIN_VER_WINDOWS2016_ENTERPRISE_SERVER_CORE_V>False</WIN_VER_WINDOWS2016_ENTERPRISE_SERVER_CORE_V>
      <WIN_VER_WINDOWS2016_ENTERPRISE_SERVER_IA64>False</WIN_VER_WINDOWS2016_ENTERPRISE_SERVER_IA64>
      <WIN_VER_WINDOWS2016_ENTERPRISE_SERVER_V>False</WIN_VER_WINDOWS2016_ENTERPRISE_SERVER_V>
      <WIN_VER_WINDOWS2016_ESSENTIALBUSINESS_SERVER_MGMT>False</WIN_VER_WINDOWS2016_ESSENTIALBUSINESS_SERVER_MGMT>
      <WIN_VER_WINDOWS2016_ESSENTIALBUSINESS_SERVER_ADDL>False</WIN_VER_WINDOWS2016_ESSENTIALBUSINESS_SERVER_ADDL>
      <WIN_VER_WINDOWS2016_ESSENTIALBUSINESS_SERVER_MGMTSVC>False</WIN_VER_WINDOWS2016_ESSENTIALBUSINESS_SERVER_MGMTSVC>
      <WIN_VER_WINDOWS2016_ESSENTIALBUSINESS_SERVER_ADDLSVC>False</WIN_VER_WINDOWS2016_ESSENTIALBUSINESS_SERVER_ADDLSVC>
      <WIN_VER_WINDOWS2016_HOME_BASIC>False</WIN_VER_WINDOWS2016_HOME_BASIC>
      <WIN_VER_WINDOWS2016_HOME_PREMIUM>False</WIN_VER_WINDOWS2016_HOME_PREMIUM>
      <WIN_VER_WINDOWS2016_HOME_PREMIUM_SERVER>False</WIN_VER_WINDOWS2016_HOME_PREMIUM_SERVER>
      <WIN_VER_WINDOWS2016_HOME_SERVER>False</WIN_VER_WINDOWS2016_HOME_SERVER>
      <WIN_VER_WINDOWS2016_HYPERV>False</WIN_VER_WINDOWS2016_HYPERV>
      <WIN_VER_WINDOWS2016_MEDIUMBUSINESS_SERVER_MANAGEMENT>False</WIN_VER_WINDOWS2016_MEDIUMBUSINESS_SERVER_MANAGEMENT>
      <WIN_VER_WINDOWS2016_MEDIUMBUSINESS_SERVER_MESSAGING>False</WIN_VER_WINDOWS2016_MEDIUMBUSINESS_SERVER_MESSAGING>
      <WIN_VER_WINDOWS2016_MEDIUMBUSINESS_SERVER_SECURITY>False</WIN_VER_WINDOWS2016_MEDIUMBUSINESS_SERVER_SECURITY>
      <WIN_VER_WINDOWS2016_MULTIPOINT_STANDARD_SERVER>False</WIN_VER_WINDOWS2016_MULTIPOINT_STANDARD_SERVER>
      <WIN_VER_WINDOWS2016_MULTIPOINT_PREMIUM_SERVER>False</WIN_VER_WINDOWS2016_MULTIPOINT_PREMIUM_SERVER>
      <WIN_VER_WINDOWS2016_PROFESSIONAL>False</WIN_VER_WINDOWS2016_PROFESSIONAL>
      <WIN_VER_WINDOWS2016_PROFESSIONAL_WMC>False</WIN_VER_WINDOWS2016_PROFESSIONAL_WMC>
      <WIN_VER_WINDOWS2016_SB_SOLUTION_SERVER_EM>False</WIN_VER_WINDOWS2016_SB_SOLUTION_SERVER_EM>
      <WIN_VER_WINDOWS2016_SERVER_FOR_SB_SOLUTIONS>False</WIN_VER_WINDOWS2016_SERVER_FOR_SB_SOLUTIONS>
      <WIN_VER_WINDOWS2016_SERVER_FOR_SB_SOLUTIONS_EM>False</WIN_VER_WINDOWS2016_SERVER_FOR_SB_SOLUTIONS_EM>
      <WIN_VER_WINDOWS2016_SERVER_FOR_SMALLBUSINESS>False</WIN_VER_WINDOWS2016_SERVER_FOR_SMALLBUSINESS>
      <WIN_VER_WINDOWS2016_SERVER_FOR_SMALLBUSINESS_V>False</WIN_VER_WINDOWS2016_SERVER_FOR_SMALLBUSINESS_V>
      <WIN_VER_WINDOWS2016_SERVER_FOUNDATION>False</WIN_VER_WINDOWS2016_SERVER_FOUNDATION>
      <WIN_VER_WINDOWS2016_SB_SOLUTION_SERVER>False</WIN_VER_WINDOWS2016_SB_SOLUTION_SERVER>
      <WIN_VER_WINDOWS2016_SMALLBUSINESS_SERVER>False</WIN_VER_WINDOWS2016_SMALLBUSINESS_SERVER>
      <WIN_VER_WINDOWS2016_SMALLBUSINESS_SERVER_PREMIUM>False</WIN_VER_WINDOWS2016_SMALLBUSINESS_SERVER_PREMIUM>
      <WIN_VER_WINDOWS2016_SMALLBUSINESS_SERVER_PREMIUM_CORE>False</WIN_VER_WINDOWS2016_SMALLBUSINESS_SERVER_PREMIUM_CORE>
      <WIN_VER_WINDOWS2016_SOLUTION_EMBEDDEDSERVER>False</WIN_VER_WINDOWS2016_SOLUTION_EMBEDDEDSERVER>
      <WIN_VER_WINDOWS2016_STANDARD_EVALUATION_SERVER>False</WIN_VER_WINDOWS2016_STANDARD_EVALUATION_SERVER>
      <WIN_VER_WINDOWS2016_STANDARD_SERVER>False</WIN_VER_WINDOWS2016_STANDARD_SERVER>
      <WIN_VER_WINDOWS2016_STANDARD_SERVER_CORE>False</WIN_VER_WINDOWS2016_STANDARD_SERVER_CORE>
      <WIN_VER_WINDOWS2016_STANDARD_SERVER_V>False</WIN_VER_WINDOWS2016_STANDARD_SERVER_V>
      <WIN_VER_WINDOWS2016_STANDARD_SERVER_CORE_V>False</WIN_VER_WINDOWS2016_STANDARD_SERVER_CORE_V>
      <WIN_VER_WINDOWS2016_STANDARD_SERVER_SOLUTIONS>False</WIN_VER_WINDOWS2016_STANDARD_SERVER_SOLUTIONS>
      <WIN_VER_WINDOWS2016_STANDARD_SERVER_SOLUTIONS_CORE>False</WIN_VER_WINDOWS2016_STANDARD_SERVER_SOLUTIONS_CORE>
      <WIN_VER_WINDOWS2016_STARTER>False</WIN_VER_WINDOWS2016_STARTER>
      <WIN_VER_WINDOWS2016_STORAGE_ENTERPRISE_SERVER>False</WIN_VER_WINDOWS2016_STORAGE_ENTERPRISE_SERVER>
      <WIN_VER_WINDOWS2016_STORAGE_ENTERPRISE_SERVER_CORE>False</WIN_VER_WINDOWS2016_STORAGE_ENTERPRISE_SERVER_CORE>
      <WIN_VER_WINDOWS2016_STORAGE_EXPRESS_SERVER>False</WIN_VER_WINDOWS2016_STORAGE_EXPRESS_SERVER>
      <WIN_VER_WINDOWS2016_STORAGE_EXPRESS_SERVER_CORE>False</WIN_VER_WINDOWS2016_STORAGE_EXPRESS_SERVER_CORE>
      <WIN_VER_WINDOWS2016_STORAGE_STANDARD_EVALUATION_SERVER>False</WIN_VER_WINDOWS2016_STORAGE_STANDARD_EVALUATION_SERVER>
      <WIN_VER_WINDOWS2016_STORAGE_STANDARD_SERVER>False</WIN_VER_WINDOWS2016_STORAGE_STANDARD_SERVER>
      <WIN_VER_WINDOWS2016_STORAGE_STANDARD_SERVER_CORE>False</WIN_VER_WINDOWS2016_STORAGE_STANDARD_SERVER_CORE>
      <WIN_VER_WINDOWS2016_STORAGE_WORKGROUP_EVALUATION_SERVER>False</WIN_VER_WINDOWS2016_STORAGE_WORKGROUP_EVALUATION_SERVER>
      <WIN_VER_WINDOWS2016_STORAGE_WORKGROUP_SERVER>False</WIN_VER_WINDOWS2016_STORAGE_WORKGROUP_SERVER>
      <WIN_VER_WINDOWS2016_STORAGE_WORKGROUP_SERVER_CORE>False</WIN_VER_WINDOWS2016_STORAGE_WORKGROUP_SERVER_CORE>
      <WIN_VER_WINDOWS2016_UNDEFINED>False</WIN_VER_WINDOWS2016_UNDEFINED>
      <WIN_VER_WINDOWS2016_ULTIMATE>False</WIN_VER_WINDOWS2016_ULTIMATE>
      <WIN_VER_WINDOWS2016_WEB_SERVER>False</WIN_VER_WINDOWS2016_WEB_SERVER>
      <WIN_VER_WINDOWS2016_WEB_SERVER_CORE>False</WIN_VER_WINDOWS2016_WEB_SERVER_CORE>
      <WIN_VER_WINDOWS2016_MOBILE_CORE>False</WIN_VER_WINDOWS2016_MOBILE_CORE>
      <WIN_VER_WINDOWS2016_EMBEDDED_INDUSTRY_EVAL>False</WIN_VER_WINDOWS2016_EMBEDDED_INDUSTRY_EVAL>
      <WIN_VER_WINDOWS2016_EMBEDDED_INDUSTRY_E_EVAL>False</WIN_VER_WINDOWS2016_EMBEDDED_INDUSTRY_E_EVAL>
      <WIN_VER_WINDOWS2016_EMBEDDED_EVAL>False</WIN_VER_WINDOWS2016_EMBEDDED_EVAL>
      <WIN_VER_WINDOWS2016_EMBEDDED_E_EVAL>False</WIN_VER_WINDOWS2016_EMBEDDED_E_EVAL>
      <WIN_VER_WINDOWS2016_NANO_SERVER>False</WIN_VER_WINDOWS2016_NANO_SERVER>
      <WIN_VER_WINDOWS2016_CLOUD_STORAGE_SERVER>False</WIN_VER_WINDOWS2016_CLOUD_STORAGE_SERVER>
      <WIN_VER_WINDOWS2016_CORE_CONNECTED>False</WIN_VER_WINDOWS2016_CORE_CONNECTED>
      <WIN_VER_WINDOWS2016_PROFESSIONAL_STUDENT>False</WIN_VER_WINDOWS2016_PROFESSIONAL_STUDENT>
      <WIN_VER_WINDOWS2016_CORE_CONNECTED_SINGLELANGUAGE>False</WIN_VER_WINDOWS2016_CORE_CONNECTED_SINGLELANGUAGE>
      <WIN_VER_WINDOWS2016_CORE_CONNECTED_COUNTRYSPECIFIC>False</WIN_VER_WINDOWS2016_CORE_CONNECTED_COUNTRYSPECIFIC>
      <WIN_VER_WINDOWS2016_CONNECTED_CAR>False</WIN_VER_WINDOWS2016_CONNECTED_CAR>
      <WIN_VER_WINDOWS2016_INDUSTRY_HANDHELD>False</WIN_VER_WINDOWS2016_INDUSTRY_HANDHELD>
      <WIN_VER_WINDOWS2016_PPI_PRO>False</WIN_VER_WINDOWS2016_PPI_PRO>
      <WIN_VER_WINDOWS2016_ARM64_SERVER>False</WIN_VER_WINDOWS2016_ARM64_SERVER>
      <WIN_VER_WINDOWS2016_EDUCATION>False</WIN_VER_WINDOWS2016_EDUCATION>
      <WIN_VER_WINDOWS2016_IOTUAP>False</WIN_VER_WINDOWS2016_IOTUAP>
      <WIN_VER_WINDOWS2016_CLOUD_HOST_INFRASTRUCTURE_SERVER>False</WIN_VER_WINDOWS2016_CLOUD_HOST_INFRASTRUCTURE_SERVER>
      <WIN_VER_WINDOWS2016_ENTERPRISE_S>False</WIN_VER_WINDOWS2016_ENTERPRISE_S>
      <WIN_VER_WINDOWS2016_PROFESSIONAL_S>False</WIN_VER_WINDOWS2016_PROFESSIONAL_S>
      <WIN_VER_WINDOWS2016_ENTERPRISE_S_EVALUATION>False</WIN_VER_WINDOWS2016_ENTERPRISE_S_EVALUATION>
      <WIN_VER_WINDOWS2016_IOTUAPCOMMERCIAL>False</WIN_VER_WINDOWS2016_IOTUAPCOMMERCIAL>
      <WIN_VER_WINDOWS2016_MOBILE_ENTERPRISE>False</WIN_VER_WINDOWS2016_MOBILE_ENTERPRISE>
      <WIN_VER_WINDOWS2016_HOLOGRAPHIC>False</WIN_VER_WINDOWS2016_HOLOGRAPHIC>
      <WIN_VER_WINDOWS2016_PRO_SINGLE_LANGUAGE>False</WIN_VER_WINDOWS2016_PRO_SINGLE_LANGUAGE>
      <WIN_VER_WINDOWS2016_PRO_CHINA>False</WIN_VER_WINDOWS2016_PRO_CHINA>
      <WIN_VER_WINDOWS2016_ENTERPRISE_SUBSCRIPTION>False</WIN_VER_WINDOWS2016_ENTERPRISE_SUBSCRIPTION>
      <WIN_VER_WINDOWS2016_DATACENTER_NANO_SERVER>False</WIN_VER_WINDOWS2016_DATACENTER_NANO_SERVER>
      <WIN_VER_WINDOWS2016_STANDARD_NANO_SERVER>False</WIN_VER_WINDOWS2016_STANDARD_NANO_SERVER>
      <WIN_VER_WINDOWS2016_DATACENTER_A_SERVER_CORE>False</WIN_VER_WINDOWS2016_DATACENTER_A_SERVER_CORE>
      <WIN_VER_WINDOWS2016_STANDARD_A_SERVER_CORE>False</WIN_VER_WINDOWS2016_STANDARD_A_SERVER_CORE>
      <WIN_VER_WINDOWS2016_DATACENTER_WS_SERVER_CORE>False</WIN_VER_WINDOWS2016_DATACENTER_WS_SERVER_CORE>
      <WIN_VER_WINDOWS2016_STANDARD_WS_SERVER_CORE>False</WIN_VER_WINDOWS2016_STANDARD_WS_SERVER_CORE>
      <WIN_VER_WINDOWS2016_UTILITY_VM>False</WIN_VER_WINDOWS2016_UTILITY_VM>
      <WIN_VER_WINDOWS2016_DATACENTER_EVALUATION_SERVER_CORE>False</WIN_VER_WINDOWS2016_DATACENTER_EVALUATION_SERVER_CORE>
      <WIN_VER_WINDOWS2016_STANDARD_EVALUATION_SERVER_CORE>False</WIN_VER_WINDOWS2016_STANDARD_EVALUATION_SERVER_CORE>
      <WIN_VER_WINDOWS2016_PRO_WORKSTATION>False</WIN_VER_WINDOWS2016_PRO_WORKSTATION>
      <WIN_VER_WINDOWS2016_PRO_FOR_EDUCATION>False</WIN_VER_WINDOWS2016_PRO_FOR_EDUCATION>
      <WIN_VER_WINDOWS2016_AZURE_SERVER_CORE>False</WIN_VER_WINDOWS2016_AZURE_SERVER_CORE>
      <WIN_VER_WINDOWS2016_AZURE_NANO_SERVER>False</WIN_VER_WINDOWS2016_AZURE_NANO_SERVER>
      <Allowance>Deny</Allowance>
      <WIN_VER_WINDOWS11>False</WIN_VER_WINDOWS11>
      <WIN_VER_WINDOWS11_UNLICENSED>False</WIN_VER_WINDOWS11_UNLICENSED>
      <WIN_VER_WINDOWS11_BUSINESS>False</WIN_VER_WINDOWS11_BUSINESS>
      <WIN_VER_WINDOWS11_CLUSTER_SERVER>False</WIN_VER_WINDOWS11_CLUSTER_SERVER>
      <WIN_VER_WINDOWS11_CLUSTER_SERVER_V>False</WIN_VER_WINDOWS11_CLUSTER_SERVER_V>
      <WIN_VER_WINDOWS11_CORE>False</WIN_VER_WINDOWS11_CORE>
      <WIN_VER_WINDOWS11_CORE_COUNTRYSPECIFIC>False</WIN_VER_WINDOWS11_CORE_COUNTRYSPECIFIC>
      <WIN_VER_WINDOWS11_CORE_SINGLELANGUAGE>False</WIN_VER_WINDOWS11_CORE_SINGLELANGUAGE>
      <WIN_VER_WINDOWS11_DATACENTER_EVALUATION_SERVER>False</WIN_VER_WINDOWS11_DATACENTER_EVALUATION_SERVER>
      <WIN_VER_WINDOWS11_DATACENTER_SERVER>False</WIN_VER_WINDOWS11_DATACENTER_SERVER>
      <WIN_VER_WINDOWS11_DATACENTER_SERVER_CORE>False</WIN_VER_WINDOWS11_DATACENTER_SERVER_CORE>
      <WIN_VER_WINDOWS11_DATACENTER_SERVER_CORE_V>False</WIN_VER_WINDOWS11_DATACENTER_SERVER_CORE_V>
      <WIN_VER_WINDOWS11_DATACENTER_SERVER_V>False</WIN_VER_WINDOWS11_DATACENTER_SERVER_V>
      <WIN_VER_WINDOWS11_ENTERPRISE>False</WIN_VER_WINDOWS11_ENTERPRISE>
      <WIN_VER_WINDOWS11_ENTERPRISE_N_EVALUATION>False</WIN_VER_WINDOWS11_ENTERPRISE_N_EVALUATION>
      <WIN_VER_WINDOWS11_ENTERPRISE_EVALUATION>False</WIN_VER_WINDOWS11_ENTERPRISE_EVALUATION>
      <WIN_VER_WINDOWS11_ENTERPRISE_SERVER>False</WIN_VER_WINDOWS11_ENTERPRISE_SERVER>
      <WIN_VER_WINDOWS11_ENTERPRISE_SERVER_CORE>False</WIN_VER_WINDOWS11_ENTERPRISE_SERVER_CORE>
      <WIN_VER_WINDOWS11_ENTERPRISE_SERVER_CORE_V>False</WIN_VER_WINDOWS11_ENTERPRISE_SERVER_CORE_V>
      <WIN_VER_WINDOWS11_ENTERPRISE_SERVER_IA64>False</WIN_VER_WINDOWS11_ENTERPRISE_SERVER_IA64>
      <WIN_VER_WINDOWS11_ENTERPRISE_SERVER_V>False</WIN_VER_WINDOWS11_ENTERPRISE_SERVER_V>
      <WIN_VER_WINDOWS11_ESSENTIALBUSINESS_SERVER_MGMT>False</WIN_VER_WINDOWS11_ESSENTIALBUSINESS_SERVER_MGMT>
      <WIN_VER_WINDOWS11_ESSENTIALBUSINESS_SERVER_ADDL>False</WIN_VER_WINDOWS11_ESSENTIALBUSINESS_SERVER_ADDL>
      <WIN_VER_WINDOWS11_ESSENTIALBUSINESS_SERVER_MGMTSVC>False</WIN_VER_WINDOWS11_ESSENTIALBUSINESS_SERVER_MGMTSVC>
      <WIN_VER_WINDOWS11_ESSENTIALBUSINESS_SERVER_ADDLSVC>False</WIN_VER_WINDOWS11_ESSENTIALBUSINESS_SERVER_ADDLSVC>
      <WIN_VER_WINDOWS11_HOME_BASIC>False</WIN_VER_WINDOWS11_HOME_BASIC>
      <WIN_VER_WINDOWS11_HOME_PREMIUM>False</WIN_VER_WINDOWS11_HOME_PREMIUM>
      <WIN_VER_WINDOWS11_HOME_PREMIUM_SERVER>False</WIN_VER_WINDOWS11_HOME_PREMIUM_SERVER>
      <WIN_VER_WINDOWS11_HOME_SERVER>False</WIN_VER_WINDOWS11_HOME_SERVER>
      <WIN_VER_WINDOWS11_HYPERV>False</WIN_VER_WINDOWS11_HYPERV>
      <WIN_VER_WINDOWS11_MEDIUMBUSINESS_SERVER_MANAGEMENT>False</WIN_VER_WINDOWS11_MEDIUMBUSINESS_SERVER_MANAGEMENT>
      <WIN_VER_WINDOWS11_MEDIUMBUSINESS_SERVER_MESSAGING>False</WIN_VER_WINDOWS11_MEDIUMBUSINESS_SERVER_MESSAGING>
      <WIN_VER_WINDOWS11_MEDIUMBUSINESS_SERVER_SECURITY>False</WIN_VER_WINDOWS11_MEDIUMBUSINESS_SERVER_SECURITY>
      <WIN_VER_WINDOWS11_MULTIPOINT_STANDARD_SERVER>False</WIN_VER_WINDOWS11_MULTIPOINT_STANDARD_SERVER>
      <WIN_VER_WINDOWS11_MULTIPOINT_PREMIUM_SERVER>False</WIN_VER_WINDOWS11_MULTIPOINT_PREMIUM_SERVER>
      <WIN_VER_WINDOWS11_PROFESSIONAL>False</WIN_VER_WINDOWS11_PROFESSIONAL>
      <WIN_VER_WINDOWS11_PROFESSIONAL_WMC>False</WIN_VER_WINDOWS11_PROFESSIONAL_WMC>
      <WIN_VER_WINDOWS11_SB_SOLUTION_SERVER_EM>False</WIN_VER_WINDOWS11_SB_SOLUTION_SERVER_EM>
      <WIN_VER_WINDOWS11_SERVER_FOR_SB_SOLUTIONS>False</WIN_VER_WINDOWS11_SERVER_FOR_SB_SOLUTIONS>
      <WIN_VER_WINDOWS11_SERVER_FOR_SB_SOLUTIONS_EM>False</WIN_VER_WINDOWS11_SERVER_FOR_SB_SOLUTIONS_EM>
      <WIN_VER_WINDOWS11_SERVER_FOR_SMALLBUSINESS>False</WIN_VER_WINDOWS11_SERVER_FOR_SMALLBUSINESS>
      <WIN_VER_WINDOWS11_SERVER_FOR_SMALLBUSINESS_V>False</WIN_VER_WINDOWS11_SERVER_FOR_SMALLBUSINESS_V>
      <WIN_VER_WINDOWS11_SERVER_FOUNDATION>False</WIN_VER_WINDOWS11_SERVER_FOUNDATION>
      <WIN_VER_WINDOWS11_SB_SOLUTION_SERVER>False</WIN_VER_WINDOWS11_SB_SOLUTION_SERVER>
      <WIN_VER_WINDOWS11_SMALLBUSINESS_SERVER>False</WIN_VER_WINDOWS11_SMALLBUSINESS_SERVER>
      <WIN_VER_WINDOWS11_SMALLBUSINESS_SERVER_PREMIUM>False</WIN_VER_WINDOWS11_SMALLBUSINESS_SERVER_PREMIUM>
      <WIN_VER_WINDOWS11_SMALLBUSINESS_SERVER_PREMIUM_CORE>False</WIN_VER_WINDOWS11_SMALLBUSINESS_SERVER_PREMIUM_CORE>
      <WIN_VER_WINDOWS11_SOLUTION_EMBEDDEDSERVER>False</WIN_VER_WINDOWS11_SOLUTION_EMBEDDEDSERVER>
      <WIN_VER_WINDOWS11_STANDARD_EVALUATION_SERVER>False</WIN_VER_WINDOWS11_STANDARD_EVALUATION_SERVER>
      <WIN_VER_WINDOWS11_STANDARD_SERVER>False</WIN_VER_WINDOWS11_STANDARD_SERVER>
      <WIN_VER_WINDOWS11_STANDARD_SERVER_CORE>False</WIN_VER_WINDOWS11_STANDARD_SERVER_CORE>
      <WIN_VER_WINDOWS11_STANDARD_SERVER_V>False</WIN_VER_WINDOWS11_STANDARD_SERVER_V>
      <WIN_VER_WINDOWS11_STANDARD_SERVER_CORE_V>False</WIN_VER_WINDOWS11_STANDARD_SERVER_CORE_V>
      <WIN_VER_WINDOWS11_STANDARD_SERVER_SOLUTIONS>False</WIN_VER_WINDOWS11_STANDARD_SERVER_SOLUTIONS>
      <WIN_VER_WINDOWS11_STANDARD_SERVER_SOLUTIONS_CORE>False</WIN_VER_WINDOWS11_STANDARD_SERVER_SOLUTIONS_CORE>
      <WIN_VER_WINDOWS11_STARTER>False</WIN_VER_WINDOWS11_STARTER>
      <WIN_VER_WINDOWS11_STORAGE_ENTERPRISE_SERVER>False</WIN_VER_WINDOWS11_STORAGE_ENTERPRISE_SERVER>
      <WIN_VER_WINDOWS11_STORAGE_ENTERPRISE_SERVER_CORE>False</WIN_VER_WINDOWS11_STORAGE_ENTERPRISE_SERVER_CORE>
      <WIN_VER_WINDOWS11_STORAGE_EXPRESS_SERVER>False</WIN_VER_WINDOWS11_STORAGE_EXPRESS_SERVER>
      <WIN_VER_WINDOWS11_STORAGE_EXPRESS_SERVER_CORE>False</WIN_VER_WINDOWS11_STORAGE_EXPRESS_SERVER_CORE>
      <WIN_VER_WINDOWS11_STORAGE_STANDARD_EVALUATION_SERVER>False</WIN_VER_WINDOWS11_STORAGE_STANDARD_EVALUATION_SERVER>
      <WIN_VER_WINDOWS11_STORAGE_STANDARD_SERVER>False</WIN_VER_WINDOWS11_STORAGE_STANDARD_SERVER>
      <WIN_VER_WINDOWS11_STORAGE_STANDARD_SERVER_CORE>False</WIN_VER_WINDOWS11_STORAGE_STANDARD_SERVER_CORE>
      <WIN_VER_WINDOWS11_STORAGE_WORKGROUP_EVALUATION_SERVER>False</WIN_VER_WINDOWS11_STORAGE_WORKGROUP_EVALUATION_SERVER>
      <WIN_VER_WINDOWS11_STORAGE_WORKGROUP_SERVER>False</WIN_VER_WINDOWS11_STORAGE_WORKGROUP_SERVER>
      <WIN_VER_WINDOWS11_STORAGE_WORKGROUP_SERVER_CORE>False</WIN_VER_WINDOWS11_STORAGE_WORKGROUP_SERVER_CORE>
      <WIN_VER_WINDOWS11_UNDEFINED>False</WIN_VER_WINDOWS11_UNDEFINED>
      <WIN_VER_WINDOWS11_ULTIMATE>False</WIN_VER_WINDOWS11_ULTIMATE>
      <WIN_VER_WINDOWS11_WEB_SERVER>False</WIN_VER_WINDOWS11_WEB_SERVER>
      <WIN_VER_WINDOWS11_WEB_SERVER_CORE>False</WIN_VER_WINDOWS11_WEB_SERVER_CORE>
      <WIN_VER_WINDOWS11_MOBILE_CORE>False</WIN_VER_WINDOWS11_MOBILE_CORE>
      <WIN_VER_WINDOWS11_EMBEDDED_INDUSTRY_EVAL>False</WIN_VER_WINDOWS11_EMBEDDED_INDUSTRY_EVAL>
      <WIN_VER_WINDOWS11_EMBEDDED_INDUSTRY_E_EVAL>False</WIN_VER_WINDOWS11_EMBEDDED_INDUSTRY_E_EVAL>
      <WIN_VER_WINDOWS11_EMBEDDED_EVAL>False</WIN_VER_WINDOWS11_EMBEDDED_EVAL>
      <WIN_VER_WINDOWS11_EMBEDDED_E_EVAL>False</WIN_VER_WINDOWS11_EMBEDDED_E_EVAL>
      <WIN_VER_WINDOWS11_NANO_SERVER>False</WIN_VER_WINDOWS11_NANO_SERVER>
      <WIN_VER_WINDOWS11_CLOUD_STORAGE_SERVER>False</WIN_VER_WINDOWS11_CLOUD_STORAGE_SERVER>
      <WIN_VER_WINDOWS11_CORE_CONNECTED>False</WIN_VER_WINDOWS11_CORE_CONNECTED>
      <WIN_VER_WINDOWS11_PROFESSIONAL_STUDENT>False</WIN_VER_WINDOWS11_PROFESSIONAL_STUDENT>
      <WIN_VER_WINDOWS11_CORE_CONNECTED_SINGLELANGUAGE>False</WIN_VER_WINDOWS11_CORE_CONNECTED_SINGLELANGUAGE>
      <WIN_VER_WINDOWS11_CORE_CONNECTED_COUNTRYSPECIFIC>False</WIN_VER_WINDOWS11_CORE_CONNECTED_COUNTRYSPECIFIC>
      <WIN_VER_WINDOWS11_CONNECTED_CAR>False</WIN_VER_WINDOWS11_CONNECTED_CAR>
      <WIN_VER_WINDOWS11_INDUSTRY_HANDHELD>False</WIN_VER_WINDOWS11_INDUSTRY_HANDHELD>
      <WIN_VER_WINDOWS11_PPI_PRO>False</WIN_VER_WINDOWS11_PPI_PRO>
      <WIN_VER_WINDOWS11_ARM64_SERVER>False</WIN_VER_WINDOWS11_ARM64_SERVER>
      <WIN_VER_WINDOWS11_EDUCATION>False</WIN_VER_WINDOWS11_EDUCATION>
      <WIN_VER_WINDOWS11_IOTUAP>False</WIN_VER_WINDOWS11_IOTUAP>
      <WIN_VER_WINDOWS11_CLOUD_HOST_INFRASTRUCTURE_SERVER>False</WIN_VER_WINDOWS11_CLOUD_HOST_INFRASTRUCTURE_SERVER>
      <WIN_VER_WINDOWS11_ENTERPRISE_S>False</WIN_VER_WINDOWS11_ENTERPRISE_S>
      <WIN_VER_WINDOWS11_PROFESSIONAL_S>False</WIN_VER_WINDOWS11_PROFESSIONAL_S>
      <WIN_VER_WINDOWS11_ENTERPRISE_S_EVALUATION>False</WIN_VER_WINDOWS11_ENTERPRISE_S_EVALUATION>
      <WIN_VER_WINDOWS11_IOTUAPCOMMERCIAL>False</WIN_VER_WINDOWS11_IOTUAPCOMMERCIAL>
      <WIN_VER_WINDOWS11_MOBILE_ENTERPRISE>False</WIN_VER_WINDOWS11_MOBILE_ENTERPRISE>
      <WIN_VER_WINDOWS11_HOLOGRAPHIC>False</WIN_VER_WINDOWS11_HOLOGRAPHIC>
      <WIN_VER_WINDOWS11_PRO_SINGLE_LANGUAGE>False</WIN_VER_WINDOWS11_PRO_SINGLE_LANGUAGE>
      <WIN_VER_WINDOWS11_PRO_CHINA>False</WIN_VER_WINDOWS11_PRO_CHINA>
      <WIN_VER_WINDOWS11_ENTERPRISE_SUBSCRIPTION>False</WIN_VER_WINDOWS11_ENTERPRISE_SUBSCRIPTION>
      <WIN_VER_WINDOWS11_DATACENTER_NANO_SERVER>False</WIN_VER_WINDOWS11_DATACENTER_NANO_SERVER>
      <WIN_VER_WINDOWS11_STANDARD_NANO_SERVER>False</WIN_VER_WINDOWS11_STANDARD_NANO_SERVER>
      <WIN_VER_WINDOWS11_DATACENTER_A_SERVER_CORE>False</WIN_VER_WINDOWS11_DATACENTER_A_SERVER_CORE>
      <WIN_VER_WINDOWS11_STANDARD_A_SERVER_CORE>False</WIN_VER_WINDOWS11_STANDARD_A_SERVER_CORE>
      <WIN_VER_WINDOWS11_DATACENTER_WS_SERVER_CORE>False</WIN_VER_WINDOWS11_DATACENTER_WS_SERVER_CORE>
      <WIN_VER_WINDOWS11_STANDARD_WS_SERVER_CORE>False</WIN_VER_WINDOWS11_STANDARD_WS_SERVER_CORE>
      <WIN_VER_WINDOWS11_UTILITY_VM>False</WIN_VER_WINDOWS11_UTILITY_VM>
      <WIN_VER_WINDOWS11_DATACENTER_EVALUATION_SERVER_CORE>False</WIN_VER_WINDOWS11_DATACENTER_EVALUATION_SERVER_CORE>
      <WIN_VER_WINDOWS11_STANDARD_EVALUATION_SERVER_CORE>False</WIN_VER_WINDOWS11_STANDARD_EVALUATION_SERVER_CORE>
      <WIN_VER_WINDOWS11_PRO_WORKSTATION>False</WIN_VER_WINDOWS11_PRO_WORKSTATION>
      <WIN_VER_WINDOWS11_PRO_FOR_EDUCATION>False</WIN_VER_WINDOWS11_PRO_FOR_EDUCATION>
      <WIN_VER_WINDOWS11_AZURE_SERVER_CORE>False</WIN_VER_WINDOWS11_AZURE_SERVER_CORE>
      <WIN_VER_WINDOWS11_AZURE_NANO_SERVER>False</WIN_VER_WINDOWS11_AZURE_NANO_SERVER>
    </WindowsVersion>
    <VirtualizationTools>
      <Enabled>False</Enabled>
      <VMWare>False</VMWare>
      <VirtualPC>False</VirtualPC>
      <Sandboxie>False</Sandboxie>
      <TerminateExecution>True</TerminateExecution>
      <ShowMessageBeforeTermination>True</ShowMessageBeforeTermination>
      <Message>
        <Title>Warning</Title>
        <Text>This program can not be run under %CU_VIRTTOOLS%!</Text>
        <UseHL>False</UseHL>
        <HLSentence>Enigma Protector</HLSentence>
        <HLURL>http://www.enigmaprotector.com/</HLURL>
        <Icon>0</Icon>
        <AutoClose>False</AutoClose>
        <CloseDelay>0</CloseDelay>
        <Disable>False</Disable>
        <DisableDelay>0</DisableDelay>
        <FontName/>
        <FontCharset>0</FontCharset>
        <FontColor>0</FontColor>
        <FontSize>0</FontSize>
        <FontBold>False</FontBold>
        <FontItalic>False</FontItalic>
        <FontUnderline>False</FontUnderline>
        <FontStrikeout>False</FontStrikeout>
      </Message>
      <VirtualBox>False</VirtualBox>
      <HyperVGuest>False</HyperVGuest>
      <HyperV>False</HyperV>
      <Parallels>False</Parallels>
    </VirtualizationTools>
    <Privileges>
      <Administrator>
        <Enabled>False</Enabled>
        <ShowMessageBeforeTermination>False</ShowMessageBeforeTermination>
        <Message>
          <Title>Warning</Title>
          <Text>You have not enough permissions to run this application. 
Please switch on administrator privileges of your Windows account and 
try again!</Text>
          <UseHL>False</UseHL>
          <HLSentence>Enigma Protector</HLSentence>
          <HLURL>http://enigmaprotector.com/</HLURL>
          <Icon>3</Icon>
          <AutoClose>False</AutoClose>
          <CloseDelay>0</CloseDelay>
          <Disable>False</Disable>
          <DisableDelay>0</DisableDelay>
          <FontName/>
          <FontCharset>0</FontCharset>
          <FontColor>0</FontColor>
          <FontSize>0</FontSize>
          <FontBold>False</FontBold>
          <FontItalic>False</FontItalic>
          <FontUnderline>False</FontUnderline>
          <FontStrikeout>False</FontStrikeout>
        </Message>
      </Administrator>
    </Privileges>
  </CheckUp>
  <Protection>
    <FileAnalyzerDeception>
      <NameOfTheProtectionCodeSection>.data</NameOfTheProtectionCodeSection>
      <ProtectionCodeMarker>MSVisualCPP</ProtectionCodeMarker>
    </FileAnalyzerDeception>
    <OriginalFileSizePreservation>
      <Enabled>False</Enabled>
    </OriginalFileSizePreservation>
    <AdvanceForceImportProtection>
      <Enabled>True</Enabled>
    </AdvanceForceImportProtection>
    <WinAPIFunctionsRedirection>
      <Enabled>True</Enabled>
    </WinAPIFunctionsRedirection>
    <WinAPIFunctionsEmulation>
      <Enabled>True</Enabled>
    </WinAPIFunctionsEmulation>
    <InlinePatching>
      <Enabled>True</Enabled>
      <Threads>3</Threads>
      <Interval>1000</Interval>
      <ProtectCode>True</ProtectCode>
    </InlinePatching>
    <ProtectedStrings>
      <Strings Count="15">
        <String>
          <ID>1</ID>
          <Key>cHZLMEYv</Key>
          <Type>0</Type>
          <String>Database.R3volutioN</String>
          <Comment/>
        </String>
        <String>
          <ID>2</ID>
          <Key>NZdmLj7c</Key>
          <Type>0</Type>
          <String>http://kalonline.ovh/REMOTE/Check.php?key=</String>
          <Comment/>
        </String>
        <String>
          <ID>3</ID>
          <Key>dFRRLdnR</Key>
          <Type>0</Type>
          <String>Administrators</String>
          <Comment/>
        </String>
        <String>
          <ID>4</ID>
          <Key>Pjcjhnfm</Key>
          <Type>0</Type>
          <String>EV)O8@BL$3O2E</String>
          <Comment/>
        </String>
        <String>
          <ID>5</ID>
          <Key>JqThO39Q</Key>
          <Type>0</Type>
          <String>KalOnline</String>
          <Comment/>
        </String>
        <String>
          <ID>6</ID>
          <Key>8vK7FoaU</Key>
          <Type>0</Type>
          <String>Data\Config\md5.dat</String>
          <Comment/>
        </String>
        <String>
          <ID>7</ID>
          <Key>nXNIDJ96</Key>
          <Type>0</Type>
          <String>n</String>
          <Comment/>
        </String>
        <String>
          <ID>8</ID>
          <Key>UR7SACNv</Key>
          <Type>0</Type>
          <String>(name </String>
          <Comment/>
        </String>
        <String>
          <ID>9</ID>
          <Key>ETalEua0</Key>
          <Type>0</Type>
          <String>(key O</String>
          <Comment/>
        </String>
        <String>
          <ID>10</ID>
          <Key>PxGZr7Ga</Key>
          <Type>0</Type>
          <String>)</String>
          <Comment/>
        </String>
        <String>
          <ID>11</ID>
          <Key>jf9oDqHv</Key>
          <Type>0</Type>
          <String>))</String>
          <Comment/>
        </String>
        <String>
          <ID>12</ID>
          <Key>mOnxfgFy</Key>
          <Type>0</Type>
          <String> have invalid md5! Please update your client!</String>
          <Comment/>
        </String>
        <String>
          <ID>13</ID>
          <Key>hVzjcqKG</Key>
          <Type>0</Type>
          <String>Data\Config\ItemRestriction.dat</String>
          <Comment/>
        </String>
        <String>
          <ID>14</ID>
          <Key>jqROiMwF</Key>
          <Type>0</Type>
          <String>(ksm </String>
          <Comment/>
        </String>
        <String>
          <ID>15</ID>
          <Key>sbxCm31F</Key>
          <Type>0</Type>
          <String>(index </String>
          <Comment/>
        </String>
      </Strings>
    </ProtectedStrings>
    <ResourcesProtection>
      <Type>0</Type>
      <SkipDialog>False</SkipDialog>
      <SkipIconAndGroupIcon>True</SkipIconAndGroupIcon>
      <SkipVersion>True</SkipVersion>
      <SkipString>True</SkipString>
    </ResourcesProtection>
  </Protection>
  <VirtualBox>
    <Secure1>123EB6A79BCADD79803BD1B379052B126E02557493520DE4B2C3EB81BF9AA47198BEC817D5CB21795304946F67020E98204F58137F9CFBEEFBC68F148FD1EA5B856A7C8746BF5BA0EF17F20AB622258C0789BD52E1A63C187C68A744E980B2B2A47A27E34BB0768CF6031FFD6D2400D9E06BE8BA188906E423D56E5493CBCBACE3C1522A23ADEF9372134A89D670B8F76154F8E0C1D343C80BAEF9D488F237C5EB1CD4D0976C7952403DDCF4609EB655754BD378CC7D1D719E7BFEA2125DEF6BF6808ACD689AF813C2F409A0AEF20624F61484EA8A5D68427F3595FA36445847D3DDC936613E0C10C2C7AEB5BEF87C503E8C183099D376D81B736850CC4FCDF0</Secure1>
    <Secure2>4FD843D4DCE7BA1B7C1E9DAFBBE037A61F1786006EB1635D8FCD059AC92E3588D027CC1D9B0449441F439EB871E028C040C3485F0CE27C78D6A230AA9B4E5299FA6E0ED120B68824C8128BD2A129F2EEBADFD7A5F59FF26275A4D0C9EC6F0E6E7ACB4B8B153B6F968840290F3CE38DC4DDC2B3748A19F95BA1A755260E0CB9E99201B41495B2DD73428EDECA51231472EFC995B19446C2629D355754CF8CDA3DC281D5CA383173967E01753433F0A24D7302B9AD9FBF56895FA6362B2E29BC4F6E824C18E43BA31B226B928926AEC3CEE189E505D082B1FC81EB5CE7CFFD29592D98A4A2BD3028811C0B795FF2B7DFFA61E014AA6BC0D74A54CE500197F1B28A</Secure2>
    <PrivateKey>020U5P6H4TF6UWEY8847YDTR9L6VCGP9D6E0AD2QNJRURNH5UVA3B7SLHPGPCAY9NA8Y4FGSC9EBWVUDNA6FLXQMQ53K7J22HV3JRUW4QDYDQL694AZ85HVQBEGX9ZYXUJ4H3ACRC98AUCYSUB7NJVRFEB2LSQ3CK9WN8QYLBB8K9TATG4QQBNZAMBQCRDMZ2FPZNC3U5ZDUFP9RULY020WCUJBX3ASZUQH9UJV4V5FG9H6FXGM2Z3020WCUJBX3ASZUQH9UJV4V5FG9H6FXGM2Z3</PrivateKey>
    <PublicKey>0206C7D82AE7FD1F28BCF66F7BC0B78E8760CDKVVENARULT74WALH6QP4BQGBGT43ESBMFZAZXLZ9BK69Z4SEEF3GXMAR84EV38KLWW3RA23GXN2NXUEX86R8JFTZDSE8KVE9ZPQB2Q2FVWZ38QG6SUR2Y34F58EGA2LSMN32LB9H485EGSC9PGEJPA3XSDA4ELCCDUSVH95CCRKCEBKPYYNGXH4GXWJVL84SLBXJ3HJDE6US50CDBELRPYSC9UNBY2VNV9ELDVG5ZTUZ7VFF73GRNXAXK552EHSA4UV8A79XYHLUFS4JF8HZCRMT7LWQHRASN7YENVAHQEMR6ZZECFFGUCFFZCSKZRPNE4UEKXAUY7PWWR4WLVUSY5WD2RBMCRPEMPEDUJ6C33FT34XZQLAE5Y9PW2L4FC3YFRT6SYQK9CQVM8TNHB6BDQ8JDPE9G0CDRL8SLBQU5NDFRSR4AFUEK74ZR2NY66D38NPW4KWFHAS9MDHF6Q2DS3KNGYH9TP88U636YQSN4PAGG8W95XLUSSXJ9PUMB86VCJSAGDYW75N3WUFWMLQFK2DFFJW24YPPCZHPVN8368Y4YCZCC9HJVUTZG2Q4KXY5K2382P8QTT4P6DQXD2FTQ92YBZLFP5Y8VTPS6KE9L7SFH</PublicKey>
    <Files>
      <Enabled>False</Enabled>
      <DeleteExtractedOnExit>False</DeleteExtractedOnExit>
      <CompressFiles>False</CompressFiles>
      <Files/>
    </Files>
    <Registries>
      <Enabled>False</Enabled>
      <Registries>
        <Registry>
          <Type>1</Type>
          <Virtual>True</Virtual>
          <Name>Classes</Name>
          <ValueType>0</ValueType>
          <Value/>
          <Registries/>
        </Registry>
        <Registry>
          <Type>1</Type>
          <Virtual>True</Virtual>
          <Name>User</Name>
          <ValueType>0</ValueType>
          <Value/>
          <Registries/>
        </Registry>
        <Registry>
          <Type>1</Type>
          <Virtual>True</Virtual>
          <Name>Machine</Name>
          <ValueType>0</ValueType>
          <Value/>
          <Registries/>
        </Registry>
        <Registry>
          <Type>1</Type>
          <Virtual>True</Virtual>
          <Name>Users</Name>
          <ValueType>0</ValueType>
          <Value/>
          <Registries/>
        </Registry>
        <Registry>
          <Type>1</Type>
          <Virtual>True</Virtual>
          <Name>Config</Name>
          <ValueType>0</ValueType>
          <Value/>
          <Registries/>
        </Registry>
      </Registries>
    </Registries>
    <Packaging>
      <Enabled>False</Enabled>
      <CheckIntegrity>True</CheckIntegrity>
    </Packaging>
    <Options>
      <MapExecutableWithTemporaryFile>True</MapExecutableWithTemporaryFile>
      <ShareVirtualSystemToChildProcesses>False</ShareVirtualSystemToChildProcesses>
      <AllowRunningOfVirtualExeFiles>True</AllowRunningOfVirtualExeFiles>
      <HideFromDialogs>False</HideFromDialogs>
      <TemporaryFileMask/>
      <ProcessesOfAnyPlatforms>False</ProcessesOfAnyPlatforms>
      <FolderForTemporaryFiles>%Temp FOLDER%</FolderForTemporaryFiles>
    </Options>
    <Storage>
      <Files>
        <Enabled>False</Enabled>
        <Folder>%DEFAULT FOLDER%\</Folder>
        <EncryptContent>False</EncryptContent>
      </Files>
    </Storage>
  </VirtualBox>
  <VirtualMachine>
    <Enabled>False</Enabled>
    <Filter/>
    <CommandLine/>
    <Functions Count="0"/>
    <FileEntryPoint>
      <Enabled>True</Enabled>
    </FileEntryPoint>
    <Common>
      <VMType>0</VMType>
      <RISCVMTrashgen>5</RISCVMTrashgen>
      <RISCVMObfuscation>2</RISCVMObfuscation>
      <RISCVMDuplicates>2</RISCVMDuplicates>
      <RISCVMEncryption>2</RISCVMEncryption>
    </Common>
  </VirtualMachine>
  <Miscellaneous>
    <SplashScreenEncapsulation>
      <Enabled>False</Enabled>
      <SplashScreenPictureFileName/>
      <ShowOnTop>True</ShowOnTop>
      <TimeToShow>2000</TimeToShow>
      <StopExecution>False</StopExecution>
      <CloseOnMouseClick>False</CloseOnMouseClick>
      <ShowAtStart>True</ShowAtStart>
    </SplashScreenEncapsulation>
    <Watermarks>
      <Enabled>False</Enabled>
      <Watermarks Count="0"/>
    </Watermarks>
    <Plugins>
      <Plugins Count="19">
        <Plugin ID="0">
          <Name>checkremotedebuggerpresent.dll</Name>
          <Data/>
        </Plugin>
        <Plugin ID="1">
          <Name>closehandle.dll</Name>
          <Data/>
        </Plugin>
        <Plugin ID="2">
          <Name>debugobjects.dll</Name>
          <Data/>
        </Plugin>
        <Plugin ID="3">
          <Name>emulatorsdetect.dll</Name>
          <Data/>
        </Plugin>
        <Plugin ID="4">
          <Name>getstartupinfo.dll</Name>
          <Data/>
        </Plugin>
        <Plugin ID="5">
          <Name>hardwarebreakpoints.dll</Name>
          <Data/>
        </Plugin>
        <Plugin ID="6">
          <Name>heapcheck.dll</Name>
          <Data/>
        </Plugin>
        <Plugin ID="7">
          <Name>hidecurrentthread.dll</Name>
          <Data/>
        </Plugin>
        <Plugin ID="8">
          <Name>int3.dll</Name>
          <Data/>
        </Plugin>
        <Plugin ID="9">
          <Name>int3check.dll</Name>
          <Data/>
        </Plugin>
        <Plugin ID="10">
          <Name>isdebuggerpresent.dll</Name>
          <Data/>
        </Plugin>
        <Plugin ID="11">
          <Name>isdebuggerpresentx.dll</Name>
          <Data/>
        </Plugin>
        <Plugin ID="12">
          <Name>ntflags.dll</Name>
          <Data/>
        </Plugin>
        <Plugin ID="13">
          <Name>ntforceflags.dll</Name>
          <Data/>
        </Plugin>
        <Plugin ID="14">
          <Name>ntglobalflag.dll</Name>
          <Data/>
        </Plugin>
        <Plugin ID="15">
          <Name>ntqueryinformationprocess.dll</Name>
          <Data/>
        </Plugin>
        <Plugin ID="16">
          <Name>outputdebugstring.dll</Name>
          <Data/>
        </Plugin>
        <Plugin ID="17">
          <Name>sandboxiedetect.dll</Name>
          <Data/>
        </Plugin>
        <Plugin ID="18">
          <Name>writeprocessmemoryinject.dll</Name>
          <Data/>
        </Plugin>
      </Plugins>
    </Plugins>
    <CustomVersionResource>
      <Enabled>False</Enabled>
      <MajorVersion>1</MajorVersion>
      <MinorVersion>0</MinorVersion>
      <Release>0</Release>
      <Build>0</Build>
      <DebugBuild>False</DebugBuild>
      <Pre-release>False</Pre-release>
      <SpecialBuild>False</SpecialBuild>
      <PrivateBuild>False</PrivateBuild>
      <DLL>False</DLL>
      <Language>1033</Language>
      <CompanyName>The Enigma Protector Developers Team</CompanyName>
      <FileDescription>Software Protection Tool</FileDescription>
      <FileVersion>*******</FileVersion>
      <InternalName>ENIGMA.EXE</InternalName>
      <LegalCopyright>Copyrights (C) 2002-2009 Vladimir Sukhov</LegalCopyright>
      <LegalTrademarks>Trademarks (R) 2002-2009 Vladimir Sukhov</LegalTrademarks>
      <OriginalFilename>enigma.exe</OriginalFilename>
      <ProductName>The Enigma Protector</ProductName>
      <ProductVersion>*******</ProductVersion>
      <Comments>http://enigmaprotector.com/</Comments>
    </CustomVersionResource>
    <CustomManifestResource>
      <Enabled>False</Enabled>
      <Template>2</Template>
      <TemplateFile/>
      <Manifest>&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot; standalone=&quot;yes&quot;?&gt;
&lt;assembly
    xmlns=&quot;urn:schemas-microsoft-com:asm.v1&quot;
    manifestVersion=&quot;1.0&quot;&gt;
    &lt;assemblyIdentity
        version=&quot;*******&quot;
        processorArchitecture=&quot;X86&quot;
        name=&quot;Enigma.exe&quot;
        type=&quot;win32&quot; /&gt;
    &lt;description&gt;The Enigma Protector&lt;/description&gt;
    &lt;dependency&gt;
        &lt;dependentAssembly&gt;
            &lt;assemblyIdentity
                type=&quot;win32&quot;
                name=&quot;Microsoft.Windows.Common-Controls&quot;
                version=&quot;*******&quot;
                processorArchitecture=&quot;X86&quot;
                publicKeyToken=&quot;6595b64144ccf1df&quot;
                language=&quot;*&quot; /&gt;
        &lt;/dependentAssembly&gt;
    &lt;/dependency&gt;
&lt;/assembly&gt;</Manifest>
    </CustomManifestResource>
    <CommandLine>
      <Enabled>False</Enabled>
      <Override>True</Override>
      <CommandLine/>
    </CommandLine>
    <EnvironmentVariables>
      <Variables Count="0"/>
    </EnvironmentVariables>
    <Other>
      <SupressGraphics>False</SupressGraphics>
      <DoNotCheckIfCompressed>False</DoNotCheckIfCompressed>
      <DoNotSearchMarkers>False</DoNotSearchMarkers>
      <NETSmartAssemblyHooking>False</NETSmartAssemblyHooking>
      <RemoveOverlayFromProtectedFile>False</RemoveOverlayFromProtectedFile>
      <KeepOriginalImportDirectory>False</KeepOriginalImportDirectory>
      <DoNotCompressEncryptCode>False</DoNotCompressEncryptCode>
      <DeleteDebugDirectory>True</DeleteDebugDirectory>
      <HookEntireProcessForEnigmaApiCalls>True</HookEntireProcessForEnigmaApiCalls>
      <CustomMemoryManager>False</CustomMemoryManager>
      <TaggantTimestamp>True</TaggantTimestamp>
      <TaggantTimestampUrl>http://taggant-tsa.ieee.org/</TaggantTimestampUrl>
      <DeleteRelocationsDirectory>False</DeleteRelocationsDirectory>
      <NetApplicationsFreezingFixWindowsXP>False</NetApplicationsFreezingFixWindowsXP>
      <CompressAndEncrypt>
        <Type>0</Type>
      </CompressAndEncrypt>
      <CompatibleWithSetUnhandledExceptionFilter>False</CompatibleWithSetUnhandledExceptionFilter>
    </Other>
    <ProtectionEvents>
      <Events Count="0"/>
    </ProtectionEvents>
  </Miscellaneous>
  <TrialControl>
    <Common>
      <ResetTrialInNewVersion>False</ResetTrialInNewVersion>
      <OpenFileIfTrialExpired>False</OpenFileIfTrialExpired>
      <OpenFileName>application.exe</OpenFileName>
    </Common>
    <TrialStoring>
      <Enabled>False</Enabled>
      <Items Count="0"/>
    </TrialStoring>
    <LockTrialToUserLanguage>
      <Enabled>False</Enabled>
      <ALB>True</ALB>
      <DZA>True</DZA>
      <ARG>True</ARG>
      <ARM>True</ARM>
      <AUS>True</AUS>
      <AUT>True</AUT>
      <AZE>True</AZE>
      <BHR>True</BHR>
      <BLR>True</BLR>
      <BEL>True</BEL>
      <BLZ>True</BLZ>
      <BOL>True</BOL>
      <BRA>True</BRA>
      <BRN>True</BRN>
      <BGR>True</BGR>
      <KHM>True</KHM>
      <CAN>True</CAN>
      <CAF>True</CAF>
      <TCD>True</TCD>
      <CHL>True</CHL>
      <CHN>True</CHN>
      <COL>True</COL>
      <CRI>True</CRI>
      <HRV>True</HRV>
      <CZE>True</CZE>
      <DNK>True</DNK>
      <DOM>True</DOM>
      <ECU>True</ECU>
      <EGY>True</EGY>
      <SLV>True</SLV>
      <EST>True</EST>
      <FRO>True</FRO>
      <FIN>True</FIN>
      <FRA>True</FRA>
      <GEO>True</GEO>
      <DEU>True</DEU>
      <GRC>True</GRC>
      <GTM>True</GTM>
      <HND>True</HND>
      <HKG>True</HKG>
      <HUN>True</HUN>
      <ISL>True</ISL>
      <IND>True</IND>
      <IDN>True</IDN>
      <IRN>True</IRN>
      <IRQ>True</IRQ>
      <IRL>True</IRL>
      <ISR>True</ISR>
      <ITA>True</ITA>
      <JAM>True</JAM>
      <JPN>True</JPN>
      <JOR>True</JOR>
      <KAZ>True</KAZ>
      <KEN>True</KEN>
      <PRK>True</PRK>
      <KOR>True</KOR>
      <KWT>True</KWT>
      <KGZ>True</KGZ>
      <LVA>True</LVA>
      <LBN>True</LBN>
      <LBR>True</LBR>
      <LIE>True</LIE>
      <LTU>True</LTU>
      <LUX>True</LUX>
      <MAC>True</MAC>
      <MKD>True</MKD>
      <MYS>True</MYS>
      <MEX>True</MEX>
      <MDA>True</MDA>
      <MCO>True</MCO>
      <MNG>True</MNG>
      <MAR>True</MAR>
      <NLD>True</NLD>
      <NZL>True</NZL>
      <NIC>True</NIC>
      <NOR>True</NOR>
      <OMN>True</OMN>
      <PAK>True</PAK>
      <PAN>True</PAN>
      <PRY>True</PRY>
      <PER>True</PER>
      <PHL>True</PHL>
      <POL>True</POL>
      <PRT>True</PRT>
      <PRI>True</PRI>
      <QAT>True</QAT>
      <ROU>True</ROU>
      <RUS>True</RUS>
      <SAU>True</SAU>
      <SCG>True</SCG>
      <SGP>True</SGP>
      <SVK>True</SVK>
      <SVN>True</SVN>
      <ZAF>True</ZAF>
      <ESP>True</ESP>
      <SWE>True</SWE>
      <CHE>True</CHE>
      <TJK>True</TJK>
      <THA>True</THA>
      <TTO>True</TTO>
      <TUN>True</TUN>
      <TUR>True</TUR>
      <TWN>True</TWN>
      <UKR>True</UKR>
      <ARE>True</ARE>
      <GBR>True</GBR>
      <USA>True</USA>
      <URY>True</URY>
      <UZB>True</UZB>
      <VEN>True</VEN>
      <VNM>True</VNM>
      <YEM>True</YEM>
      <ZWE>True</ZWE>
      <AFG>True</AFG>
      <BGD>True</BGD>
      <BIH>True</BIH>
      <CAR>True</CAR>
      <ETH>True</ETH>
      <GRL>True</GRL>
      <LAO>True</LAO>
      <LBY>True</LBY>
      <MDV>True</MDV>
      <MLT>True</MLT>
      <MNE>True</MNE>
      <NEP>True</NEP>
      <NGA>True</NGA>
      <ROM>True</ROM>
      <RWA>True</RWA>
      <SEN>True</SEN>
      <SRB>True</SRB>
      <LKA>True</LKA>
      <SYR>True</SYR>
      <TAJ>True</TAJ>
      <TKM>True</TKM>
    </LockTrialToUserLanguage>
    <CountOfExecutionLimit>
      <Enabled>False</Enabled>
      <MaximalCountOfExecutions>0</MaximalCountOfExecutions>
      <TerminateAfterDelay>True</TerminateAfterDelay>
      <Delay>0</Delay>
      <ShowMessageBeforeTermination>True</ShowMessageBeforeTermination>
      <Message>
        <Title>Trial has expired!</Title>
        <Text>Sorry, but your trial period has expired!
You have exceeded %TrialExecsTotal% trial executions!
Please, register this software to remove trial limitations!</Text>
        <UseHL>False</UseHL>
        <HLSentence>Click here for registration</HLSentence>
        <HLURL>http://www.yourcompany.com/</HLURL>
        <Icon>0</Icon>
        <AutoClose>False</AutoClose>
        <CloseDelay>0</CloseDelay>
        <Disable>False</Disable>
        <DisableDelay>0</DisableDelay>
        <FontName/>
        <FontCharset>0</FontCharset>
        <FontColor>0</FontColor>
        <FontSize>0</FontSize>
        <FontBold>False</FontBold>
        <FontItalic>False</FontItalic>
        <FontUnderline>False</FontUnderline>
        <FontStrikeout>False</FontStrikeout>
      </Message>
    </CountOfExecutionLimit>
    <CountOfDaysLimit>
      <Enabled>False</Enabled>
      <MaximalCountOfDays>0</MaximalCountOfDays>
      <TerminateAfterDelay>True</TerminateAfterDelay>
      <Delay>0</Delay>
      <ShowMessageBeforeTermination>True</ShowMessageBeforeTermination>
      <Message>
        <Title>Trial has expired!</Title>
        <Text>Sorry, but your trial period has expired!
You have exceeded %TrialDaysTotal% trial days!
Please, register this software to remove trial limitations!</Text>
        <UseHL>False</UseHL>
        <HLSentence>Click here for registration</HLSentence>
        <HLURL>http://www.yourcompany.com/</HLURL>
        <Icon>0</Icon>
        <AutoClose>False</AutoClose>
        <CloseDelay>0</CloseDelay>
        <Disable>False</Disable>
        <DisableDelay>0</DisableDelay>
        <FontName/>
        <FontCharset>0</FontCharset>
        <FontColor>0</FontColor>
        <FontSize>0</FontSize>
        <FontBold>False</FontBold>
        <FontItalic>False</FontItalic>
        <FontUnderline>False</FontUnderline>
        <FontStrikeout>False</FontStrikeout>
      </Message>
    </CountOfDaysLimit>
    <ExpirationDateLimit>
      <Enabled>False</Enabled>
      <ExpirationDate>2007-12-19T23:59:30.629Z</ExpirationDate>
      <TerminateAfterDelay>True</TerminateAfterDelay>
      <Delay>0</Delay>
      <ShowMessageBeforeTermination>True</ShowMessageBeforeTermination>
      <Message>
        <Title>Trial has expired!</Title>
        <Text>Sorry, but your trial period has expired!
The expiration date is %TrialExpDay%/%TrialExpMonth%/%TrialExpYear% (dd/mm/yyyy)
Please, register this software to remove trial limitations!</Text>
        <UseHL>False</UseHL>
        <HLSentence>Click here for registration</HLSentence>
        <HLURL>http://www.yourcompany.com/</HLURL>
        <Icon>0</Icon>
        <AutoClose>False</AutoClose>
        <CloseDelay>0</CloseDelay>
        <Disable>False</Disable>
        <DisableDelay>0</DisableDelay>
        <FontName/>
        <FontCharset>0</FontCharset>
        <FontColor>0</FontColor>
        <FontSize>0</FontSize>
        <FontBold>False</FontBold>
        <FontItalic>False</FontItalic>
        <FontUnderline>False</FontUnderline>
        <FontStrikeout>False</FontStrikeout>
      </Message>
    </ExpirationDateLimit>
    <ExpirationFromDateTillDate>
      <Enabled>False</Enabled>
      <StartDate>2007-12-19T23:59:30.629Z</StartDate>
      <EndDate>2007-12-19T23:59:30.629Z</EndDate>
      <TerminateAfterDelay>True</TerminateAfterDelay>
      <Delay>0</Delay>
      <ShowMessageBeforeTermination>True</ShowMessageBeforeTermination>
      <Message>
        <Title>Trial has expired!</Title>
        <Text>Sorry, but your trial period has expired!
You can use it from %TrialStartDay%/%TrialStartMonth%/%TrialStartYear% till %TrialEndDay%/%TrialEndMonth%/%TrialEndYear% (dd/mm/yyyy)
Please, register this software to remove trial limitations!</Text>
        <UseHL>False</UseHL>
        <HLSentence>Click here for registration</HLSentence>
        <HLURL>http://www.yourcompany.com/</HLURL>
        <Icon>0</Icon>
        <AutoClose>False</AutoClose>
        <CloseDelay>0</CloseDelay>
        <Disable>False</Disable>
        <DisableDelay>0</DisableDelay>
        <FontName/>
        <FontCharset>0</FontCharset>
        <FontColor>0</FontColor>
        <FontSize>0</FontSize>
        <FontBold>False</FontBold>
        <FontItalic>False</FontItalic>
        <FontUnderline>False</FontUnderline>
        <FontStrikeout>False</FontStrikeout>
      </Message>
    </ExpirationFromDateTillDate>
    <ExecutionTimeLimit>
      <Enabled>False</Enabled>
      <NumberOfMinutes>0</NumberOfMinutes>
      <TerminateOnExpiration>True</TerminateOnExpiration>
      <ShowMessageBeforeTermination>True</ShowMessageBeforeTermination>
      <Message>
        <Title>Trial has expired!</Title>
        <Text>Sorry, but your trial execution time has expired! 
Application works only %TrialExecMinsTotal% minutes since start.
Please, register this software to remove trial limitations!</Text>
        <UseHL>False</UseHL>
        <HLSentence>Register it now!</HLSentence>
        <HLURL>http://www.yourcompany.com/</HLURL>
        <Icon>0</Icon>
        <AutoClose>False</AutoClose>
        <CloseDelay>0</CloseDelay>
        <Disable>False</Disable>
        <DisableDelay>0</DisableDelay>
        <FontName/>
        <FontCharset>0</FontCharset>
        <FontColor>0</FontColor>
        <FontSize>0</FontSize>
        <FontBold>False</FontBold>
        <FontItalic>False</FontItalic>
        <FontUnderline>False</FontUnderline>
        <FontStrikeout>False</FontStrikeout>
      </Message>
    </ExecutionTimeLimit>
    <Reminder>
      <Enabled>False</Enabled>
      <PeriodOfIterations>0</PeriodOfIterations>
      <Message>
        <Title>Shareware reminder!</Title>
        <Text>This program is shareware - you get to try it before you buy it. 
If you want to continue using it after the trial 
period you will have to buy it.
Please register to remove this nag-screen!
</Text>
        <UseHL>False</UseHL>
        <HLSentence>Click here for registration</HLSentence>
        <HLURL>http://www.yourcompany.com/</HLURL>
        <Icon>0</Icon>
        <AutoClose>False</AutoClose>
        <CloseDelay>0</CloseDelay>
        <Disable>False</Disable>
        <DisableDelay>0</DisableDelay>
        <FontName/>
        <FontCharset>0</FontCharset>
        <FontColor>0</FontColor>
        <FontSize>0</FontSize>
        <FontBold>False</FontBold>
        <FontItalic>False</FontItalic>
        <FontUnderline>False</FontUnderline>
        <FontStrikeout>False</FontStrikeout>
      </Message>
    </Reminder>
    <TimeControl>
      <TerminateOnClockReversing>False</TerminateOnClockReversing>
      <ShowMessageBeforeTermination>True</ShowMessageBeforeTermination>
      <Message>
        <Title>Clock reversing</Title>
        <Text>The software had determined that the system clock
had been turned back. Please set the correct date. 
This program will now close.</Text>
        <UseHL>False</UseHL>
        <HLSentence>Click here for registration</HLSentence>
        <HLURL>http://www.yourcompany.com/</HLURL>
        <Icon>0</Icon>
        <AutoClose>False</AutoClose>
        <CloseDelay>0</CloseDelay>
        <Disable>False</Disable>
        <DisableDelay>0</DisableDelay>
        <FontName/>
        <FontCharset>0</FontCharset>
        <FontColor>0</FontColor>
        <FontSize>0</FontSize>
        <FontBold>False</FontBold>
        <FontItalic>False</FontItalic>
        <FontUnderline>False</FontUnderline>
        <FontStrikeout>False</FontStrikeout>
      </Message>
      <DoNotCheckIfRegistered>False</DoNotCheckIfRegistered>
      <UseProtectionDate>False</UseProtectionDate>
      <Terminate>True</Terminate>
    </TimeControl>
  </TrialControl>
  <Other>
    <CommandLine/>
  </Other>
</EnigmaProject>
