<?xml version='1.0' encoding='utf-8' ?>
<!-- C++Builder XML Project -->
<PROJECT>
  <MACROS>
    <VERSION value="BCB.05.03"/>
    <PROJECT value="imagview.exe"/>
    <OBJFILES value="IMAGVIEW.obj imagewn.obj ViewFrm.obj"/>
    <RESFILES value="imagview.res"/>
    <IDLFILES value=""/>
    <IDLGENFILES value=""/>
    <DEFFILE value=""/>
    <RESDEPEN value="$(RESFILES) imagewn.dfm ViewFrm.dfm"/>
    <LIBFILES value=""/>
    <LIBRARIES value="vclx50.lib vcl50.lib"/>
    <SPARELIBS value="vcl50.lib vclx50.lib"/>
    <PACKAGES value=""/>
    <PATHCPP value=".;"/>
    <PATHPAS value=".;"/>
    <PATHRC value=".;"/>
    <PATHASM value=".;"/>
    <DEBUGLIBPATH value="$(BCB)\lib\debug"/>
    <RELEASELIBPATH value="$(BCB)\lib\release"/>
    <LINKER value="ilink32"/>
    <USERDEFINES value=""/>
    <SYSDEFINES value="NO_STRICT"/>
    <MAINSOURCE value="IMAGVIEW.cpp"/>
    <INCLUDEPATH value="$(BCB)\include;$(BCB)\include\vcl"/>
    <LIBPATH value="$(BCB)\lib\obj;$(BCB)\lib"/>
    <WARNINGS value="-w-8026 -w-8027 -w-par"/>
  </MACROS>
  <OPTIONS>
    <IDLCFLAGS value="-src_suffixcpp -I$(BCB)\include -I$(BCB)\include\vcl"/>
    <CFLAG1 value="-Od -H=$(BCB)\lib\vcl50.csm -Hc -Vx -Ve -r- -a8 -k -y -v -vi- -c -b- -tW"/>
    <PFLAGS value="-$YD -v -JPHNV -M"/>
    <RFLAGS value=""/>
    <AFLAGS value="/mx /w0 /zd"/>
    <LFLAGS value="-aa -Tpe -x -v"/>
  </OPTIONS>
  <LINKER>
    <ALLOBJ value="c0w32.obj sysinit.obj $(OBJFILES)"/>
    <ALLRES value="$(RESFILES)"/>
    <ALLLIB value="$(LIBFILES) $(LIBRARIES) import32.lib cp32mt.lib"/>
  </LINKER>
  <IDEOPTIONS>
[Version Info]
IncludeVerInfo=0
AutoIncBuild=0
MajorVer=1
MinorVer=0
Release=0
Build=0
Debug=0
PreRelease=0
Special=0
Private=0
DLL=0
Locale=1033
CodePage=1252

[HistoryLists\hlIncludePath]
Count=1
Item0=$(BCB)\include;$(BCB)\include\vcl

[HistoryLists\hlLibraryPath]
Count=1
Item0=$(BCB)\lib\obj;$(BCB)\lib

[Debugging]
DebugSourceDirs=

[Parameters]
RunParams=
HostApplication=
RemoteHost=
RemotePath=
RemoteDebug=0

[Compiler]
InMemoryExe=0
ShowInfoMsgs=0
LinkDebugVcl=1
  </IDEOPTIONS>
</PROJECT>