//Microsoft Developer Studio generated resource script.
//
#include "resource.h"

#define APSTUDIO_READONLY_SYMBOLS
/////////////////////////////////////////////////////////////////////////////
//
// Generated from the TEXTINCLUDE 2 resource.
//
#include "afxres.h"

/////////////////////////////////////////////////////////////////////////////
#undef APSTUDIO_READONLY_SYMBOLS

/////////////////////////////////////////////////////////////////////////////
// Russian resources

#if !defined(AFX_RESOURCE_DLL) || defined(AFX_TARG_RUS)
#ifdef _WIN32
LANGUAGE LANG_RUSSIAN, SUBLANG_DEFAULT
#pragma code_page(1251)
#endif //_WIN32

#ifdef APSTUDIO_INVOKED
/////////////////////////////////////////////////////////////////////////////
//
// TEXTINCLUDE
//

1 TEXTINCLUDE DISCARDABLE 
BEGIN
    "resource.h\0"
END

2 TEXTINCLUDE DISCARDABLE 
BEGIN
    "#include ""afxres.h""\r\n"
    "\0"
END

3 TEXTINCLUDE DISCARDABLE 
BEGIN
    "#define _AFX_NO_SPLITTER_RESOURCES\r\n"
    "#define _AFX_NO_OLE_RESOURCES\r\n"
    "#define _AFX_NO_TRACKER_RESOURCES\r\n"
    "#define _AFX_NO_PROPERTY_RESOURCES\r\n"
    "\r\n"
    "#if !defined(AFX_RESOURCE_DLL) || defined(AFX_TARG_ENU)\r\n"
    "#ifdef _WIN32\r\n"
    "LANGUAGE 9, 1\r\n"
    "#pragma code_page(1252)\r\n"
    "#endif //_WIN32\r\n"
    "#include ""res\\test.rc2""  // non-Microsoft Visual C++ edited resources\r\n"
    "#include ""afxres.rc""         // Standard components\r\n"
    "#endif\r\n"
    "\0"
END

#endif    // APSTUDIO_INVOKED


/////////////////////////////////////////////////////////////////////////////
//
// Icon
//

// Icon with lowest ID value placed first to ensure application icon
// remains consistent on all systems.
IDR_MAINFRAME           ICON    DISCARDABLE     "test.ico"
#endif    // Russian resources
/////////////////////////////////////////////////////////////////////////////


/////////////////////////////////////////////////////////////////////////////
// English (U.S.) resources

#if !defined(AFX_RESOURCE_DLL) || defined(AFX_TARG_ENU)
#ifdef _WIN32
LANGUAGE LANG_ENGLISH, SUBLANG_ENGLISH_US
#pragma code_page(1252)
#endif //_WIN32

/////////////////////////////////////////////////////////////////////////////
//
// Dialog
//

IDD_TEST_DIALOG DIALOGEX 0, 0, 242, 201
STYLE DS_MODALFRAME | WS_POPUP | WS_VISIBLE | WS_CAPTION | WS_SYSMENU
EXSTYLE WS_EX_APPWINDOW
CAPTION "test"
FONT 8, "MS Sans Serif"
BEGIN
    LTEXT           "Your Hardware ID",IDC_STATIC_HARDWARE,7,7,62,8
    EDITTEXT        IDC_EDIT_HARDWARE,80,7,153,12,ES_AUTOHSCROLL | 
                    ES_READONLY
    LTEXT           "To register application fill out the following fields!",
                    IDC_STATIC,7,24,226,9
    LTEXT           "User Information",IDC_STATIC_USERINFO,7,39,226,8
    EDITTEXT        IDC_EDIT_USERINFO,7,54,226,12,ES_AUTOHSCROLL
    LTEXT           "Registration Key",IDC_STATIC_KEY,7,73,57,8
    EDITTEXT        IDC_EDIT_KEY,7,87,226,36,ES_CENTER | ES_MULTILINE | 
                    WS_VSCROLL
    CTEXT           "UNREGISTERED VERSION",IDC_STATIC_UNREGISTER,7,131,226,8
    PUSHBUTTON      "Buy it now!",IDC_BUTTON_BUY,7,147,63,15
    PUSHBUTTON      "Register",IDC_BUTTON_REGISTER,80,147,70,15
    PUSHBUTTON      "Close",IDC_BUTTON_CLOSE,160,147,73,15
    PUSHBUTTON      "Unregister",IDC_BUTTON_UNREGISTER,7,172,226,15,
                    WS_DISABLED
END


#ifndef _MAC
/////////////////////////////////////////////////////////////////////////////
//
// Version
//

VS_VERSION_INFO VERSIONINFO
 FILEVERSION 1,0,0,1
 PRODUCTVERSION 1,0,0,1
 FILEFLAGSMASK 0x3fL
#ifdef _DEBUG
 FILEFLAGS 0x1L
#else
 FILEFLAGS 0x0L
#endif
 FILEOS 0x4L
 FILETYPE 0x1L
 FILESUBTYPE 0x0L
BEGIN
    BLOCK "StringFileInfo"
    BEGIN
        BLOCK "040904B0"
        BEGIN
            VALUE "CompanyName", "\0"
            VALUE "FileDescription", "test MFC Application\0"
            VALUE "FileVersion", "1, 0, 0, 1\0"
            VALUE "InternalName", "test\0"
            VALUE "LegalCopyright", "Copyright (C) 2007\0"
            VALUE "LegalTrademarks", "\0"
            VALUE "OriginalFilename", "test.EXE\0"
            VALUE "ProductName", "test Application\0"
            VALUE "ProductVersion", "1, 0, 0, 1\0"
        END
    END
    BLOCK "VarFileInfo"
    BEGIN
        VALUE "Translation", 0x409, 1200
    END
END

#endif    // !_MAC


/////////////////////////////////////////////////////////////////////////////
//
// DESIGNINFO
//

#ifdef APSTUDIO_INVOKED
GUIDELINES DESIGNINFO DISCARDABLE 
BEGIN
    IDD_TEST_DIALOG, DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 233
        VERTGUIDE, 70
        VERTGUIDE, 80
        VERTGUIDE, 150
        VERTGUIDE, 160
        TOPMARGIN, 7
        BOTTOMMARGIN, 194
        HORZGUIDE, 19
        HORZGUIDE, 24
        HORZGUIDE, 33
        HORZGUIDE, 39
        HORZGUIDE, 47
        HORZGUIDE, 54
        HORZGUIDE, 66
        HORZGUIDE, 73
        HORZGUIDE, 81
        HORZGUIDE, 87
        HORZGUIDE, 123
        HORZGUIDE, 131
        HORZGUIDE, 139
        HORZGUIDE, 147
        HORZGUIDE, 162
        HORZGUIDE, 172
        HORZGUIDE, 187
    END
END
#endif    // APSTUDIO_INVOKED

#endif    // English (U.S.) resources
/////////////////////////////////////////////////////////////////////////////



#ifndef APSTUDIO_INVOKED
/////////////////////////////////////////////////////////////////////////////
//
// Generated from the TEXTINCLUDE 3 resource.
//
#define _AFX_NO_SPLITTER_RESOURCES
#define _AFX_NO_OLE_RESOURCES
#define _AFX_NO_TRACKER_RESOURCES
#define _AFX_NO_PROPERTY_RESOURCES

#if !defined(AFX_RESOURCE_DLL) || defined(AFX_TARG_ENU)
#ifdef _WIN32
LANGUAGE 9, 1
#pragma code_page(1252)
#endif //_WIN32
#include "test.rc2"  // non-Microsoft Visual C++ edited resources
#include "afxres.rc"         // Standard components
#endif

/////////////////////////////////////////////////////////////////////////////
#endif    // not APSTUDIO_INVOKED

