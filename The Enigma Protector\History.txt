The Enigma Protector v6.70 Build 20200428
- Added Linux 64-bit and Windows 64-bit CGI keys generators
- Internal protection improvements
- Improved protection of Hardware ID
- Improved detection of virtual machines
- Improved compatibility with 3rd applications that are injecting into protected process
- Added few new examples of Enigma API usage
- Added option to open source file folder after protection
- Virtual Box: Improved handling of ZwQueryDirectoryFile
- Virtual Box: Fixed issues accessing virtual files from multiple threads
- Virtual Box: Fixed freezing issue when running multiple child processes at a time

The Enigma Protector v6.60 Build 20190821
- Improved protection of import redirection
- Improved checkup of file name
- Fixed some problems with Enigma API declaration for Visual C++
- Improved Mailer compatibility with qq.com
- Fixed Register Before date in License Manager
- Fixed wrong alignment of relocation directory
- Added support of IDA generated MAP files
- Added redirects support for online activation
- Many minor improvements and fixes
- Virtual Box: Fixed problems using Share to child processes option
- Virtual Box: Fixed problem running multiple child processes
- Virtual Box: Fixed freezing issue on packing x64 net files
- Virtual Box: Fixed issues running virtual x64 exe files
- Virtual Box: Fixed problem with sharing virtual system to child x64 process
- Virtual Box: Fixed issue with opening directory like a file, caused unexpected application behavior
- Virtual Box: Improved directory listing caused exceptions in .net programs
- Virtual Box: Added two new root paths %SYSTEM DRIVE% and %UserProfile FOLDER%

The Enigma Protector v6.50 Build 20190129
- Internal protection improvements
- Added inline patching protection of application code under Protection Features - Inline Patching
- Improved Motherboard hardware lock that works on all versions of Windows now
- Improved VmWare detection
- Added Protection Events feature that allows to run programs after or before protection
- Added search feature in License Manager
- Fixed console version rejected external packages for Virtual Box
- Fixed problem with keyge64.dll
- Environment Variables becomes unicode
- Virtual Box: bug fixed validating external package
- Virtual Box: bug fixed building packages

The Enigma Protector v6.40 Build 20181017
- Added packager builder to console version
- Fixed problem of loading MAP file for specific applications
- Added ability to set command line to running files
- Improved Checkup of Installed Services
- GUI improvements
- Internal protection improvements
- Virtual Box: Added new virtualization method "Never Write to Disk, Use File from Disk (if Exists)"
- Virtual Box: Few improvements for Packages feature
- Virtual Box: Select multiple files or folders to change properties for
- Virtual Box: File and folder attributes and time are being extracted from real items while adding to the tree
- Virtual Box: Fixed issue running virtual exe files with bound import
- Virtual Box: Added manifest support for virtual exe files

The Enigma Protector v6.30 Build 20180704
- Fixed problem with Volume Name hardware lock with unicode volume names
- Improved processing of relocation directory for specific files
- Accept drag and drop for input file
- Virtual Box: Major changes to improve speed of Virtual Box
- Virtual Box: Added drive exists detection for packages
- Virtual Box: Added option to hide virtual files from file open/save dialogs

The Enigma Protector v6.20 Build 20180517
- Fixed small memory leaks for .NET applications
- Fixed issues with RISC virtual machine caused application crash
- Fixed multithreading issue with licensing Enigma API functions
- Fixed crash on exit of a very particular protected dll files
- Added support for protection of Xojo applications
- Improved plugins compatibility
- Virtual Box: Issue fixed executing virtual process

The Enigma Protector v6.10 Build 20180124
- Added virtualization of some new instructions for Classic virtual machine
- Fixed problems virtualizing imul assembler instruction
- Improved handling of switch/case stataments in Virtual Machine
- Bug fixed in calculating API time for Trial - Limitation of execution time

The Enigma Protector v6.00 Build 20171213
- Improved file entry point protection
- Improved protection against hardware id change
- Improved protection against clock reversing
- Added timestamp option for taggant
- Fixed issue with the timestamp for taggant
- Better markers handling for VB6 applications
- Improved Executed Process checkup, new search conditions are added
- Fixed problem with WinAPI emulation for a very specific applications on Windows 10
- Fixed issue with wrong counting of executed copies
- Fixed wrong value shown for %TrialStartMonth% and %TrialEndMonth% variables
- Fixed calling conversion issue for online activation/deactivation Enigma API functions
- Fixed problem protecting Electron type of applications
- Improved relocation directory loading routine
- Improved Installed Services checkup, added unicode support
- Fixed problem protecting some dll files included into Virtual Box
- Fixed gui problems
- Virtual Box: Improved compatibility with Windows 10 Insider
- Virtual Box: Improved compatibility running specific virtual executable files
- Virtual Box: Fixed some problems on Windows 8-10, packed software crashed accessing virtual file
- Virtual Box: Fixed incompatibility with some antivirus softwares

The Enigma Protector v5.90 Build 20170915
- Internal protection improvements
- Improved compatibility of plugins system
- Improved functionality of Import Protection - Import Redirection feature

The Enigma Protector v5.80 Build 20170724
- Internal protection improvements
- Interface improvements
- Examples and manual updates
- Added support for more types of MAP files
- Virtual Box: Fixed issue with registering of some ActiveX components

The Enigma Protector v5.70 Build 20170424
- Added option to check the debugger while application is working (to avoid the debugger to be attached)
- Fixed RISC VM, virtualization of complex nop instruction
- Fixed the protection crash with specific PNG files for splash screen
- Hardware ID vulnerability fixed
- Virtual Box: Fixed issue with overwriting the file if "Write to Disk if not Exist" option used

The Enigma Protector v5.60 Build 20170220
- New interface Virtual Machine - Functions Selecting
- Added support of map files of different compilers MSVC/Delphi/GCC/CBUILDER etc
- Added ability to select virtualization method for different functions
- Added support for transparent png images for splash screen
- Added set of API functions for online activation (for version with online activation only)
- Windows Checkup appended with Windows 8/2012/8.1/2012R2/10/2016
- Fixed minor issue with EP_RegDeleteKey, function returned wrong result
- Fixed random crash at startup if RISC virtualization of entry point is used
- Fixed issue for Win9x

The Enigma Protector v5.50 Build 20160928
- Internal protection improved
- Entry point protection improved
- Hardware ID protection improved
- Minor interface changes and fixes
- Fixed crash of console version for non-English systems
- Fixed Enigma API hooking for VB6 applications
- Virtual Box: Added support of FileNormalizedNameInformation class for ZwQueryInformationFile
- Virtual Box: Fixed duplicated entries in file search (file open/save dialogs)
- Virtual Box: Added compatibility with RestartScan flag for ZwQueryDirectoryFile
- Virtual Box: Error fixed with Package Builder in GUI

The Enigma Protector v5.40 Build 20160629
- Internal protection improvements
- Added ability to save license key to a file
- Fixed some problem with NodeJs applications
- Fixed problem with .net files, file crashed on attempt to read protected assembly
- Fixed stack overflow exception for multithreaded protected applications
- Add fix of debug directory offsets if no file compression and keep debug directory options enabled

The Enigma Protector v5.30 Build 20160505
- Added new interface for Checkup - External Files
- Added ability to select resources to be protected
- Added validation of license file path that caused key invalidation
- Internal protection improvements

The Enigma Protector v5.20 Build 20160203
- Internal protection improvements
- Improved TLS directory handling

The Enigma Protector v5.10 Build 20150624
- Virtual Box: Fixed problem with Windows 8.x

The Enigma Protector v5.00 Build 20150301
- Taggant system is added
- Improved Mailer, templates are now being saved in UTF8 format
- Bug fixed processing OnDeleteKey for plugins system
- Virtual Box: Fixed problem with folder properties
- Virtual Box: Fixed random crashes in interface

The Enigma Protector v4.30 Build 20150225
- Protection core improvements
- Improved Hardware ID detection of HDD Serial
- Bug fixed loading stolen keys from keygen database for command line version
- Bug fixed loading relocation directory
- Minor bugs fixed
- Virtual Box: Fixed problem packaging feature is duplicated files are found
- Virtual Box: Bug fixed, Command Line options were not read in packages
- Virtual Box: Fixed problem with CreateProcess function for x64 applications
- Virtual Box: Fixed issue with ZwQuerySecurityObject
- Virtual Box: Added Vietnamese translation
- Virtual Box: Some problems fixed with the Registry Importing feature

The Enigma Protector v4.20 Build 20141001
- Bug fixed virtualizing entry point for Delphi XE7 files
- Protection improvements
- Improved processes detection of EP_CheckupFindProcess
- Bug fixed calling protector dialogs in Delphi applications
- Bug fixed protecting Delphi dll files with virtual machine
- Improved relocations directory handling
- Bug fixed with EP_RegHardwareID for .NET applications
- Bug fixed processing relocation directory for RISC virtual machine
- Bug fixed loading comment for protected strings

The Enigma Protector v4.10 Build 20140507
- Bug fixed protecting executables with shared sections
- Bug fixed handling invalid import, export and reloc directories
- Some problems fixed with Risc Virtual Machine
- Bug fixed protecting particular Rad Studio files
- Bug fixed with loading virtual content from packaging files of Virtual Box feature
- Bugs fixed in samples and ide files of keys generator for C#
- Bug fixed with memory leaks in loader
- Bug fixed with option Checkup - Control sum
- Bug fixed protecting dll files

The Enigma Protector v4.00 Build 20140304
- Added new RISC virtual machine
- Added new vm markers vm_risc_begin and vm_risc_end
- Improved virtualization for instructions ror, rol, inc, dec
- Bug fixed virtualizing instructions sar, sal, shr, shl, adc, sbb, scas
- Improved handling of trial Enigma API in registered mode
- Improved trial control while application is running
- Improved compatibility with madExcept package
- Improved handling of files with sections of zero virtual size
- Bug fixed with changing of HDD Serial (Compatibility Level 4 should be enabled in Miscellaneous - Others)
- Bug fixed processing relocation elements in VM markers
- Bug fixed using VM markers for Visual Basic programs
- Bug fixed detecting Windows version for Windows 8+
- Bug fixed with VC/VC64 keygen examples
- Bug fixed with declaration of TKeyInformation for Visual Basic

The Enigma Protector v3.150 Build 20131005
- Added option to enable or suppress check of packages integrity for Virtual Box
- Bug fixed - crash of console version
- Many minor improvements and bugs fixed
- Virtual Box: Bug fixed loading virtual dll files under Windows 8 x64
- Virtual Box: Bug fixed running some virtual exe files on Windows 8
- Virtual Box: Bug fixed processing particular files
- Virtual Box: Bug fixed running multiple instances of virtual processes

The Enigma Protector v3.140 Build 20130522
- Improved internal protection core
- Improved obfuscation of virtual machine code
- Bug fixed with entry point virtualization for particular files
- Fixed problems with multiple options if Virtual Box feature includes main file
- Bug fixed with keygen.dll - crash on attempt to generate a key
- Virtual Box: Improved packing of big files
- Virtual Box: Improved handling of manifest files
- Virtual Box: Improved virtualization of dll files
- Virtual Box: Improved handling of asynchronous read-write operations under virtual files
- Virtual Box: Bug fixed processing particular files

The Enigma Protector v3.130 Build 20130205
- Internal protection improvements
- Improved control sum protection
- Critical bug fixed with Virtual Box - Packaging feature
- Virtual Box: Critical improvements of handling of file read-write operations
- Virtual Box: Bug fixed running one packed exe from another
- Virtual Box: GUI improved for working with relative file names
- Virtual Box: Improved handing of ZwSetInformationFile function
- Virtual Box: Bug fixed writing to virtual files
- Virtual Box: Minor problems fixed with console version
- Virtual Box: Improved files virtualization core
- Virtual Box: Added ability to setup and install ActiveX components
- Virtual Box: Improved ActiveX/COM registration process
- Virtual Box: Added ability to make packages through command line version
- Virtual Box: Fixed compatibility issue with Windows 2000 for some files

The Enigma Protector v3.120 Build 20121130
- Improved compatibility of using Windows Serial for Hardware Lock
- Bug fixed protecting .NET applications with Control Sum checkup
- Minor GUI improvements

The Enigma Protector v3.110 Build 20121113
- Improved internal protection
- Improved Import Emulation
- Improved protection of Enigma API
- Rare bug fixed with processing of resource directory
- Bug fixed protecting particular files with Virtual Box feature
- Virtual Box: Improved compatibility with different versions of Windows and softwares
- Virtual Box: Bug fixed working 64 bit files under Windows 8 x64
- Virtual Box: Bug fixed extracting files to the disk from external package
- Virtual Box: Minor improvements and fixes

The Enigma Protector v3.100 Build 20121010
- Improved handling of virtual machine
- Bug fixed importing settings from external project 

The Enigma Protector v3.90 Build 20121004
- Improved internal protection
- Added Enigma API function EP_RegLoadKeyEx instead of deprecated EP_RegLoadKey
- Added feature Checkup - File Integrity Checking to check if the protected file is modified
- Added new plugin functions Enigma_Plugin_OnInitEx and Enigma_Plugin_OnFinalEx
- Added unicode support for Checkup - Executed Processes
- Improved processing of big files
- Improved option Miscalleneous - Other - .NET Smart Assembly Hooking for particular .NET files
- Improved Mailer to support unicode in emails
- Bug fixed processing SEH handlers for code virtualization in Visual Basic applications
- Bug fixed with Linux cgi keys generator working with unicode registration names
- Bug fixed showing registration dialog for NET applications through Enigma API

The Enigma Protector v3.80 Build 20120802
- Improved internal protection
- Improved working with reloc directory
- Bug fixed using additional parameters in license manager
- Bug fixed importing project settings from external file
- Bug fixed protecting complex .net applications
- Bug fixed with Virtual Machine - Entry Point for Visual Basic programs
- Bug fixed with Enigma SDK file for VB.NET
- Bug fixed emulating command line parameters
- Bug fixed processing Enigma_Plugin_OnLoadKey
- Bug fixed processing Enigma_Plugin_OnSaveKey
- Bug fixed with internal protection
- Bug fixed dynamically calling Enigma API
- Virtual Box: Bug fixed handling of ZwDeleteValueKey function caused application freezing
- Virtual Box: Bug fixed editing file virtual path in File Properties dialog
- Virtual Box: Bug fixed activating context
- Virtual Box: Added ability to create activation context for manifests
- Virtual Box: Bug fixed loading virtual registry values
- Virtual Box: Improved handling of ZwMapViewOfSection function
- Virtual Box: Added ability to write to virtual files
- Virtual Box: Added ability to load package from an external folder
- Virtual Box: Added ability to enter path variables as a value of registry items
- Virtual Box: Improved stability with multi-threaded applications
- Virtual Box: Improved handling of ZwMapViewOfSection function
- Virtual Box: Improved access handling for files operations
- Virtual Box: Improved ZwSetInformationFile function for working with files deleting
- Virtual Box: Bug fixed saving virtual registry settings
- Virtual Box: Bug fixed mapping particular files

The Enigma Protector v3.70 Build 20120504
- Improved internal protection
- Added support of png images in Registration Dialog
- Added Enigma SDK for VB .NET
- Improved compatibility with the protected plugins
- Improved relocation directory handling
- Improved virtualization engine for Delphi and C executables
- Improved saving license to file under Windows 9x
- Improved registration dialog feature for Windows 9x
- Bug fixed assigning jpeg images in Registration Dialog
- Bug fixed with Entry Point virtualization for Visual Basic applications
- Bug fixed processing stolen keys
- Virtual Box: Bug fixed launching packed application from a network device
- Virtual Box: Improved working with big files
- Virtual Box: Improved detection of virtual registry keys
- Virtual Box: Bug fixed deleting extracted files with hidden attribute

The Enigma Protector v3.60 Build ********
- Added Import Project feature to import settings from external project file
- Improved Exported Keys generator
- Improved Hardware ID Checker
- Improved compatibility with the multi-processors computers
- Allowed execution of virtual executable files for Virtual Box feature
- Improved User Name hardware lock, fixed it's changing when switching Windows account
- Improved HDD Serial hardware lock, fixed it's changing when switching Windows account
- Bug fixed virtualizing particular applications
- Virtual Box: Improved virtualization of .net files
- Virtual Box: Improved loaders handling that allows to decrease size of virtualized output file
- Virtual Box: Improved case sensitive proccessing for files and registry
- Virtual Box: Improved files and registry virtualization
- Virtual Box: Bug fixed deleting extracted files on exit
- Virtual Box: Bug fixed storing of overwritten attributes for virtual files
- Virtual Box: Bug fixed querying objects security information (caused crash on Windows 8)
- Virtual Box: Problem fixed handing ZwDeviceIoControlFile function
- Virtual Box: Added option Allow runing of virtual exe files

The Enigma Protector v3.50 Build ********
- Added Required Process feature for Executed Processes checkup
- Improved anti-patch protection
- Improved internal protection
- Bug fixed with Registration Dialog, error message had been shown on form key press

The Enigma Protector v3.40 Build ********
- Improved plugins protection
- Bug fixed, protecting dll files with Import Emulation feature
- Bug fixed with interface, disappearing of controls when Alt key is pressed
- Virtual Box: Added root folder for files virtualization %Local, ApplicationData FOLDER%
- Virtual Box: Added two more root folders for files virtualization %Temp FOLDER% and %AllUsers, ApplicationData FOLDER%
- Virtual Box: Improved detection of virtual files in very particular applications
- Virtual Box: Improved carrying of original file name for virtual executable
- Virtual Box: Bug fixed with registry virtualization, function ZwQueryKeyValue
- Virtual Box: Improved speed of files virtualization
- Virtual Box: Improved speed of registry virtualization
- Virtual Box: Bug fixed enumerating registry keys
- Virtual Box: Added handing of ZwDeviceIoControlFile function
- Virtual Box: Improved handling of ZwFsControlFile function
- Virtual Box: Bug fixed opening device handles

The Enigma Protector v3.30 Build 20111118
- Improved handling of markers in Visual Basic applications
- Improved handing of ZwUnmapViewOfSection for Virtual Box
- Improved memory saving for Virtual Box
- Minor interface improvements
- Bug fixed with Virtual Machine - processing number overflow operations
- Bug fixed protecting very particular files with long import function names
- Bug fixed loading MAP files for Virtual Machine
- Bug fixed with memory leak in files and registry virtualization for Virtual Box

The Enigma Protector v3.20 Build 20111027
- Bug fixed with External Processes checkup which caused crash

The Enigma Protector v3.10 Build 20111007
- Bug fixed with option Time Control - Use Protection Date
- Added option Virtual Box - Options - Map Executable Files Using Temporary File for better compatibility with the virtual executable files
- Improved handling few more Windows API functions for Virtual Box
- Improved folders deleting for extracted files for Virtual Box
- Improved watermarks feature to support unicode
- Bug fixed with registry virtualization for Virtual Box
- Bug fixed running boxed file in a root drive folder
- Many minor bugs fixed

The Enigma Protector v3.00 Build 20110912
- Added completely new Virtual Box feature
- Improved Sandboxie detection for Virtualization Tools check
- Improved protection for Virtualization Tools check
- Many minor improvements and bugs fixed

The Enigma Protector v2.70 Build 20110629
- Added logging for Keys Generator
- Added few callback functions for plugins
- Added option Miscellaneous - Other - Supress graphical module
- Added option Miscellaneous - Other - Do not check if file is compressed
- Added option Miscellaneous - Other - Do not search markers
- Improved Virtual Machine - File Entry Point protection of particular Delphi programs
- Bug fixed showing unicode AppName and AppVer variables for message and dialog designers
- Bug fixed overloading project settings on re-opening project file
- Bug fixed handing unmanaged exceptions for .net applications
- Minor bugs fixed loading new project
- Minor GUI improvements

The Enigma Protector v2.60 Build 20110523
- Added option to automatically get product information from file resources
- Improved displaying unicode strings in the Registration Dialog
- Improved getting actual Product Name and Version in the Input panel
- Improved detection files for Virtual Box
- Improved gui, new project is being generated each start
- Improved reading extended trial information
- Improved deleting local debug information
- GUI improvement, last project is opening at Enigma Protector start
- Bug fixed reseting local trial information for particular projects
- Bug fixed emulating LockFile/UnlockFile and LockFileEx/UnlockFileEx functions
- Bug fixed getting hardware id in a very particular PC
- Bug fixed protecting very particular files
- Bug fixed protecting .net executables
- Bug fixed allocating stack for Virtual Machine
- Bug fixed loading form from a file in the Registration Dialog designer

The Enigma Protector v2.50 Build 20110330
- Added EP_RegShowDialog Enigma API for showing of the registration dialog
- Added EP_CheckupVirtualizationTools Enigma API for manual checking of Virtualization Tools
- Added Enigma_Plugin_OnSaveKey and Enigma_Plugin_OnLoadKey callbacks to Plugins SDK
- Improved Miscellaneous - VERSION Resource, added support of unicode
- Improved Check up of File Name, added support of unicode file name 
- Minor GUI improvements
- Some bugs fixed that appeared on Windows 9x
- Bug fixed with CGI keys generator, GenerateKeyFromProjectW action caused errors
- Bug fixed protecting some files with incorrect section sizes
- Bug fixed loading unicode information from project file
- Bug fixed detecting debugger under compatibility mode
- Many minor bugs fixed

The Enigma Protector v2.33 Build 20110112
- Added option Use protection date to check users clock for clock reversing
- Added option Do not check clock reversing if registered for clock reversing 
- Added EP_RegKeyStatus Enigma API
- Added EP_RegKeyInformation Enigma API
- Added detection of hidden processes by Executed Process checkup
- Added options Encrypt Registration Information and Encrypt with Hardware ID under Regitsration Features - Registration data storing
- Minor GUI improvements
- Improved unicode support
- Improved Registration Data Storing - Both Registry and File
- Improved countries list for Keys Generator, Lock Trial to User Language and User Language check
- Bug fixed setting property Position for the form in Registration Dialog Designer
- Bug fixed verifying particular registration keys in Keys Generator
- Bug fixed deleting and re-registering application that uses file for registration key storing with readonly attributes

The Enigma Protector v2.21 Build 20101111
- Minor GUI improvements

The Enigma Protector v2.20 Build 20101105
- Added better GUI unicode support
- Bug fixed verifying ansi registration keys

The Enigma Protector v2.14 Build 20101025
- Added additional unicode support for some features
- Many minor GUI improvements
- Bug fixed porotecting files with uncompressable data (files with a low entropy)
- Bug fixed with SSL option in Mailer

The Enigma Protector v2.13 Build 20100923
- Added Windows User Name hardware lock
- Added Files Entry Point virtualization
- Added quite modes for console versions
- Added keygen64.dll for x64 keys generators
- Improved hooking of Windows API 
- Improved compression routine
- Minor GUI improvements
- Improved virtualization of some exception handlers of Delphi applications
- Improved keys verification routine regarding processing of illegal symbols in the key
- Bug fixed processing particular VM markers

The Enigma Protector v2.12 Build 20100903
- Improved Executed Processes checkup
- Improved compression of Virtual Box files

The Enigma Protector v2.11 Build 20100825
- Improved Simulation for Virtual Machine
- Minor improvements
- Bug fixed extracting compressed files for Virtual Box

The Enigma Protector v2.10 Build 20100824
- Improved Virtual Machine
- Interface improvements
- Bug fixed flowing of Registration Dialog on Windows 7 with 125% display font

The Enigma Protector v2.05 Build 20100727
- Added Enigma API EP_CheckupFindProcess (ansi and unicode versions) for searching executed processes
- Improved protection and logic of Virtual Box
- Minor bugs fixed

The Enigma Protector v2.04 Build 20100629
- Improved Protected Strings feature
- Bug fixed processing OnClose action of standard Registration Dialog

The Enigma Protector v2.03 Build 20100623
- Improved internal protection
- Bug fixed processing some VM markers

The Enigma Protector v2.00 Build 20100607
- Added new Registration Dialog Designer
- Added Virtualization VM Markers
- Added unicode support for Registration Dialog controls
- Improved HDD Serial hardware lock
- Improved Computer Name hardware lock
- Changed way loading Virtual Machine settings from project file
- Minor GUI improvements
- Bug fixed with Windows Serial Key hardware lock, it failed under Windows Seven and Vista
- Bug fixed displaying crypted sections when keys is verifying in Keys Generator and License Manager

The Enigma Protector v1.96 Build 20100420
- Added Compress Files option for Virtual Box
- Minor GUI improvements
- Bug fixed processing of empty files for Virtual Box
- Bug fixed in CGI keys generators, hyphens parameters had been missed
- Minor bug fixes in CGI keys generators
- Bug fixed in Virtual Machine, functions processing if input file does not exist
- Bug fixed incorrect disassembled text in Virtual Machine
- Bug fixed displaying of selected functions in Virtual Machine after project loading
- Bug fixed with Virtual Box - Compress Files for particular already compressed files
- Bug fixed applying option Encrypt with EncryptionConstant even if option Allow Execution Only if Registered is disabled

The Enigma Protector v1.95 Build 20100409
- Added Taiwan for User Language checkup and for Lock Trial to User Language
- Added option Do not create backup files on Output panel
- Added possibility to copy protection log to clipboard or save to file
- Bug fixed with Virtual Box, failing of initializing of particular embeded libraries
- Bug fixed with Virtual Box, incorrect processing of the last file from the list
- Bug fixed with Virtual Box, crash of file dialogs under Windows XP
- Bug fixed with Same as Input File feature from Output panel
- Bug fixed emulating of GetProcAddress function
- Bug fixed emulating of VirtualQuery function
- Bug fixed protecting files without resource directory

The Enigma Protector v1.94 Build 20100329
- Added full unicode support for Virtual Box
- Added full thread safe for Virtual Box
- Rewritten GUI and core of Virtual Box
- Bug fixed with crashing of Save and Open dialogs if Virtual Box enabled
- Many minor improvements and bug fixes of Virtual Box

The Enigma Protector v1.93 Build 20100308
- Added Enigma API EP_SplashScreenShow and EP_SplashScreenHide for manual showing and hiding of splash screen
- Added option Show at Program Start for Splash Screen
- Improved internal protection of loader data
- Optimized internal protection routines
- Optimized Virtual Machine, it runs faster now
- Bug fixed with Virtual Box, crash on very particular PC
- Bug fixed with EP_TrialDays, returned incorrect number of days

The Enigma Protector v1.92 Build 20100224
- Added possibility to define own paths for trial storing
- Added full unicode support for Virtual Box
- Improved clock reversing protection
- Improved trial protection
- Improved handling of Windows, System and current folders under Windows 9x
- Changed signature of Microsoft Visual C++ for option File Analyzer Deception - Hide Protection Code With
- Optimized WinAPI emulation feature
- Bug fixed with Clock Reversing
- Bug fixed with processing of some registration keys
- Bug fixed, added EP_EnigmaVersion function to enigma_ide.dll
- Bug fixed with displaying of non english text in console version
- Bug fixed with Visual Basic applications if C++ signature is using for Hide Protection Code With
- Bug fixed with processing of registration keys caused application crash at start up
- Bug fixed processing of hardware locked registration keys with option Allow Hardware Changes
- Bug fixed with processing of trial days limitation
- Bug fixed with reseting of application language after reinstalling
- Bug fixed with Virtual Box, crash of application after file call with Zone.Identifier appendix
- Bug fixed with Virtual Box, processing of some unicode paths

The Enigma Protector v1.91 Build 20100209
- Added Memo (multi-line Edit) control for Registration Dialog designer
- Improved handling of markers in Visual Basic applications
- Bug fixed with saving of the ansi registration information into file
- Bug fixed with License Manager, hardware id was not saved in ansi mode
- Bug fixed with verifying of the hardware locked registration key 
- Bug fixed with generating of invalid registration keys if long hardware id is used

The Enigma Protector v1.90 Build 20100127
- Added Use SSL connection feature in Mailer to allow emails send from servers that require secure connection (like gmail.com)
- Added GenerateKeyW action for CGI keys generator for generating unicode registration key
- Added Windows 7 and Windows 2008 R2 for Windows Version check up
- Added C#, Delphi 2009 up, Visual C++ examples of custom unicode keys genereator
- Added unicode Keygen API in keygen.dll and libkeygen.so KG_GenerateRegistrationKeyW, KG_GenerateRegistrationKeyFromProjectW, KG_VerifyRegistrationInfoW, KG_VerifyRegistrationInfoFromProjectW
- Added additional ansi string Keygen API in keygen.dll KG_GenerateRegistrationKeyA, KG_GenerateRegistrationKeyFromProjectA, KG_VerifyRegistrationInfoA, KG_VerifyRegistrationInfoFromProjectA
- Added C#, Delphi 2009 up, Visual C++ examples of unicode registration
- Added unicode support for Mailer
- Added unicode support for Dialog Messages
- Added unicode support for License Manager
- Added unicode support for Keys Generator and exported Keys Generator
- Added unicode Enigma API EP_RegSaveKeyW, EP_RegLoadKeyW, EP_RegHardwareIDW, EP_RegCheckKeyW, EP_RegCheckAndSaveKeyW
- Added ansi duplicates of registration Enigma API EP_RegSaveKeyA, EP_RegLoadKeyA, EP_RegHardwareIDA, EP_RegCheckKeyA, EP_RegCheckAndSaveKeyA
- Added option UNICODE Registration Scheme that determinate what registration, Ansi or Unicode, will be used for whole project
- Added Delphi, VC, VB, Bcb, C# examples of application with runtime locked registration keys
- Added Plugin SDK for Borland C Builder
- Added options Allows only keys with Register After date, keys with Register Before date, executions limited keys, days limited keys, run-time limited keys, global time limited keys, country locked keys under REGISTRATION FEATURES - Key Properties panel
- Added Enigma API EP_RegKeyRegisterBeforeDate, EP_RegKeyRegisterBeforeDateEx that return Register Before date of the current registration key
- Added Enigma API EP_RegKeyRegisterAfterDate, EP_RegKeyRegisterAfterDateEx that return Register After date of the current registration key
- Added Enigma API EP_RegKeyGlobalTime, EP_RegKeyGlobalTimeTotal, EP_RegKeyGlobalTimeLeft that return number of left and total global time minutes that current registration key will be valid
- Added Enigma API EP_RegKeyRuntime, EP_RegKeyRuntimeTotal, EP_RegKeyRuntimeLeft that return number of left and total runtime minutes that current registration key will be valid
- Added Enigma API EP_RegKeyDays, EP_RegKeyDaysTotal, EP_RegKeyDaysLeft that return number of left and total days that current registration key will be valid
- Added Enigma API EP_RegKeyExecutions, EP_RegKeyExecutionsTotal, EP_RegKeyExecutionsLeft that return number of left and total executions that current registration key will be valid
- Added Enigma API EP_MiscCountryCode that return the code of the user country
- Added Enigma API EP_RegKeyCreationDateEx to extent EP_RegKeyCreationDate
- Added Enigma API EP_RegKeyExpirationDateEx to extent EP_RegKeyExpirationDate
- Added Enigma API EP_CheckupCopiesTotal and EP_CheckupCopiesCurrent to extend EP_CheckupCopies
- Added Enigma API EP_TrialExecutionTimeTotal and EP_TrialExecutionTimeLeft to extend EP_TrialExecutionTime
- Added Enigma API EP_TrialDateTillDateStartEx and EP_TrialDateTillDateEndEx to extend EP_TrialDateTillDate
- Added Enigma API EP_TrialExpirationDateEx to extent EP_TrialExpirationDate
- Added Enigma API EP_TrialDaysTotal and EP_TrialDaysLeft to extent EP_TrialDays
- Added Enigma API EP_TrialExecutionsTotal and EP_TrialExecutionsLeft to extent EP_TrialExecutions
- Added box in REGISTRATION FEATURES - Common panel that shows Encryption Constant, Public and Private Keys of current project for custom keys generator
- Added new Debug item - Reset all local information to delete trial, registration and special information for debuging purposes
- Added possibility to lock registration key to number of minutes of global-time execution (sum of all executions)
- Added possibility to lock registration key to number of minutes of run-time execution
- Added possibility to lock registration key to number of license usage days
- Added possibility to lock registration key to number executions
- Added possibility to set up Register After and Register Before dates for registration keys
- Added possibility to lock registration key to particular country
- Improved protection core, added unicode support
- Improved examples of custom keys generators
- Improved CGI keys generators, eliminated case sensitive of http parameters
- Improved error returning of CGI keys generators, errors return as a string, but not as an error code variable name
- Improved Enigma API EP_RegKeyExpirationDate, it returned expiration date of even expired registration keys
- Updated VC example of custom registration keys generator
- Updated keygen.dll, TKeyGenParams and TKeyVerifyParams structures
- Updated Export Key Generator and it's wizard according with new changes
- Updated Keys Generator and License Manager according with new changes
- Bug fixed with Virtual Box, incorrent handling of very rare file calls
- Bug fixed with Allow Hardware changes that works for each key now
- Bug fixed of registration information loading under Windows 95
- Bug fixed of loader compression, very rare bug
- Bug fixed in CGI generators, incorrect processing of unicode registration information
- Bug fixed with Mailer, exception occured when deleted last email from the list
- Bug fixed with Enigma API EP_RegKeyCreationDate, it always returned true

The Enigma Protector v1.81 Build 20091203
- Bug fixed with Virtual Box and WinAPI Emulation

The Enigma Protector v1.80 Build 20091130
- Added global variables for Relative Path value in Virtual Box (like %SYSTEM FOLDER%, %WINDOWS FOLDER% etc)
- Added Advance Input panel for multiple files protection
- Added random virtualization of Virtual Machine functions
- Added random loader virtualization for each protected file, for registered version only
- Added feature that allows to lock change by user of hyphens, sections and key expiration date values in exported keys generator
- Added File - History menu for quick access to previously opened projects
- Added Allow Hardware Changes feature
- Some GUI improvements
- Bugs fixed with examples that use registration Enigma API for Delphi 2009
- Bug fixed with Virtual Box under Windows 7 that does not allow to run save file dialog
- Bug fixed with keygen_ide.pas and keygen example for Delphi 2009
- Bug fixed with protecting of .NET executables

The Enigma Protector v1.79 Build 20091017
- Added CGI keygen example with POST method
- Improved protecting of files with relocation directory for Windows Vista/7
- Improved TLS directory handing for files working under Windows Vista/7
- Improved SDK and manual
- Bug fixed of registration information loading
- Bug fixed with protecting of Windows Vista/7 system executables
- Bug fixed with processing of global variables in dialog messages

The Enigma Protector v1.78 Build 20091007
- Added possibility to assign paste and copy buttons to edit boxes in Registration Dialog Designer
- Added possibility to set license file attributes
- Added Enigma API EP_CryptEncryptBuffer, EP_CryptEncryptBufferEx, EP_CryptDecryptBuffer, EP_CryptDecryptBufferEx for buffer encryption-decryption
- Added Enigma API EP_EnigmaVersion to return a version of Enigma Protector
- Added possibility to change text font for dialog messages
- Improved runtime trial expiration checkup
- Bug fixed with exceptions handling for particular files

The Enigma Protector v1.77 Build 20090901
- Added RunOnce markers
- Added possibility to setup own environment variables for protected file
- Added override command line feature under Miscellaneous group
- Improved GUI, added possibility to resize main Enigma window
- Minor protection core improvements
- Bug fixed with import initialization of very particular Virtual Boxed dll files
- Bugs fixed with GUI of some Enigma windows under Windows Vista
- Bug fixed with Advanced Force Import Protection for very particular files
- Bug fixed with Virtual Box, caused crashing of very particular files

The Enigma Protector v1.76 Build 20090818
- Added Check Executed Processes every X seconds feature
- Added Enigma_Plugin_OnFinal plugin function that is calling before main program is executed
- Added Delphi plugins SDK, look at EnigmaPluginsSDK folder
- Added possibility to use additional parameters of License Manager in Mailer
- Added possibility to set up unlimited number of own additional parameters for customers and licenses in License Manager
- Bug fixed with Decrypt On Execute marker, marker caused freezing on second execution 
- Bug fixed with Virtual Machine, not enough stack exception
- Minor bugs fixed

The Enigma Protector v1.75 Build 20090801
- Bug fixed with dll protecting

The Enigma Protector v1.74 Build 20090727
- Improved .NET executables protection
- Optimized loader, speeded up execution time
- Bug fixed with Enigma Mailer, AutoClose exception
- Bug fixed with internal GetProcAddress function
- Bug fixed with crashing of protected .NET under Windows Vista/2008
- Bug fixed with showing of loader messages under Windows 9x
- Bug fixed with Registration Dialog

The Enigma Protector v1.73 Build 20090722
- Added support ActiveX components for Virtual Box
- Added support of _lread, _hread, _llseek Windows API for Virtual Box
- Added support of NT core functions for Virtual Box
- Improved emulation of CreateFile functions for Virtual Box
- Bug fixed with MapViewOfFile function for Virtual Box

The Enigma Protector v1.72 Build 20090715
- Improved import redirection feature for dll files
- Improved logic of dll files protection
- Bug fixed with dll files protection, dll crashed on exit

The Enigma Protector v1.71 Build 20090706
- Files Attachment is moved to Virtual Box section
- Improved logic of Executed Processes checkup
- Bug fixed with overlay files

The Enigma Protector v1.70 Build 20090703
- Improved protection core
- Improved Virtual Machine GUI
- Bug fixed with Trial Control - Clock Reversing
- Bug fixed with loading of relocation table in very particular applications
- Bug fixed with Virtual Machine occured on changing of input file
- Bug fixed with particular protected applications running under Windows 9x emulation
- Bug fixed with Watermarks and Watermark Viewer
- Bug fixed with FindFirstFile functions

The Enigma Protector v1.68 Build 20090701
- Added %RegKey%, %RegName% variables to Message Designer to show registration name and key
- Added %KeyExpYear%, %KeyExpMonth%, %KeyExpDay% variables to Message Designer to show key expiration date
- Bug fixed with Executed Process checkup when some window text is empty string
- Bug fixed with Enigma Mailer when open it first time
- Bug fixed with most plugins for Windows 7
- Bug fixed with Inline Patching protection for Windows 7
- Bug fixed with enigma_ide.pas for Delphi 2009

The Enigma Protector v1.67 Build 20090601
- Added possibility to check Executed Process window text by text mask
- Added support of Markers for Visual Basic applications
- Added emulating FindFirstFile/FindNextFile functions for Files Attachment
- Some GUI improvements
- Bug fixed with Files Attachment feature, emulating of FindFirstFile functions

The Enigma Protector v1.66 Build 20090504
- Added support of SEH handlers and exceptions inside Virtual Machine for C++ and C compiled applications
- Added support of SEH handlers and exceptions inside Virtual Machine for Visual Basic compiled applications
- Added support of SEH handlers and exceptions inside Virtual Machine for Delphi compiled applications
- Added Check Hardware ID button for Keys Generator and License Manager
- Added button Generate Email in License Manager for fast generating and sending emails
- Added Edit Email feature for Mailer, it allows to edit generated emails
- Added CC and BCC fields for Mailer
- Added Expire Subscription feature for Mailer
- Improved GUI of Mailer, allow to resize the window
- Bug fixed with Custom VERSION Resource with very particular files
- Bug fixed with Custom MANIFEST Resource with very particular files
- Bug fixed with a GUI of Mailer under Windows Vista
- Bug fixed with Executed Processes Checkup
- Bug fixed with Windows API emulating, LoadLibrary functions
- Bug fixed occured on Windows 9x under virtual machines
- Bug fixed with Files Attachment and Plugins features, added hook of VirtualQuery functions
- Bug fixed with markers loading
- Bug fixed with Virtual Machine for multi-threaded applications
- Bug fixed with opening of project file if there is setup any not existing Attached File
- Bug fixed with protecting of dll files, exception on dll unload

The Enigma Protector v1.65 Build 20090404
- Added CGI keygen for Linux servers
- Added CGI keygen for Windows servers
- Added Check_Protection marker that allows to check integrity of protection code
- Added Encrypt application with particular Hardware ID feature
- Added Markers Analyses panel as a help tool to view markers defined in the file
- Added Unprotected markers, to mark the parts of code that have to be executed only in unprotected file
- Added Enigma API function EP_CheckupIsEnigmaOk to check integrity of Enigma protection code
- Added Enigma API function EP_CheckupIsProtected to provide quick checkup, is module protected or no
- Added Enigma API function EP_CheckupCopies that returns total and current number of executed copies of application
- Added Terminate Execution option for Checkup - Executed Copies
- Added "Disable OK button for seconds" option for Message dialogs
- Added "Automatically close this message after seconds" option for Message dialogs
- Added support of .NET DLL files for Files Attachment feature with Never Write to Disk option
- Added support of CreateFileMapping function with SEC_IMAGE flag for Files Attachment feature with Never Write to Disk option
- Changed signature of Reg_Crypt markers
- Changed signature of Unprotected markers
- Improved Encrypt application with Encryption Constant feature, see manual for details
- Improved Virtual Machine logic
- Improved Files Attachment feature, more support for GetFileAttributesEx functions
- Improved Files Attachment feature for DLL files with Never Write to Disk option, added support exceptions inside DLL under Windows 2003/Vista/2008
- Bug fixed with Files Attachment feature, incorrect checking of embeded folders 
- Bug fixed with Files Attachment feature when dll is attached with option "Never write to disk" and some export functions of this dll are in ordinal mode
- Bug fixed with protecting of some kind of DLL files, DLL crashed in detach event
- Bug fixed with detection of markers in very particular Delphi 2009 and C++ AciveX applications
- Bug fixed with Import Redirection under Windows 7 RC1

The Enigma Protector v1.64 Build 20090303
- Added Inline Patching Protection
- Added possibility to add custom VERSION resource into protected file
- Added possibility to add custom MANIFEST resource into protected file
- Added preview button for message dialogs
- Added possibility to set up icon for message dialogs
- Added exceptions support for Virtual Machine (it does not support reading of SEH yet)
- Improved Files Attachment feature (added support of few new API)
- Improved Import Emulation feature
- Improved logic of Disk Drive Checkup
- Optimized reading functions from C++ compiled map files for Virtual Machine, long function names convert to short
- Bug fixed with a GUI under Windows Vista
- Bug fixed that occured when reading VS compiled map files for Virtual Machine
- Bug fixed with Enigma GUI: main window of Enigma moved to back of other windows after association question
- Bug fixed with protecting of .NET executables, the bug caused "Unable to find a version of the runtime to run this application" exception on Windows Vista and higher
- Bug fixed when reading C++ compiled map files for Virtual Machine, bug occured due to exceeding length of function name
- Fixed huge bug that occured if the protected file is executed from CMD shell
- Bug fixed with Disk Drive Checkup

The Enigma Protector v1.63 Build 20090202
- Added few more base folders for storing of registration information
- Added VC SDK and example for custom keys generator
- Improved main window of License Manager
- Improved Customer Edit window of License Manager
- Improved Mailer
- Improved GUI of Protected Strings panel
- Improved GUI of Files Attachment panel
- Improved GUI of External Files checkup panel
- Improved GUI of Executed Processes checkup panel
- Improved GUI of Loaded Drivers checkup panel
- Improved GUI of Installed Services checkup panel
- Bug fixed with Startup Password
- Bug fixed in the following plugins: closehandle.dll, emulatorsdetect.dll, hardwarebreakpoints.dll, int3.dll, sandboxiedetect.dll, writeprocessmemoryinject.dll
- Bug fixed when editing a file for Files Attachment
- Bug fixed when editing a file for External File
- Bug fixed when editing a process for Executed Processes

The Enigma Protector v1.62 Build 20090115
- Added new Virtual Machine
- Added support of Virtual Machine for dll files
- Added possibility to mark expired keys as stolen in License Manager
- Added option "Encrypt Application" with encryption constant for "Allow execution only if registered"
- Added possibility to set up default values for License Manager's Key Generator
- Added crypt API (EP_CryptHashBuffer, EP_CryptHashFileA, EP_CryptHashFileW, EP_CryptHashStringA, EP_CryptHashStringW) for providing hash of buffer/string/file, supported hashes: Xor32, MD2, MD5, RipeMD160, SH1, SHA224, SHA256, SHA384, SHA512
- Added anti-OllyDbg tricks, licensed version only
- Improved HDD Serial hardware lock
- Optimized read hardware routine
- Changed signature of Decrypt_On_Execute marker, please take a look at SDK
- Bug fixed with Decrypt_On_Execute markers in multi-threaded applications
- Bug fixed in protecting dlls with shared sections
- Bug fixed in dll protection, exception occured with some protected dlls after dll had been detached
- Bug fixed in Hardware ID Checker - simple typo

The Enigma Protector v1.61 Build 20081222
- Bug solved when loading project - Hardware Lock "System Volume Serial Number" always was enabled
- Bug solved with registration of Enigma itself

The Enigma Protector v1.60 Build 20081215
- Added Hardware ID checker, it allows to check content of hardware id strings
- Added License Manager instead of Registration Manager
- Added database importing in License Manager
- Added possibility to export keys generator for particular project into single execution file
- Added different output bases of registration keys, Base 2/8/16/32/64
- Added different types of safety/length of registration keys, safety/length is like RSA 512/768/1024/2048/3072/4096 bits
- Added Emulators Detect plugin
- Changed registration key algorithm from RSA to ECC
- Changed keygen.dll and it's functions
- Improved projects' examples
- Improved thread safety of main cipher algorithm
- Improved loading of functions for Virtual Machine
- Improved registration keys checking routine, it is much faster now
- Improved registration keys safety, maximum registration key safety is like RSA 4096 bits
- Improved project file, older created project files will not be supported anymore
- Improved registration scheme, older created registration keys will not be supported anymore
- Improved project file loading routine
- Improved interface and logic of License Manager
- Bug fixed in protection of Visual Studio 8.0 ActiveX dlls
- Bug fixed in protection of very particular .NET executables

The Enigma Protector v1.55 Build 20081023
- Bug fixed in Files Attachment when embeded a dll with "Never write to disk" option and it is calling without file extension
- Bug fixed in Files Attachment: added emulation of GetModuleFileName function
- Bug fixed in Files Attachment, bug occured when embed dll with "Never write to disk" option and when is being initialization of dll
- Bug fixed in Files Attachment, bug occured when embed dll with "Never write to disk" option and when the process being finalize
- Bug fixed with Hardware Lock "Windows Serial Key"
- Bug fixed with Hardware Lock "HDD Serial Number"

The Enigma Protector v1.54 Build 20081001
- Added Chinese Traditional translation
- Improved plugins: memorybreakpoints, closehandle, hardwarebreakpoints, int3, they support Windows 2008 now
- Bug fixed in files attachment, extracted files had been always deleted after application terminated, even if "Delete on Exit" is disabled
- Bug fixed in plugins: memorybreakpoints, closehandle, hardwarebreakpoints, int3 crashed on some versions of Windows 2003/Vista
- Bug fixed plugins now support exceptions under Windows 2003/2008
- Bug fixed very particular applications crashed on Windows 2003/2008 if Registration Dialog/ Any Messages are enabled
- Bug fixed when enabled any trial limitation, option "Terminate after seconds" and number of seconds > 0, trial message appear few times against only one
- Bug fixed in very particular .NET executables
- Bug fixed in project files in Examples folder

The Enigma Protector v1.53 Build 20080915
- Added possibility to embed any types of files into protected file without writing these files to the disk
- Added about 17 anti-debugger plugins
- Added possibility to embed own plugins into protected files
- Added Italian translation
- Added checkup of Windows user's privileges
- Added hardware lock - "Hard Disk Serial Number"
- Added support JPEG and PNG files for Splash Screen
- Added "Close on mouse click" in Splash Screen
- Added "Stop execution when splash screen is showing" in Splash Screen
- Added checkup of Virtualization tools - VirtualBox
- Added examples of using of EP_ProtectedStringByID and EP_ProtectedStringByKey functions, see Examples\ProtectedStrings folder
- Added API EP_ProtectedStringByID and EP_ProtectedStringByKey that return protected strings
- Added Protected Strings feature to protect user defined strigns
- Added examples of EP_MiscGetWatermark, see Examples\Watermarks folder
- Added Enigma API EP_MiscGetWatermark that returns watermarks from the file
- Bug fixed when protecting overlay files with "File size preservation" feature enabled
- Bug fixed in Checkup - Windows version
- Bug fixed in some dlls that are using DecryptOnExecute marker
- Bug fixed in some applications when reading relocations directory
- Bug fixed when using DecryptOnExecute marker in dlls
- Bug fixed in some applications that are protected with Advanced force import protection + DecryptOnExecute marker
- Bug fixed with loading older created project files
- Bug fixed when executing older protected version after new one has been executed

The Enigma Protector v1.51 Build 20080715
- Added console version
- Added possibility to use message designer variables in Registration Dialog Designer
- Added feature to reset local special info for debugging puporses (for example, to reset local startup password information)
- Added feature that allows to ask startup password after defined number of days
- Added possibility to change startup password
- Added feature to hide/unhide symbols of startup password
- Added feature that allows execution only if the module is registered
- Added variables %AppName% %AppVers% for dialog designer that return Application Name and Version
- Added Hardware Lock type that depends on Windows Serial Key
- Added Enigma API EP_RegKeyCreationDate that returns the date when the key was created
- Added OnClose event for form in Registration Dialog Designer
- Added protection against memory breakpoints
- Added feature to add/view matermarks in the protected file
- Added function to show key expiration reminder
- Added fast help link on the settings pages
- Improved algorithm of loading Virtual Machine functions from project file, it is faster now
- Remade startup password window
- Bug fixed Entry Point Protection feature when protecting executable files compiled with Visual Studio 8+
- Bug fixed when protected file contains import dll which does not import any function
- Bug fixed with Files Attachment function if embed already protected dll files
- Bug fixed with loading of project that contains files attachment feature
- Bug fixed in Registration Dialog Designer, width did not save for label components

The Enigma Protector v1.50 Build 20080601
- Added possibility to show custom registration dialog
- Added few new variables for message designer, see manual
- Added checkup of Virtualization Tools (Virtual Machines) like VMWare, Virtual PC, Sandboxie
- Added checkup of Windows Version
- Added checkup of executed processes (by file name, window text, window class)
- Added checkup of external files
- Added File Attachment function
- Added possibility to send emails to selected users in Enigma Mailer
- Added possibility to generate emails for selected users in Enigma Mailer
- Added emulating of LoadLibraryA function for dinamic Enigma API calling
- Added mass functions selecting for Virtual Machine
- Remade struct of project file, some data may be lost if you open older project (no event registration data lose)
- Improved plugin for file analyzing
- Improved displaying of Virtual Machine functions
- Improved MAP file parsing algorithm
- Improved protection against Original Entry Point finding
- Bug fixed running on Windows Vista
- Bug fixed running protected files on Windows 2008 Server Windows 2003 Server
- Bug fixed running protected files on Windows 9x under VMWare
- Bug fixed with antidebugger protection
- Bug fixed with files attachment if the attached files are compressed
- Bug fixed with protecting files that do not support SEH
- Bug fixed with dynamic calling API's
- Bug fixed registration info reading APIs were failed on Windows Vista with minimum privileges
- Bug fixed with Decrypt_On_Execute marker (protected file sometimes crashed)
- Bug fixed with file entropy for files with overlays
- Bug fixed with Trial Limitation of Eexuction Time (expired even if registered)
- Bug fixed with Enigma API while dinamically calling
- Bug fixed with keygen.dll VerifyRegistrationInfo (remade TVerifyKey type)

The Enigma Protector v1.40 Build 20080126
- Added Enigma Protector Registration Manager
- Added Enigma Protector Mailer
- Added Czech GUI translation
- Added possibility to mark registration keys as stolen
- Added trial limitation from date to date, Enigma API EP_TrialDateTillDate that returns start and end dates of trial
- Added support of Visual Studio 2008 map files
- Added KG_VerifyRegistrationInfo function in keygen.dll for verifying and extracting values from registration keys
- Bug fixed with Virtual Machine, processing instruction mov [reg32 + imm8], imm32
- Bug fixed, trial does not expire immediately if the execution time sets to 0 minutes
- Bug fixed, Enigma Protector manual sometimes did not open from main manu.

The Enigma Protector v1.37 Build 20071226
- Added various output registration key types (10, 16, 32, 64 digs)
- Added short type of registration key (reg. keys size is up to 41 symbols)
- Bug fixed with Virtual Machine, processing instruction cmp [reg32], imm32
- Bug fixed with Enigma Protector executing under Windows Vista

The Enigma Protector v1.35 Build 20071012
- Added Virtual Machine protection feature for extended protection! Supports Delphi, CBulder, VC.
- Solved DEP problem. Protected applications are started now without DEP exception.
- Added example programs for C#.
- Added Enigma API support for .NET applications. 
- Bug fixed with Enigma API function EP_RegKeyExpirationDate for Visual Basic.

The Enigma Protector v1.33 Build 20070810
- Added message variables, you are able to use special strings to show some numerical values.
- Added trial feature which allows to open file when trial has expired
- Added registration feature to allow protected module only time limited registration keys
- Added registration feature to allow protected module only hardware locked registration keys
- Added Enigma API function EP_RegKeyExpirationDate - returns key expiration date in case the registration key is valid and time limited.
- Added checkup of executed copies of protected module. 
- Added status icons on the main settings tree
- Added multi-languages support
- Added file information panel in the Input settings tab. It provides with special information about file type, size, PE header values.
- Added trial limiation feature "Limit of execution time". You are able to limit time of module work within execution. Added Enigma API function EP_TrialExecutionTime to return parameters of this limitation. Added function examples.
- Added .NET executables support
- Minor bugs fixed with trial Enigma API
- Minor bugs fixed with Enigma API for Visual Basic compiled executables.

The Enigma Protector v1.31 Build 20070615
- Added dimensions of the loader messages are proportional to its content now
- Added features for run-time trial controling, if the protected application will work much time without termination and trial will expire while application works then expiration event will be processed as well
- Added feature for deleting of the local trial and registration information for debugging purpose.
- Rewritten main options tree of GUI
- Rewritten algorithm of CPU Hardware Lock
- Bug fixed with trial limitation of expiration date
- Bug fixed in Computer Name Hardware Lock
- Bug fixed something also with command line
- Bug fixed with "File entry point obfuscation"

The Enigma Protector v1.31 Build 20070604
- Bugs fixed with command line supporting
- Bugs fixed in trial settings saving

The Enigma Protector v1.30 Build 20070528
- Renamed Enigma API
- Bugs fixed with "Anti-debugger protection" for vmwares

The Enigma Protector v1.30 Build 20070223
- Added builded Registration Keys Generator
- Added feature for checking of hardware breakpoints
- Bugs fixed in trials...
- Bugs fixed in "Anti-debugger protection" for Win 9x
- Bugs fixed with Virtual Machine for emulation of functions from map file

The Enigma Protector v1.30 Build 20070131
- Bugs fixed with Enigma API

The Enigma Protector v1.30 Build 20070130

- Added several instructions for EP emulation
- Bug fixed with CRC checking
- Bug fixed with EP import recovering