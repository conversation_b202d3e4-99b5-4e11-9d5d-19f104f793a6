<?xml version="1.0"?>
<CONFIG>
  <ProjectOptions>
    <PathDelim Value="\"/>
    <Version Value="8"/>
    <General>
      <Flags>
        <MainUnitHasCreateFormStatements Value="False"/>
        <MainUnitHasTitleStatement Value="False"/>
        <UseDefaultCompilerOptions Value="True"/>
      </Flags>
      <MainUnit Value="0"/>
      <TargetFileExt Value=".exe"/>
      <ResourceType Value="res"/>
      <UseXPManifest Value="True"/>
      <Icon Value="0"/>
      <ActiveWindowIndexAtStart Value="0"/>
    </General>
    <i18n>
      <EnableI18N LFM="False"/>
    </i18n>
    <VersionInfo>
      <Language Value=""/>
      <CharSet Value=""/>
      <StringTable Comments="" CompanyName="" FileDescription="" FileVersion="" InternalName="" LegalCopyright="" LegalTrademarks="" OriginalFilename="" ProductName="" ProductVersion=""/>
    </VersionInfo>
    <PublishOptions>
      <Version Value="2"/>
      <IgnoreBinaries Value="False"/>
      <IncludeFileFilter Value="*.(pas|pp|inc|lfm|lpr|lrs|lpi|lpk|sh|xml)"/>
      <ExcludeFileFilter Value="*.(bak|ppu|ppw|o|so);*~;backup"/>
    </PublishOptions>
    <RunParams>
      <local>
        <FormatVersion Value="1"/>
      </local>
    </RunParams>
    <Units Count="1">
      <Unit0>
        <Filename Value="plugin.lpr"/>
        <IsPartOfProject Value="True"/>
        <UnitName Value="plugin"/>
        <IsVisibleTab Value="True"/>
        <EditorIndex Value="0"/>
        <WindowIndex Value="0"/>
        <TopLine Value="1"/>
        <CursorPos X="15" Y="1"/>
        <UsageCount Value="20"/>
        <Loaded Value="True"/>
      </Unit0>
    </Units>
    <JumpHistory Count="0" HistoryIndex="-1"/>
  </ProjectOptions>
  <CompilerOptions>
    <Version Value="9"/>
    <PathDelim Value="\"/>
    <Target>
      <Filename Value="plugin"/>
    </Target>
    <SearchPaths>
      <IncludeFiles Value="$(ProjOutDir)\"/>
      <UnitOutputDirectory Value="lib\$(TargetCPU)-$(TargetOS)"/>
    </SearchPaths>
    <Linking>
      <Options>
        <ExecutableType Value="Library"/>
      </Options>
    </Linking>
    <Other>
      <CompilerPath Value="$(CompPath)"/>
    </Other>
  </CompilerOptions>
  <Debugging>
    <Exceptions Count="3">
      <Item1>
        <Name Value="EAbort"/>
      </Item1>
      <Item2>
        <Name Value="ECodetoolError"/>
      </Item2>
      <Item3>
        <Name Value="EFOpenError"/>
      </Item3>
    </Exceptions>
  </Debugging>
</CONFIG>
