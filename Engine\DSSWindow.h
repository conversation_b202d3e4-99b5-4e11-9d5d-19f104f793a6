static int (__thiscall *KalItemShop1)(void *a, const char *a2, int a3) = (int (__thiscall*)(void*,const char*,int))0x005C1A80;
static int (__thiscall *KalItemShop2)(void *a, const char *a2, int a3) = (int (__thiscall*)(void*,const char*,int))0x005C1B90;
static int (__thiscall *KalItemShop3)(void *a, const char *a2, int a3) = (int (__thiscall*)(void*,const char*,int))0x005C4880;
static int (__stdcall *KalShop)(char a1) = (int (__stdcall*)(char))0x005C47D0;
static int (__cdecl *ShopKal)() = (int (__cdecl*)())0x005C1260;

int __fastcall MyKalItemShop1(void *a, void *edx, const char *a2, int a3)
{
	if (_stricmp(a2, "cancel") && _stricmp(a2, "close"))
	{
		if (!_stricmp(a2, "ok"))
		{
			if (*((DWORD*)a + 111))
			{
				if (!*((BYTE*)a + 452))
				{
					*((BYTE*)a + 452) = 1;
					KalShop(0);
					Interface<IPackets> Packets;
					Packets->Send(183, "bbdw", 4, *(BYTE*)(ShopKal() + 4), *((DWORD*)a + 112), *((DWORD*)a + 110));
				}
			}
			
			return (*(int (__thiscall **)(void *))(*(DWORD *)a + 232))(a);
		}
	}

	return KalItemShop1(a,a2,a3);
}

int __fastcall MyKalItemShop2(void *a, void *edx, const char *a2, int a3)
{
	if (_stricmp(a2, "cancel") && _stricmp(a2, "close"))
	{
		if (!_stricmp(a2, "ok"))
		{
			if (*((DWORD*)a + 111))
			{
				if (!*((BYTE*)a + 452))
				{
					*((BYTE*)a + 452) = 1;
					KalShop(0);
					Interface<IPackets> Packets;
					Packets->Send(183, "bbdw", 4, *(BYTE*)(ShopKal() + 4), *((DWORD*)a + 112), *((DWORD*)a + 110));
				}
			}
			
			return (*(int (__thiscall **)(void *))(*(DWORD *)a + 232))(a);
		}
	}

	return KalItemShop2(a,a2,a3);
}

int __fastcall MyKalItemShop3(void *a, void *edx, const char *a2, int a3)
{
	if (_stricmp(a2, "cancel") && _stricmp(a2, "close"))
	{
		if (!_stricmp(a2, "ok"))
		{
			if (*((DWORD*)a + 111) && !*((BYTE*)a + 480))
			{
				*((BYTE*)a + 480) = 1;
				KalShop(0);
				Interface<IPackets> Packets;
				if (*((DWORD*)a + 119) < 16u)
					Packets->Send(183, "bbdws", 2, *(BYTE*)(ShopKal() + 4), *((DWORD*)a + 112), *((DWORD*)a + 110), (int)((char*)a + 456));
				else
					Packets->Send(183, "bbdws", 2, *(BYTE*)(ShopKal() + 4), *((DWORD*)a + 112), *((DWORD*)a + 110), *((DWORD*)a + 114));
			}
			
			return (*(int (__thiscall **)(void *))(*(DWORD *)a + 232))(a);
		}
	}

	return KalItemShop3(a,a2,a3);
}