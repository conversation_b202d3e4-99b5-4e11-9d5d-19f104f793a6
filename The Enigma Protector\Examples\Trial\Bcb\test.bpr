<?xml version='1.0' encoding='utf-8' ?>
<!-- C++Builder XML Project -->
<PROJECT>
  <MACROS>
    <VERSION value="BCB.05.03"/>
    <PROJECT value="test.exe"/>
    <OBJFILES value="test.obj test_unit.obj"/>
    <RESFILES value="test.res"/>
    <IDLFILES value=""/>
    <IDLGENFILES value=""/>
    <DEFFILE value=""/>
    <RESDEPEN value="$(RESFILES) test_unit.dfm"/>
    <LIBFILES value=""/>
    <LIBRARIES value=""/>
    <SPARELIBS value="Vcl50.lib"/>
    <PACKAGES value="Vcl50.bpi Vclx50.bpi bcbsmp50.bpi Vcldb50.bpi vclado50.bpi ibsmp50.bpi 
      VCLBDE50.bpi vcldbx50.bpi Qrpt50.bpi TeeUI50.bpi TeeDB50.bpi Tee50.bpi 
      Dss50.bpi TeeQR50.bpi VCLIB50.bpi Vclmid50.bpi vclie50.bpi Inetdb50.bpi 
      Inet50.bpi NMFast50.bpi webmid50.bpi bcbie50.bpi dclocx50.bpi 
      bcb2kaxserver50.bpi"/>
    <PATHCPP value=".;"/>
    <PATHPAS value=".;"/>
    <PATHRC value=".;"/>
    <PATHASM value=".;"/>
    <DEBUGLIBPATH value="$(BCB)\lib\debug"/>
    <RELEASELIBPATH value="$(BCB)\lib\release"/>
    <LINKER value="tlink32"/>
    <USERDEFINES value="_DEBUG"/>
    <SYSDEFINES value="_RTLDLL;NO_STRICT;USEPACKAGES"/>
    <MAINSOURCE value="test.cpp"/>
    <INCLUDEPATH value="$(BCB)\include;$(BCB)\include\vcl"/>
    <LIBPATH value="$(BCB)\lib\obj;$(BCB)\lib"/>
    <WARNINGS value="-w-par"/>
  </MACROS>
  <OPTIONS>
    <IDLCFLAGS value="-I$(BCB)\include -I$(BCB)\include\vcl -src_suffix cpp -D_DEBUG -boa"/>
    <CFLAG1 value="-Od -H=$(BCB)\lib\vcl50.csm -Hc -Vx -Ve -X- -r- -a8 -b- -k -y -v -vi- -c 
      -tW -tWM"/>
    <PFLAGS value="-$YD -$W -$O- -v -JPHNE -M"/>
    <RFLAGS value=""/>
    <AFLAGS value="/mx /w2 /zd"/>
    <LFLAGS value="-D&quot;&quot; -aa -Tpe -x -Gn -v"/>
  </OPTIONS>
  <LINKER>
    <ALLOBJ value="c0w32.obj $(PACKAGES) Memmgr.Lib sysinit.obj $(OBJFILES)"/>
    <ALLRES value="$(RESFILES)"/>
    <ALLLIB value="$(LIBFILES) $(LIBRARIES) import32.lib cp32mti.lib"/>
  </LINKER>
  <IDEOPTIONS>
[Version Info]
IncludeVerInfo=0
AutoIncBuild=0
MajorVer=1
MinorVer=0
Release=0
Build=0
Debug=0
PreRelease=0
Special=0
Private=0
DLL=0
Locale=1049
CodePage=1251

[Version Info Keys]
CompanyName=
FileDescription=
FileVersion=*******
InternalName=
LegalCopyright=
LegalTrademarks=
OriginalFilename=
ProductName=
ProductVersion=*******
Comments=

[Debugging]
DebugSourceDirs=$(BCB)\source\vcl

[Parameters]
RunParams=
HostApplication=
RemoteHost=
RemotePath=
RemoteDebug=0

[Compiler]
ShowInfoMsgs=0
LinkDebugVcl=0
LinkCGLIB=0

[CORBA]
AddServerUnit=1
AddClientUnit=1
PrecompiledHeaders=1

[Language]
ActiveLang=
ProjectLang=
RootDir=
  </IDEOPTIONS>
</PROJECT>