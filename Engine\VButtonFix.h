#define VK_A 0x41
#define VK_D 0x44
#define VK_S 0x53
#define VK_W 0x57

int __fastcall OnOk(void *Argument, void *edx)
{
	Interface<IPackets> Packets;

	if (*((DWORD *)Argument + 111) == 1000)
		Packets->Send(254,"dss",1,Packets->RecCaller.c_str(),Packets->Recall.c_str());

	return Engine::KGameSys::OnOk(Argument);
}

int __fastcall OnCancel(void *Argument, void *edx)
{
	Interface<IPackets> Packets;

	if (*((DWORD *)Argument + 111) == 1000)
		Packets->Send(254,"dss",0,Packets->RecCaller.c_str(),Packets->Recall.c_str());

	return Engine::KGameSys::OnCancel(Argument);
}

signed int __cdecl PressKey(int Word, void *edx)
{
	if (Engine::KGameSys::PressedKey && *Engine::CGame_Character::m_Master)
	{
		int ReCheck = Engine::KGameSys::Check();
		if (ReCheck && ReCheck && (*(int (__thiscall **)(int))(*(DWORD *)ReCheck + 148))(ReCheck))
		{
			return 0;
		} else {
			if (Word == 'v' || Word == 'V')
			{
				if ( !Engine::KGameSys::WindowCheck("Honor") )
					return Engine::KGameSys::OpenWindow("Honor", 0,0,0,0,0);
				else
					return Engine::KGameSys::CloseWindow("Honor");
			}

			if (Word == 'B' || Word == 'b')
			{
				Interface<IPackets> Packets;
				Packets->Send(255, "dd", 5, 1);
			}
		}
	}

	return Engine::KGameSys::PressKey(Word);
}

int __fastcall MySetMovement(int Value, void *edx, HWND hWnd, UINT Msg, int wParam, LPARAM lParam)
{
	if (*Engine::CGame_Character::m_Master)
	{
		if (*(DWORD*)(*Engine::CGame_Character::m_Master + 18416) & 256
			|| *(DWORD*)(*Engine::CGame_Character::m_Master + 18416) & 268435456
			|| *(DWORD*)(*Engine::CGame_Character::m_Master + 18400) & 512
			|| *(DWORD*)(*Engine::CGame_Character::m_Master + 18400) & 32
			|| *(DWORD*)(*Engine::CGame_Character::m_Master + 18400) & 128
			|| *(DWORD*)(*Engine::CGame_Character::m_Master + 18400) & 1048576
			|| Engine::Move::MoveCheck(4096, 0) || Engine::Move::MoveCheck(16384, 0)
			|| *(DWORD*)(*Engine::CGame_Character::m_Master + 18372) & 4194304
			|| *(DWORD*)(*Engine::CGame_Character::m_Master + 18368) & 32
			|| *(DWORD*)(*Engine::CGame_Character::m_Master + 18368) & 16
			|| *(DWORD*)(*Engine::CGame_Character::m_Master + 18368) & 4
			|| *(DWORD*)(*Engine::CGame_Character::m_Master + 18384) & 1073741824
			|| *(DWORD*)(*Engine::CGame_Character::m_Master + 18388) & 63
			|| *(DWORD*)(*Engine::CGame_Character::m_Master + 18384) & 2147483648
			|| *(DWORD*)(*Engine::CGame_Character::m_Master + 18452))
		{ return Engine::Move::SetMovement(Value,hWnd,Msg,wParam,lParam); } else {
			if (!*(DWORD*)0x009F3FC0 && GetFocus())
			{
				if (!GetAsyncKeyState(VK_A) && !GetAsyncKeyState(VK_D) && !GetAsyncKeyState(VK_S) && GetAsyncKeyState(VK_W) && GetKeyState(VK_W) & 0x8000) *(DWORD*)(Value + 1040) = 5;
			}
		}

		if (*(DWORD*)(Value + 1040) && !GetFocus()) *(DWORD*)(Value + 1040) = 0;
	}

	return Engine::Move::SetMovement(Value,hWnd,Msg,wParam,lParam);
}

int __cdecl AuctionOnOpen(const char *name, int Argument, int Value, int Type, int nForce, int x)
{
	int Check = Engine::KGameSys::OpenWindow(name,Argument,Value,Type,nForce,x);
	if ((std::string)name == "auction_main")
	{
		Engine::Auction::Enable((void*)Check);
		Engine::Auction::Update((void*)Check);
	}
	return Check;
}