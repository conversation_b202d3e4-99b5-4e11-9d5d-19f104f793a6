﻿<?xml version="1.0" encoding="UTF-8"?>
<EnigmaProject Version="1.60" Modification="560">
  <Compatibility>
    <Level1>True</Level1>
    <Level2>True</Level2>
    <Level3>True</Level3>
    <Level4>True</Level4>
    <Level5>True</Level5>
    <Level6>True</Level6>
    <Level7>True</Level7>
  </Compatibility>
  <EnigmaProjectDetails>
    <FullNameOfProject>Default project</FullNameOfProject>
    <ProjectDescription>Description of the default project</ProjectDescription>
  </EnigmaProjectDetails>
  <KeysGenerator>
    <EnableLogging>False</EnableLogging>
  </KeysGenerator>
  <Input>
    <FileName>.\Test.exe</FileName>
    <ProductName>Test product</ProductName>
    <VersionInfo>1.0</VersionInfo>
    <ProductInfoFromResources>False</ProductInfoFromResources>
  </Input>
  <Output>
    <SameAsInputFile>False</SameAsInputFile>
    <FileName>.\Test_protected.exe</FileName>
    <DoNotBackup>False</DoNotBackup>
  </Output>
  <AdvanceInput>
    <ProtectAfter>False</ProtectAfter>
    <Files Count="0"/>
  </AdvanceInput>
  <Compression>
    <Type>Best</Type>
    <Mode>AllResourcesExceptIconAndVersion</Mode>
  </Compression>
  <OnlineServices>
    <Common>
      <IsConfigured>False</IsConfigured>
      <ProductId/>
      <ActivationLinkId/>
      <ActivationUrlHost/>
      <ActivationUrlPort>0</ActivationUrlPort>
      <ActivationUrlEndPoint/>
      <ProductName/>
      <ActivationLinkName/>
      <SiteUrl/>
      <AdministratorLogin/>
      <AdministratorPassword/>
      <TestOrderUrl/>
    </Common>
    <OnlineActivationSettings>
      <ActivateEveryExecution>False</ActivateEveryExecution>
    </OnlineActivationSettings>
    <OnlineActivationDialog>
      <Enabled>False</Enabled>
      <ShowOnUnregistered>True</ShowOnUnregistered>
      <ShowSucceededMessage>False</ShowSucceededMessage>
      <ShowFailedMessage>False</ShowFailedMessage>
      <MessageSucceeded>
        <Icon>0</Icon>
        <Title/>
        <Text/>
        <UseHL>False</UseHL>
        <HLSentence/>
        <HLURL/>
        <AutoClose>False</AutoClose>
        <CloseDelay>0</CloseDelay>
        <Disable>False</Disable>
        <DisableDelay>0</DisableDelay>
        <FontName/>
        <FontCharset>0</FontCharset>
        <FontColor>0</FontColor>
        <FontSize>0</FontSize>
        <FontBold>False</FontBold>
        <FontItalic>False</FontItalic>
        <FontUnderline>False</FontUnderline>
        <FontStrikeout>False</FontStrikeout>
      </MessageSucceeded>
      <MessageFailed>
        <Icon>0</Icon>
        <Title/>
        <Text/>
        <UseHL>False</UseHL>
        <HLSentence/>
        <HLURL/>
        <AutoClose>False</AutoClose>
        <CloseDelay>0</CloseDelay>
        <Disable>False</Disable>
        <DisableDelay>0</DisableDelay>
        <FontName/>
        <FontCharset>0</FontCharset>
        <FontColor>0</FontColor>
        <FontSize>0</FontSize>
        <FontBold>False</FontBold>
        <FontItalic>False</FontItalic>
        <FontUnderline>False</FontUnderline>
        <FontStrikeout>False</FontStrikeout>
      </MessageFailed>
      <Form>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</Form>
    </OnlineActivationDialog>
  </OnlineServices>
  <RegistrationFeatures>
    <RegistrationDataStoring>
      <Storage>SystemRegistry</Storage>
      <Registry>
        <Base>CurrentUser</Base>
        <Path>\SOFTWARE\Test product\</Path>
      </Registry>
      <FileSystem>
        <Base>MyDocumentsFolder</Base>
        <Path>\Test product\license.dat</Path>
        <SetFileAttributes>False</SetFileAttributes>
        <FileAttributesArchive>False</FileAttributesArchive>
        <FileAttributesReadOnly>True</FileAttributesReadOnly>
        <FileAttributesHidden>False</FileAttributesHidden>
        <FileAttributesSystem>False</FileAttributesSystem>
      </FileSystem>
      <EncryptRegInfo>False</EncryptRegInfo>
      <EncryptWithHardwareId>False</EncryptWithHardwareId>
    </RegistrationDataStoring>
    <Common>
      <Unicode>False</Unicode>
      <AllowHardwareLockedKeys>False</AllowHardwareLockedKeys>
      <AllowTimeLimitedKeys>False</AllowTimeLimitedKeys>
      <AllowExecOnlyIfRegistered>False</AllowExecOnlyIfRegistered>
      <KeyMode>2</KeyMode>
      <KeyBase>3</KeyBase>
      <EncryptApplication>False</EncryptApplication>
      <AllowRegisterAfterKeys>False</AllowRegisterAfterKeys>
      <AllowRegisterBeforeKeys>False</AllowRegisterBeforeKeys>
      <AllowExecutionsKeys>False</AllowExecutionsKeys>
      <AllowDaysKeys>False</AllowDaysKeys>
      <AllowRunTimeKeys>False</AllowRunTimeKeys>
      <AllowGlobalTimeKeys>False</AllowGlobalTimeKeys>
      <AllowCountryLockedKeys>False</AllowCountryLockedKeys>
    </Common>
    <HardwareAssociation>
      <UseVolumeSerialDrive>False</UseVolumeSerialDrive>
      <UseSystemVolumeName>False</UseSystemVolumeName>
      <UseComputerName>False</UseComputerName>
      <UseCPUType>False</UseCPUType>
      <UseWindowsKey>False</UseWindowsKey>
      <UseMotherboard>False</UseMotherboard>
      <UseHDDSerial>True</UseHDDSerial>
      <UseUserName>False</UseUserName>
      <VolumeSerialDriveChange>0</VolumeSerialDriveChange>
      <SystemVolumeNameChange>0</SystemVolumeNameChange>
      <ComputerNameChange>0</ComputerNameChange>
      <CPUTypeChange>0</CPUTypeChange>
      <MotherboardChange>0</MotherboardChange>
      <WindowsKeyChange>0</WindowsKeyChange>
      <HDDSerialChange>0</HDDSerialChange>
      <UserNameChange>0</UserNameChange>
    </HardwareAssociation>
    <Constants>
      <EncryptedConstant>2034581521</EncryptedConstant>
      <Secure1>C5A669907E74F9D5851024717DD34784</Secure1>
      <WatermarkConstant>863D5CF3E7CABC774B1860C6E4E99860</WatermarkConstant>
      <Mode512>
        <PublicKey/>
        <PrivateKey/>
      </Mode512>
      <Mode768>
        <PublicKey/>
        <PrivateKey/>
      </Mode768>
      <Mode1024>
        <PublicKey>0209FACFBDDA057160EAD06D7AA040249230CDKS4NZ2SFUE3UYAGRSJESEL254LQTWCGZGG375QMM4CGPPHQ2ERZ2UYFJV256DPJPAZYC5DFL4CNF9LJL9574F5BJFSQFUDPQCK5FYQMSGGDPSWG7JGX4XDRAB89U66AT256ULPPXGGXBL2W8JKKBM9TZ9UDGRP9QYAQCFGZ7K8MZHUX85JKTYZGWVJJD4PCPAFWFY49HP2VT70CDHQFJ5KBZGWVHSYQCDKAJ4YAVVKL5FN5GWCL4VWAUTHYT28YMFG9BZHZKJGYY5TYZFWYUNP7GECR8HQL2FVJNJRYKGYXNYWR9T953QWLWQ3XWJXUCG4SKKKNUTZ3SN875F36XE2KHYXNHJL5XBG2VZN88M3ZRGTX9R855GWWM6XRVCUVKBS27TTYR72YMCH36874LF9ZQCAK7D0CDMSFCJEWY3BMLZT5M2B3LFYDEBNYDJWGAB24CCBBZ4K8B32NR5WPNR8HQLA3R5AFBXN7JJFV33YXR73PN9RQAYVNCTR3XNUAUQ55KPLWZBHH5NHM5FG4BYHR2F5F36S6NQV5324K9G9DQ4KDLH63REDAJKVH6EHKHTS2VMSLQ6J8UANZQ2EFX6C95QLYZC9PW6QVSUEV7ANPYZ</PublicKey>
        <PrivateKey>020JA9MKWYZ8JWXN4JL7XSRB8T5U6HXDPAF0ADTMGQK8DDKCFHWB8CNJK25R3A95XT9VDWJVHU8RPW6SK9662NRGGHYGGY7G2RMNQQ4L659PY46SKP6AX29PDHV2ELTMN6LBVFAM8WRG57EVWZ7C4JK62BTP48YEZ34DSXAF7YW9PNQSRU7QABH2FA6J736MWP4A5Q2FECFDZ3R59AC020XHAY8884DLKGUN7HWCS82S7N37D7B6MM020XHAY8884DLKGUN7HWCS82S7N37D7B6MM</PrivateKey>
      </Mode1024>
      <Mode2048>
        <PublicKey/>
        <PrivateKey/>
      </Mode2048>
      <Mode3072>
        <PublicKey/>
        <PrivateKey/>
      </Mode3072>
      <Mode4096>
        <PublicKey/>
        <PrivateKey/>
      </Mode4096>
      <HardwareConstant>9FACFBDDA057160EAD06D7AA04024923</HardwareConstant>
    </Constants>
    <RegistrationDialog>
      <Form>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</Form>
      <Enabled>False</Enabled>
      <ShowOnTrialExpire>False</ShowOnTrialExpire>
      <ShowOnUnregistered>True</ShowOnUnregistered>
      <ShowCorrectMessage>False</ShowCorrectMessage>
      <ShowIncorrectMessage>False</ShowIncorrectMessage>
      <MessageCorrect>
        <Title>The Registration Succeeded</Title>
        <Text>Thanks for registration!</Text>
        <UseHL>False</UseHL>
        <HLSentence>The Enigma Protector</HLSentence>
        <HLURL>http://www.enigmaprotector.com/</HLURL>
        <Icon>0</Icon>
        <AutoClose>False</AutoClose>
        <CloseDelay>0</CloseDelay>
        <Disable>False</Disable>
        <DisableDelay>0</DisableDelay>
        <FontName/>
        <FontCharset>0</FontCharset>
        <FontColor>0</FontColor>
        <FontSize>0</FontSize>
        <FontBold>False</FontBold>
        <FontItalic>False</FontItalic>
        <FontUnderline>False</FontUnderline>
        <FontStrikeout>False</FontStrikeout>
      </MessageCorrect>
      <MessageIncorrect>
        <Title>The Registration did not succeeded</Title>
        <Text>Your registration information is incorrect!</Text>
        <UseHL>False</UseHL>
        <HLSentence>The Enigma Protector</HLSentence>
        <HLURL>http://www.enigmaprotector.com/</HLURL>
        <Icon>0</Icon>
        <AutoClose>False</AutoClose>
        <CloseDelay>0</CloseDelay>
        <Disable>False</Disable>
        <DisableDelay>0</DisableDelay>
        <FontName/>
        <FontCharset>0</FontCharset>
        <FontColor>0</FontColor>
        <FontSize>0</FontSize>
        <FontBold>False</FontBold>
        <FontItalic>False</FontItalic>
        <FontUnderline>False</FontUnderline>
        <FontStrikeout>False</FontStrikeout>
      </MessageIncorrect>
    </RegistrationDialog>
    <KeyExpirationReminder>
      <Enabled>False</Enabled>
      <DaysBeforeExpiration>30</DaysBeforeExpiration>
      <Message>
        <Title>Registration Key Expiration Reminder</Title>
        <Text>Your registration key will expire in %DaysToKeyExp% days. 
Please contact to our support team to obtain new one!
Use the link below to visit our web site.</Text>
        <UseHL>False</UseHL>
        <HLSentence>The Enigma Protector</HLSentence>
        <HLURL>http://enigmaprotector.com/</HLURL>
        <Icon>0</Icon>
        <AutoClose>False</AutoClose>
        <CloseDelay>0</CloseDelay>
        <Disable>False</Disable>
        <DisableDelay>0</DisableDelay>
        <FontName/>
        <FontCharset>0</FontCharset>
        <FontColor>0</FontColor>
        <FontSize>0</FontSize>
        <FontBold>False</FontBold>
        <FontItalic>False</FontItalic>
        <FontUnderline>False</FontUnderline>
        <FontStrikeout>False</FontStrikeout>
      </Message>
    </KeyExpirationReminder>
    <EncryptWithHardwareID>
      <Enabled>False</Enabled>
      <HardwareID/>
    </EncryptWithHardwareID>
  </RegistrationFeatures>
  <CheckUp>
    <AntiDebugger>
      <Enabled>False</Enabled>
      <ShowMessageBeforeTermination>True</ShowMessageBeforeTermination>
      <Message>
        <Title>Debugger warning</Title>
        <Text>A debugger is detected on this computer.
Applications such as WinIce/SoftIce, OllyDbg, TWD could be executing.
This program will not execute while debuggers are running.</Text>
        <UseHL>False</UseHL>
        <HLSentence>Click here for help</HLSentence>
        <HLURL>http://www.yourcompany.com/help/</HLURL>
        <Icon>0</Icon>
        <AutoClose>False</AutoClose>
        <CloseDelay>0</CloseDelay>
        <Disable>False</Disable>
        <DisableDelay>0</DisableDelay>
        <FontName/>
        <FontCharset>0</FontCharset>
        <FontColor>0</FontColor>
        <FontSize>0</FontSize>
        <FontBold>False</FontBold>
        <FontItalic>False</FontItalic>
        <FontUnderline>False</FontUnderline>
        <FontStrikeout>False</FontStrikeout>
      </Message>
      <CheckAtRuntime>True</CheckAtRuntime>
    </AntiDebugger>
    <ControlSum>
      <Enabled>False</Enabled>
      <FileIntegrityChecking>False</FileIntegrityChecking>
      <RTIntegrityChecking>False</RTIntegrityChecking>
      <ShowMessageBeforeTermination>True</ShowMessageBeforeTermination>
      <Message>
        <Title>Virus warning</Title>
        <Text>Program integrity check failed. 
Please check for viruses on your computer. 
If program is not infected, it had been patched 
or tampered  - please re-download and re-install. 
This program will now close.</Text>
        <UseHL>False</UseHL>
        <HLSentence>Click here for help</HLSentence>
        <HLURL>http://www.yourcompany.com/help/</HLURL>
        <Icon>0</Icon>
        <AutoClose>False</AutoClose>
        <CloseDelay>0</CloseDelay>
        <Disable>True</Disable>
        <DisableDelay>10</DisableDelay>
        <FontName/>
        <FontCharset>0</FontCharset>
        <FontColor>0</FontColor>
        <FontSize>0</FontSize>
        <FontBold>False</FontBold>
        <FontItalic>False</FontItalic>
        <FontUnderline>False</FontUnderline>
        <FontStrikeout>False</FontStrikeout>
      </Message>
    </ControlSum>
    <StartupPassword>
      <Enabled>False</Enabled>
      <AlwaysAsk>True</AlwaysAsk>
      <AskFirstTime>False</AskFirstTime>
      <AskInDays>False</AskInDays>
      <DaysAfterAsk>30</DaysAfterAsk>
      <HidePassword>True</HidePassword>
      <AllowChange>False</AllowChange>
      <Message>
        <Title>Invalid password</Title>
        <Text>You have entered an invalid startup password.
Try to check it again or contact the software developers:</Text>
        <UseHL>False</UseHL>
        <HLSentence>Click here for registration</HLSentence>
        <HLURL>http://www.yourcompany.com/</HLURL>
        <Icon>0</Icon>
        <AutoClose>False</AutoClose>
        <CloseDelay>0</CloseDelay>
        <Disable>False</Disable>
        <DisableDelay>0</DisableDelay>
        <FontName/>
        <FontCharset>0</FontCharset>
        <FontColor>0</FontColor>
        <FontSize>0</FontSize>
        <FontBold>False</FontBold>
        <FontItalic>False</FontItalic>
        <FontUnderline>False</FontUnderline>
        <FontStrikeout>False</FontStrikeout>
      </Message>
      <Passwords Count="0"/>
    </StartupPassword>
    <FileName>
      <Enabled>False</Enabled>
      <OriginalFileName>notepad_protected.exe</OriginalFileName>
      <ShowMessageBeforeTermination>True</ShowMessageBeforeTermination>
      <Message>
        <Title>Invalid File Name</Title>
        <Text>The software&apos;s file name had been changed.  
Please restore the original file name. 
This program will now close.</Text>
        <UseHL>False</UseHL>
        <HLSentence>Press here to download official version!</HLSentence>
        <HLURL>http://www.yourcompany.com/downloads/</HLURL>
        <Icon>0</Icon>
        <AutoClose>False</AutoClose>
        <CloseDelay>0</CloseDelay>
        <Disable>True</Disable>
        <DisableDelay>10</DisableDelay>
        <FontName/>
        <FontCharset>0</FontCharset>
        <FontColor>0</FontColor>
        <FontSize>0</FontSize>
        <FontBold>False</FontBold>
        <FontItalic>False</FontItalic>
        <FontUnderline>False</FontUnderline>
        <FontStrikeout>False</FontStrikeout>
      </Message>
    </FileName>
    <DiskDrive>
      <Enabled>False</Enabled>
      <Selection>
        <Removable>True</Removable>
        <Fixed>True</Fixed>
        <Remote>True</Remote>
        <CDROM>True</CDROM>
        <RAMDisk>True</RAMDisk>
      </Selection>
      <ShowMessageBeforeTermination>True</ShowMessageBeforeTermination>
      <Message>
        <Title>Invalid drive type</Title>
        <Text>The software has checked impossible disk drive type!
Try to contact to developers or download newest version of this software at:</Text>
        <UseHL>False</UseHL>
        <HLSentence>Click here for newest version!</HLSentence>
        <HLURL>http://www.yourcompany.com/downloads/</HLURL>
        <Icon>0</Icon>
        <AutoClose>False</AutoClose>
        <CloseDelay>0</CloseDelay>
        <Disable>True</Disable>
        <DisableDelay>10</DisableDelay>
        <FontName/>
        <FontCharset>0</FontCharset>
        <FontColor>0</FontColor>
        <FontSize>0</FontSize>
        <FontBold>False</FontBold>
        <FontItalic>False</FontItalic>
        <FontUnderline>False</FontUnderline>
        <FontStrikeout>False</FontStrikeout>
      </Message>
    </DiskDrive>
    <ExecutedCopies>
      <Enabled>False</Enabled>
      <AllowCopies>1</AllowCopies>
      <ShowMessageIfCopiesCountIsExceeded>True</ShowMessageIfCopiesCountIsExceeded>
      <Message>
        <Title>Warning</Title>
        <Text>You have exceeded maximal number of executed copies. 
Application won&apos;t be started.</Text>
        <UseHL>False</UseHL>
        <HLSentence>Software information</HLSentence>
        <HLURL>http://www.yourcompany.com/</HLURL>
        <Icon>0</Icon>
        <AutoClose>False</AutoClose>
        <CloseDelay>0</CloseDelay>
        <Disable>False</Disable>
        <DisableDelay>0</DisableDelay>
        <FontName/>
        <FontCharset>0</FontCharset>
        <FontColor>0</FontColor>
        <FontSize>0</FontSize>
        <FontBold>False</FontBold>
        <FontItalic>False</FontItalic>
        <FontUnderline>False</FontUnderline>
        <FontStrikeout>False</FontStrikeout>
      </Message>
      <TerminateExecution>True</TerminateExecution>
    </ExecutedCopies>
    <UserLanguage>
      <Enabled>False</Enabled>
      <ALB>True</ALB>
      <DZA>True</DZA>
      <ARG>True</ARG>
      <ARM>True</ARM>
      <AUS>True</AUS>
      <AUT>True</AUT>
      <AZE>True</AZE>
      <BHR>True</BHR>
      <BLR>True</BLR>
      <BEL>True</BEL>
      <BLZ>True</BLZ>
      <BOL>True</BOL>
      <BRA>True</BRA>
      <BRN>True</BRN>
      <BGR>True</BGR>
      <KHM>True</KHM>
      <CAN>True</CAN>
      <CAF>True</CAF>
      <TCD>True</TCD>
      <CHL>True</CHL>
      <CHN>True</CHN>
      <COL>True</COL>
      <CRI>True</CRI>
      <HRV>True</HRV>
      <CZE>True</CZE>
      <DNK>True</DNK>
      <DOM>True</DOM>
      <ECU>True</ECU>
      <EGY>True</EGY>
      <SLV>True</SLV>
      <EST>True</EST>
      <FRO>True</FRO>
      <FIN>True</FIN>
      <FRA>True</FRA>
      <GEO>True</GEO>
      <DEU>True</DEU>
      <GRC>True</GRC>
      <GTM>True</GTM>
      <HND>True</HND>
      <HKG>True</HKG>
      <HUN>True</HUN>
      <ISL>True</ISL>
      <IND>True</IND>
      <IDN>True</IDN>
      <IRN>True</IRN>
      <IRQ>True</IRQ>
      <IRL>True</IRL>
      <ISR>True</ISR>
      <ITA>True</ITA>
      <JAM>True</JAM>
      <JPN>True</JPN>
      <JOR>True</JOR>
      <KAZ>True</KAZ>
      <KEN>True</KEN>
      <PRK>True</PRK>
      <KOR>True</KOR>
      <KWT>True</KWT>
      <KGZ>True</KGZ>
      <LVA>True</LVA>
      <LBN>True</LBN>
      <LBR>True</LBR>
      <LIE>True</LIE>
      <LTU>True</LTU>
      <LUX>True</LUX>
      <MAC>True</MAC>
      <MKD>True</MKD>
      <MYS>True</MYS>
      <MEX>True</MEX>
      <MDA>True</MDA>
      <MCO>True</MCO>
      <MNG>True</MNG>
      <MAR>True</MAR>
      <NLD>True</NLD>
      <NZL>True</NZL>
      <NIC>True</NIC>
      <NOR>True</NOR>
      <OMN>True</OMN>
      <PAK>True</PAK>
      <PAN>True</PAN>
      <PRY>True</PRY>
      <PER>True</PER>
      <PHL>True</PHL>
      <POL>True</POL>
      <PRT>True</PRT>
      <PRI>True</PRI>
      <QAT>True</QAT>
      <ROU>True</ROU>
      <RUS>True</RUS>
      <SAU>True</SAU>
      <SCG>True</SCG>
      <SGP>True</SGP>
      <SVK>True</SVK>
      <SVN>True</SVN>
      <ZAF>True</ZAF>
      <ESP>True</ESP>
      <SWE>True</SWE>
      <CHE>True</CHE>
      <TJK>True</TJK>
      <THA>True</THA>
      <TTO>True</TTO>
      <TUN>True</TUN>
      <TUR>True</TUR>
      <TWN>True</TWN>
      <UKR>True</UKR>
      <ARE>True</ARE>
      <GBR>True</GBR>
      <USA>True</USA>
      <URY>True</URY>
      <UZB>True</UZB>
      <VEN>True</VEN>
      <VNM>True</VNM>
      <YEM>True</YEM>
      <ZWE>True</ZWE>
      <AFG>True</AFG>
      <BGD>True</BGD>
      <BIH>True</BIH>
      <CAR>True</CAR>
      <ETH>True</ETH>
      <GRL>True</GRL>
      <LAO>True</LAO>
      <LBY>True</LBY>
      <MDV>True</MDV>
      <MLT>True</MLT>
      <MNE>True</MNE>
      <NEP>True</NEP>
      <NGA>True</NGA>
      <ROM>True</ROM>
      <RWA>True</RWA>
      <SEN>True</SEN>
      <SRB>True</SRB>
      <LKA>True</LKA>
      <SYR>True</SYR>
      <TAJ>True</TAJ>
      <TKM>True</TKM>
      <ShowMessageBeforeTermination>True</ShowMessageBeforeTermination>
      <Message>
        <Title>Warning</Title>
        <Text>You have not permissions to start this application!
Please, contact to developers.</Text>
        <UseHL>False</UseHL>
        <HLSentence>Software information</HLSentence>
        <HLURL>http://www.yourcompany.com/</HLURL>
        <Icon>0</Icon>
        <AutoClose>False</AutoClose>
        <CloseDelay>0</CloseDelay>
        <Disable>False</Disable>
        <DisableDelay>0</DisableDelay>
        <FontName/>
        <FontCharset>0</FontCharset>
        <FontColor>0</FontColor>
        <FontSize>0</FontSize>
        <FontBold>False</FontBold>
        <FontItalic>False</FontItalic>
        <FontUnderline>False</FontUnderline>
        <FontStrikeout>False</FontStrikeout>
      </Message>
    </UserLanguage>
    <ExternalFiles>
      <Enabled>False</Enabled>
      <ShowMessageBeforeTermination>True</ShowMessageBeforeTermination>
      <Message>
        <Title>Warning</Title>
        <Text>The installation package had been modified! 
File: %CU_EXTFILES% is corrupted!
Please reinstall application!</Text>
        <UseHL>False</UseHL>
        <HLSentence>Enigma Protector</HLSentence>
        <HLURL>http://www.enigmaprotector.com/</HLURL>
        <Icon>0</Icon>
        <AutoClose>False</AutoClose>
        <CloseDelay>0</CloseDelay>
        <Disable>False</Disable>
        <DisableDelay>0</DisableDelay>
        <FontName/>
        <FontCharset>0</FontCharset>
        <FontColor>0</FontColor>
        <FontSize>0</FontSize>
        <FontBold>False</FontBold>
        <FontItalic>False</FontItalic>
        <FontUnderline>False</FontUnderline>
        <FontStrikeout>False</FontStrikeout>
      </Message>
      <Files/>
    </ExternalFiles>
    <ExecutedProcesses>
      <Enabled>False</Enabled>
      <ShowMessageBeforeTermination>True</ShowMessageBeforeTermination>
      <Message>
        <Title>Warning</Title>
        <Text>Spyware tool is executed on this PC. 
Please close the application %CU_EXECPR% try again!</Text>
        <UseHL>False</UseHL>
        <HLSentence>Enigma Protector</HLSentence>
        <HLURL>http://www.enigmaprotector.com/</HLURL>
        <Icon>0</Icon>
        <AutoClose>False</AutoClose>
        <CloseDelay>0</CloseDelay>
        <Disable>False</Disable>
        <DisableDelay>0</DisableDelay>
        <FontName/>
        <FontCharset>0</FontCharset>
        <FontColor>0</FontColor>
        <FontSize>0</FontSize>
        <FontBold>False</FontBold>
        <FontItalic>False</FontItalic>
        <FontUnderline>False</FontUnderline>
        <FontStrikeout>False</FontStrikeout>
      </Message>
      <CheckEndless>True</CheckEndless>
      <CheckDelay>2</CheckDelay>
      <CheckHiddenProcess>False</CheckHiddenProcess>
      <Processes Count="0"/>
    </ExecutedProcesses>
    <LoadedDrivers>
      <Enabled>False</Enabled>
      <ShowMessageBeforeTermination>False</ShowMessageBeforeTermination>
      <Message>
        <Title>Warning</Title>
        <Text>Spyware tool is executed on this PC. 
Please close this tool try again!</Text>
        <UseHL>False</UseHL>
        <HLSentence>Enigma Protector</HLSentence>
        <HLURL>http://www.enigmaprotector.com/</HLURL>
        <Icon>0</Icon>
        <AutoClose>False</AutoClose>
        <CloseDelay>0</CloseDelay>
        <Disable>False</Disable>
        <DisableDelay>0</DisableDelay>
        <FontName/>
        <FontCharset>0</FontCharset>
        <FontColor>0</FontColor>
        <FontSize>0</FontSize>
        <FontBold>False</FontBold>
        <FontItalic>False</FontItalic>
        <FontUnderline>False</FontUnderline>
        <FontStrikeout>False</FontStrikeout>
      </Message>
      <Drivers Count="0"/>
    </LoadedDrivers>
    <InstalledServices>
      <Enabled>False</Enabled>
      <ShowMessageBeforeTermination>True</ShowMessageBeforeTermination>
      <Message>
        <Title>Warning</Title>
        <Text>Spyware tool is installed on this PC. 
Uninstall service &quot;%CU_INSTSERV%&quot; and try again!</Text>
        <UseHL>False</UseHL>
        <HLSentence>Enigma Protector</HLSentence>
        <HLURL>http://www.enigmaprotector.com/</HLURL>
        <Icon>0</Icon>
        <AutoClose>False</AutoClose>
        <CloseDelay>0</CloseDelay>
        <Disable>False</Disable>
        <DisableDelay>0</DisableDelay>
        <FontName/>
        <FontCharset>0</FontCharset>
        <FontColor>0</FontColor>
        <FontSize>0</FontSize>
        <FontBold>False</FontBold>
        <FontItalic>False</FontItalic>
        <FontUnderline>False</FontUnderline>
        <FontStrikeout>False</FontStrikeout>
      </Message>
      <Services Count="0"/>
    </InstalledServices>
    <WindowsVersion>
      <Enabled>False</Enabled>
      <WIN_VER_WINDOWS95>False</WIN_VER_WINDOWS95>
      <WIN_VER_WINDOWS95OSR2>False</WIN_VER_WINDOWS95OSR2>
      <WIN_VER_WINDOWS98>False</WIN_VER_WINDOWS98>
      <WIN_VER_WINDOWS98SE>False</WIN_VER_WINDOWS98SE>
      <WIN_VER_WINDOWSME>False</WIN_VER_WINDOWSME>
      <WIN_VER_WINDOWS2000>False</WIN_VER_WINDOWS2000>
      <WIN_VER_WINDOWS2000PROF>False</WIN_VER_WINDOWS2000PROF>
      <WIN_VER_WINDOWS2000DATASERVER>False</WIN_VER_WINDOWS2000DATASERVER>
      <WIN_VER_WINDOWS2000ADVSERVER>False</WIN_VER_WINDOWS2000ADVSERVER>
      <WIN_VER_WINDOWS2000SERVER>False</WIN_VER_WINDOWS2000SERVER>
      <WIN_VER_WINDOWSXP>False</WIN_VER_WINDOWSXP>
      <WIN_VER_WINDOWSXPHOME>False</WIN_VER_WINDOWSXPHOME>
      <WIN_VER_WINDOWSXPPROF>False</WIN_VER_WINDOWSXPPROF>
      <WIN_VER_WINDOWSXPPROFx64>False</WIN_VER_WINDOWSXPPROFx64>
      <WIN_VER_WINDOWSXPPROFDATACENTERx64>False</WIN_VER_WINDOWSXPPROFDATACENTERx64>
      <WIN_VER_WINDOWSXPPROFENERPRICEx64>False</WIN_VER_WINDOWSXPPROFENERPRICEx64>
      <WIN_VER_WINDOWSXPPROFSTANDARTx64>False</WIN_VER_WINDOWSXPPROFSTANDARTx64>
      <WIN_VER_WINDOWS2003SERVER>False</WIN_VER_WINDOWS2003SERVER>
      <WIN_VER_WINDOWS2003SERVERR2>False</WIN_VER_WINDOWS2003SERVERR2>
      <WIN_VER_WINDOWS2003STORAGESERVER>False</WIN_VER_WINDOWS2003STORAGESERVER>
      <WIN_VER_WINDOWS2003DATACENTERITANIUM>False</WIN_VER_WINDOWS2003DATACENTERITANIUM>
      <WIN_VER_WINDOWS2003ENTERPRICEITANIUM>False</WIN_VER_WINDOWS2003ENTERPRICEITANIUM>
      <WIN_VER_WINDOWS2003DATACENTERx64>False</WIN_VER_WINDOWS2003DATACENTERx64>
      <WIN_VER_WINDOWS2003ENERPRICEx64>False</WIN_VER_WINDOWS2003ENERPRICEx64>
      <WIN_VER_WINDOWS2003STANDARTx64>False</WIN_VER_WINDOWS2003STANDARTx64>
      <WIN_VER_WINDOWS2003COMPUTE>False</WIN_VER_WINDOWS2003COMPUTE>
      <WIN_VER_WINDOWS2003DATACENTER>False</WIN_VER_WINDOWS2003DATACENTER>
      <WIN_VER_WINDOWS2003ENTERPRICE>False</WIN_VER_WINDOWS2003ENTERPRICE>
      <WIN_VER_WINDOWS2003WEB>False</WIN_VER_WINDOWS2003WEB>
      <WIN_VER_WINDOWS2003STANDART>False</WIN_VER_WINDOWS2003STANDART>
      <WIN_VER_WINDOWSVISTA>False</WIN_VER_WINDOWSVISTA>
      <WIN_VER_WINDOWSVISTA_BUSINESS>False</WIN_VER_WINDOWSVISTA_BUSINESS>
      <WIN_VER_WINDOWSVISTA_CLUSTER_SERVER>False</WIN_VER_WINDOWSVISTA_CLUSTER_SERVER>
      <WIN_VER_WINDOWSVISTA_DATACENTER_SERVER>False</WIN_VER_WINDOWSVISTA_DATACENTER_SERVER>
      <WIN_VER_WINDOWSVISTA_DATACENTER_SERVER_CORE>False</WIN_VER_WINDOWSVISTA_DATACENTER_SERVER_CORE>
      <WIN_VER_WINDOWSVISTA_DATACENTER_SERVER_CORE_V>False</WIN_VER_WINDOWSVISTA_DATACENTER_SERVER_CORE_V>
      <WIN_VER_WINDOWSVISTA_DATACENTER_SERVER_V>False</WIN_VER_WINDOWSVISTA_DATACENTER_SERVER_V>
      <WIN_VER_WINDOWSVISTA_ENTERPRICE>False</WIN_VER_WINDOWSVISTA_ENTERPRICE>
      <WIN_VER_WINDOWSVISTA_ENTERPRISE_SERVER>False</WIN_VER_WINDOWSVISTA_ENTERPRISE_SERVER>
      <WIN_VER_WINDOWSVISTA_ENTERPRISE_SERVER_CORE>False</WIN_VER_WINDOWSVISTA_ENTERPRISE_SERVER_CORE>
      <WIN_VER_WINDOWSVISTA_ENTERPRISE_SERVER_V>False</WIN_VER_WINDOWSVISTA_ENTERPRISE_SERVER_V>
      <WIN_VER_WINDOWSVISTA_ENTERPRISE_SERVER_CORE_V>False</WIN_VER_WINDOWSVISTA_ENTERPRISE_SERVER_CORE_V>
      <WIN_VER_WINDOWSVISTA_ENTERPRISE_SERVER_IA64>False</WIN_VER_WINDOWSVISTA_ENTERPRISE_SERVER_IA64>
      <WIN_VER_WINDOWSVISTA_HOME_BASIC>False</WIN_VER_WINDOWSVISTA_HOME_BASIC>
      <WIN_VER_WINDOWSVISTA_HOME_PREMIUM>False</WIN_VER_WINDOWSVISTA_HOME_PREMIUM>
      <WIN_VER_WINDOWSVISTA_HOME_SERVER>False</WIN_VER_WINDOWSVISTA_HOME_SERVER>
      <WIN_VER_WINDOWSVISTA_SERVER_FOR_SMALLBUSINESS>False</WIN_VER_WINDOWSVISTA_SERVER_FOR_SMALLBUSINESS>
      <WIN_VER_WINDOWSVISTA_SMALLBUSINESS_SERVER>False</WIN_VER_WINDOWSVISTA_SMALLBUSINESS_SERVER>
      <WIN_VER_WINDOWSVISTA_SMALLBUSINESS_SERVER_PREMIUM>False</WIN_VER_WINDOWSVISTA_SMALLBUSINESS_SERVER_PREMIUM>
      <WIN_VER_WINDOWSVISTA_MEDIUMBUSINESS_SERVER_MANAGEMENT>False</WIN_VER_WINDOWSVISTA_MEDIUMBUSINESS_SERVER_MANAGEMENT>
      <WIN_VER_WINDOWSVISTA_MEDIUMBUSINESS_SERVER_MESSAGING>False</WIN_VER_WINDOWSVISTA_MEDIUMBUSINESS_SERVER_MESSAGING>
      <WIN_VER_WINDOWSVISTA_MEDIUMBUSINESS_SERVER_SECURITY>False</WIN_VER_WINDOWSVISTA_MEDIUMBUSINESS_SERVER_SECURITY>
      <WIN_VER_WINDOWSVISTA_STANDARD_SERVER>False</WIN_VER_WINDOWSVISTA_STANDARD_SERVER>
      <WIN_VER_WINDOWSVISTA_STANDARD_SERVER_V>False</WIN_VER_WINDOWSVISTA_STANDARD_SERVER_V>
      <WIN_VER_WINDOWSVISTA_STANDARD_SERVER_CORE>False</WIN_VER_WINDOWSVISTA_STANDARD_SERVER_CORE>
      <WIN_VER_WINDOWSVISTA_STANDARD_SERVER_CORE_V>False</WIN_VER_WINDOWSVISTA_STANDARD_SERVER_CORE_V>
      <WIN_VER_WINDOWSVISTA_STARTER>False</WIN_VER_WINDOWSVISTA_STARTER>
      <WIN_VER_WINDOWSVISTA_STORAGE_ENTERPRISE_SERVER>False</WIN_VER_WINDOWSVISTA_STORAGE_ENTERPRISE_SERVER>
      <WIN_VER_WINDOWSVISTA_STORAGE_EXPRESS_SERVER>False</WIN_VER_WINDOWSVISTA_STORAGE_EXPRESS_SERVER>
      <WIN_VER_WINDOWSVISTA_STORAGE_STANDARD_SERVER>False</WIN_VER_WINDOWSVISTA_STORAGE_STANDARD_SERVER>
      <WIN_VER_WINDOWSVISTA_STORAGE_WORKGROUP_SERVER>False</WIN_VER_WINDOWSVISTA_STORAGE_WORKGROUP_SERVER>
      <WIN_VER_WINDOWSVISTA_UNDEFINED>False</WIN_VER_WINDOWSVISTA_UNDEFINED>
      <WIN_VER_WINDOWSVISTA_ULTIMATE>False</WIN_VER_WINDOWSVISTA_ULTIMATE>
      <WIN_VER_WINDOWSVISTA_WEB_SERVER>False</WIN_VER_WINDOWSVISTA_WEB_SERVER>
      <WIN_VER_WINDOWSVISTA_WEB_SERVER_CORE>False</WIN_VER_WINDOWSVISTA_WEB_SERVER_CORE>
      <WIN_VER_WINDOWSVISTA_UNLICENSED>False</WIN_VER_WINDOWSVISTA_UNLICENSED>
      <WIN_VER_WINDOWS2008>False</WIN_VER_WINDOWS2008>
      <WIN_VER_WINDOWS2008_BUSINESS>False</WIN_VER_WINDOWS2008_BUSINESS>
      <WIN_VER_WINDOWS2008_CLUSTER_SERVER>False</WIN_VER_WINDOWS2008_CLUSTER_SERVER>
      <WIN_VER_WINDOWS2008_DATACENTER_SERVER>False</WIN_VER_WINDOWS2008_DATACENTER_SERVER>
      <WIN_VER_WINDOWS2008_DATACENTER_SERVER_CORE>False</WIN_VER_WINDOWS2008_DATACENTER_SERVER_CORE>
      <WIN_VER_WINDOWS2008_DATACENTER_SERVER_CORE_V>False</WIN_VER_WINDOWS2008_DATACENTER_SERVER_CORE_V>
      <WIN_VER_WINDOWS2008_DATACENTER_SERVER_V>False</WIN_VER_WINDOWS2008_DATACENTER_SERVER_V>
      <WIN_VER_WINDOWS2008_ENTERPRICE>False</WIN_VER_WINDOWS2008_ENTERPRICE>
      <WIN_VER_WINDOWS2008_ENTERPRISE_SERVER>False</WIN_VER_WINDOWS2008_ENTERPRISE_SERVER>
      <WIN_VER_WINDOWS2008_ENTERPRISE_SERVER_CORE>False</WIN_VER_WINDOWS2008_ENTERPRISE_SERVER_CORE>
      <WIN_VER_WINDOWS2008_ENTERPRISE_SERVER_V>False</WIN_VER_WINDOWS2008_ENTERPRISE_SERVER_V>
      <WIN_VER_WINDOWS2008_ENTERPRISE_SERVER_CORE_V>False</WIN_VER_WINDOWS2008_ENTERPRISE_SERVER_CORE_V>
      <WIN_VER_WINDOWS2008_ENTERPRISE_SERVER_IA64>False</WIN_VER_WINDOWS2008_ENTERPRISE_SERVER_IA64>
      <WIN_VER_WINDOWS2008_HOME_BASIC>False</WIN_VER_WINDOWS2008_HOME_BASIC>
      <WIN_VER_WINDOWS2008_HOME_PREMIUM>False</WIN_VER_WINDOWS2008_HOME_PREMIUM>
      <WIN_VER_WINDOWS2008_HOME_SERVER>False</WIN_VER_WINDOWS2008_HOME_SERVER>
      <WIN_VER_WINDOWS2008_SERVER_FOR_SMALLBUSINESS>False</WIN_VER_WINDOWS2008_SERVER_FOR_SMALLBUSINESS>
      <WIN_VER_WINDOWS2008_SMALLBUSINESS_SERVER>False</WIN_VER_WINDOWS2008_SMALLBUSINESS_SERVER>
      <WIN_VER_WINDOWS2008_SMALLBUSINESS_SERVER_PREMIUM>False</WIN_VER_WINDOWS2008_SMALLBUSINESS_SERVER_PREMIUM>
      <WIN_VER_WINDOWS2008_MEDIUMBUSINESS_SERVER_MANAGEMENT>False</WIN_VER_WINDOWS2008_MEDIUMBUSINESS_SERVER_MANAGEMENT>
      <WIN_VER_WINDOWS2008_MEDIUMBUSINESS_SERVER_MESSAGING>False</WIN_VER_WINDOWS2008_MEDIUMBUSINESS_SERVER_MESSAGING>
      <WIN_VER_WINDOWS2008_MEDIUMBUSINESS_SERVER_SECURITY>False</WIN_VER_WINDOWS2008_MEDIUMBUSINESS_SERVER_SECURITY>
      <WIN_VER_WINDOWS2008_STANDARD_SERVER>False</WIN_VER_WINDOWS2008_STANDARD_SERVER>
      <WIN_VER_WINDOWS2008_STANDARD_SERVER_V>False</WIN_VER_WINDOWS2008_STANDARD_SERVER_V>
      <WIN_VER_WINDOWS2008_STANDARD_SERVER_CORE>False</WIN_VER_WINDOWS2008_STANDARD_SERVER_CORE>
      <WIN_VER_WINDOWS2008_STANDARD_SERVER_CORE_V>False</WIN_VER_WINDOWS2008_STANDARD_SERVER_CORE_V>
      <WIN_VER_WINDOWS2008_STARTER>False</WIN_VER_WINDOWS2008_STARTER>
      <WIN_VER_WINDOWS2008_STORAGE_ENTERPRISE_SERVER>False</WIN_VER_WINDOWS2008_STORAGE_ENTERPRISE_SERVER>
      <WIN_VER_WINDOWS2008_STORAGE_EXPRESS_SERVER>False</WIN_VER_WINDOWS2008_STORAGE_EXPRESS_SERVER>
      <WIN_VER_WINDOWS2008_STORAGE_STANDARD_SERVER>False</WIN_VER_WINDOWS2008_STORAGE_STANDARD_SERVER>
      <WIN_VER_WINDOWS2008_STORAGE_WORKGROUP_SERVER>False</WIN_VER_WINDOWS2008_STORAGE_WORKGROUP_SERVER>
      <WIN_VER_WINDOWS2008_UNDEFINED>False</WIN_VER_WINDOWS2008_UNDEFINED>
      <WIN_VER_WINDOWS2008_ULTIMATE>False</WIN_VER_WINDOWS2008_ULTIMATE>
      <WIN_VER_WINDOWS2008_WEB_SERVER>False</WIN_VER_WINDOWS2008_WEB_SERVER>
      <WIN_VER_WINDOWS2008_WEB_SERVER_CORE>False</WIN_VER_WINDOWS2008_WEB_SERVER_CORE>
      <WIN_VER_WINDOWS2008_UNLICENSED>False</WIN_VER_WINDOWS2008_UNLICENSED>
      <WIN_VER_WINDOWS2008R2>False</WIN_VER_WINDOWS2008R2>
      <WIN_VER_WINDOWS2008R2_BUSINESS>False</WIN_VER_WINDOWS2008R2_BUSINESS>
      <WIN_VER_WINDOWS2008R2_CLUSTER_SERVER>False</WIN_VER_WINDOWS2008R2_CLUSTER_SERVER>
      <WIN_VER_WINDOWS2008R2_DATACENTER_SERVER>False</WIN_VER_WINDOWS2008R2_DATACENTER_SERVER>
      <WIN_VER_WINDOWS2008R2_DATACENTER_SERVER_CORE>False</WIN_VER_WINDOWS2008R2_DATACENTER_SERVER_CORE>
      <WIN_VER_WINDOWS2008R2_DATACENTER_SERVER_CORE_V>False</WIN_VER_WINDOWS2008R2_DATACENTER_SERVER_CORE_V>
      <WIN_VER_WINDOWS2008R2_DATACENTER_SERVER_V>False</WIN_VER_WINDOWS2008R2_DATACENTER_SERVER_V>
      <WIN_VER_WINDOWS2008R2_ENTERPRICE>False</WIN_VER_WINDOWS2008R2_ENTERPRICE>
      <WIN_VER_WINDOWS2008R2_ENTERPRISE_SERVER>False</WIN_VER_WINDOWS2008R2_ENTERPRISE_SERVER>
      <WIN_VER_WINDOWS2008R2_ENTERPRISE_SERVER_CORE>False</WIN_VER_WINDOWS2008R2_ENTERPRISE_SERVER_CORE>
      <WIN_VER_WINDOWS2008R2_ENTERPRISE_SERVER_V>False</WIN_VER_WINDOWS2008R2_ENTERPRISE_SERVER_V>
      <WIN_VER_WINDOWS2008R2_ENTERPRISE_SERVER_CORE_V>False</WIN_VER_WINDOWS2008R2_ENTERPRISE_SERVER_CORE_V>
      <WIN_VER_WINDOWS2008R2_ENTERPRISE_SERVER_IA64>False</WIN_VER_WINDOWS2008R2_ENTERPRISE_SERVER_IA64>
      <WIN_VER_WINDOWS2008R2_HOME_BASIC>False</WIN_VER_WINDOWS2008R2_HOME_BASIC>
      <WIN_VER_WINDOWS2008R2_HOME_PREMIUM>False</WIN_VER_WINDOWS2008R2_HOME_PREMIUM>
      <WIN_VER_WINDOWS2008R2_HOME_SERVER>False</WIN_VER_WINDOWS2008R2_HOME_SERVER>
      <WIN_VER_WINDOWS2008R2_SERVER_FOR_SMALLBUSINESS>False</WIN_VER_WINDOWS2008R2_SERVER_FOR_SMALLBUSINESS>
      <WIN_VER_WINDOWS2008R2_SMALLBUSINESS_SERVER>False</WIN_VER_WINDOWS2008R2_SMALLBUSINESS_SERVER>
      <WIN_VER_WINDOWS2008R2_SMALLBUSINESS_SERVER_PREMIUM>False</WIN_VER_WINDOWS2008R2_SMALLBUSINESS_SERVER_PREMIUM>
      <WIN_VER_WINDOWS2008R2_MEDIUMBUSINESS_SERVER_MANAGEMENT>False</WIN_VER_WINDOWS2008R2_MEDIUMBUSINESS_SERVER_MANAGEMENT>
      <WIN_VER_WINDOWS2008R2_MEDIUMBUSINESS_SERVER_MESSAGING>False</WIN_VER_WINDOWS2008R2_MEDIUMBUSINESS_SERVER_MESSAGING>
      <WIN_VER_WINDOWS2008R2_MEDIUMBUSINESS_SERVER_SECURITY>False</WIN_VER_WINDOWS2008R2_MEDIUMBUSINESS_SERVER_SECURITY>
      <WIN_VER_WINDOWS2008R2_STANDARD_SERVER>False</WIN_VER_WINDOWS2008R2_STANDARD_SERVER>
      <WIN_VER_WINDOWS2008R2_STANDARD_SERVER_V>False</WIN_VER_WINDOWS2008R2_STANDARD_SERVER_V>
      <WIN_VER_WINDOWS2008R2_STANDARD_SERVER_CORE>False</WIN_VER_WINDOWS2008R2_STANDARD_SERVER_CORE>
      <WIN_VER_WINDOWS2008R2_STANDARD_SERVER_CORE_V>False</WIN_VER_WINDOWS2008R2_STANDARD_SERVER_CORE_V>
      <WIN_VER_WINDOWS2008R2_STARTER>False</WIN_VER_WINDOWS2008R2_STARTER>
      <WIN_VER_WINDOWS2008R2_STORAGE_ENTERPRISE_SERVER>False</WIN_VER_WINDOWS2008R2_STORAGE_ENTERPRISE_SERVER>
      <WIN_VER_WINDOWS2008R2_STORAGE_EXPRESS_SERVER>False</WIN_VER_WINDOWS2008R2_STORAGE_EXPRESS_SERVER>
      <WIN_VER_WINDOWS2008R2_STORAGE_STANDARD_SERVER>False</WIN_VER_WINDOWS2008R2_STORAGE_STANDARD_SERVER>
      <WIN_VER_WINDOWS2008R2_STORAGE_WORKGROUP_SERVER>False</WIN_VER_WINDOWS2008R2_STORAGE_WORKGROUP_SERVER>
      <WIN_VER_WINDOWS2008R2_UNDEFINED>False</WIN_VER_WINDOWS2008R2_UNDEFINED>
      <WIN_VER_WINDOWS2008R2_ULTIMATE>False</WIN_VER_WINDOWS2008R2_ULTIMATE>
      <WIN_VER_WINDOWS2008R2_WEB_SERVER>False</WIN_VER_WINDOWS2008R2_WEB_SERVER>
      <WIN_VER_WINDOWS2008R2_WEB_SERVER_CORE>False</WIN_VER_WINDOWS2008R2_WEB_SERVER_CORE>
      <WIN_VER_WINDOWS2008R2_UNLICENSED>False</WIN_VER_WINDOWS2008R2_UNLICENSED>
      <WIN_VER_WINDOWSSEVEN>False</WIN_VER_WINDOWSSEVEN>
      <WIN_VER_WINDOWSSEVEN_BUSINESS>False</WIN_VER_WINDOWSSEVEN_BUSINESS>
      <WIN_VER_WINDOWSSEVEN_CLUSTER_SERVER>False</WIN_VER_WINDOWSSEVEN_CLUSTER_SERVER>
      <WIN_VER_WINDOWSSEVEN_DATACENTER_SERVER>False</WIN_VER_WINDOWSSEVEN_DATACENTER_SERVER>
      <WIN_VER_WINDOWSSEVEN_DATACENTER_SERVER_CORE>False</WIN_VER_WINDOWSSEVEN_DATACENTER_SERVER_CORE>
      <WIN_VER_WINDOWSSEVEN_DATACENTER_SERVER_CORE_V>False</WIN_VER_WINDOWSSEVEN_DATACENTER_SERVER_CORE_V>
      <WIN_VER_WINDOWSSEVEN_DATACENTER_SERVER_V>False</WIN_VER_WINDOWSSEVEN_DATACENTER_SERVER_V>
      <WIN_VER_WINDOWSSEVEN_ENTERPRICE>False</WIN_VER_WINDOWSSEVEN_ENTERPRICE>
      <WIN_VER_WINDOWSSEVEN_ENTERPRISE_SERVER>False</WIN_VER_WINDOWSSEVEN_ENTERPRISE_SERVER>
      <WIN_VER_WINDOWSSEVEN_ENTERPRISE_SERVER_CORE>False</WIN_VER_WINDOWSSEVEN_ENTERPRISE_SERVER_CORE>
      <WIN_VER_WINDOWSSEVEN_ENTERPRISE_SERVER_V>False</WIN_VER_WINDOWSSEVEN_ENTERPRISE_SERVER_V>
      <WIN_VER_WINDOWSSEVEN_ENTERPRISE_SERVER_CORE_V>False</WIN_VER_WINDOWSSEVEN_ENTERPRISE_SERVER_CORE_V>
      <WIN_VER_WINDOWSSEVEN_ENTERPRISE_SERVER_IA64>False</WIN_VER_WINDOWSSEVEN_ENTERPRISE_SERVER_IA64>
      <WIN_VER_WINDOWSSEVEN_HOME_BASIC>False</WIN_VER_WINDOWSSEVEN_HOME_BASIC>
      <WIN_VER_WINDOWSSEVEN_HOME_PREMIUM>False</WIN_VER_WINDOWSSEVEN_HOME_PREMIUM>
      <WIN_VER_WINDOWSSEVEN_HOME_SERVER>False</WIN_VER_WINDOWSSEVEN_HOME_SERVER>
      <WIN_VER_WINDOWSSEVEN_SERVER_FOR_SMALLBUSINESS>False</WIN_VER_WINDOWSSEVEN_SERVER_FOR_SMALLBUSINESS>
      <WIN_VER_WINDOWSSEVEN_SMALLBUSINESS_SERVER>False</WIN_VER_WINDOWSSEVEN_SMALLBUSINESS_SERVER>
      <WIN_VER_WINDOWSSEVEN_SMALLBUSINESS_SERVER_PREMIUM>False</WIN_VER_WINDOWSSEVEN_SMALLBUSINESS_SERVER_PREMIUM>
      <WIN_VER_WINDOWSSEVEN_MEDIUMBUSINESS_SERVER_MANAGEMENT>False</WIN_VER_WINDOWSSEVEN_MEDIUMBUSINESS_SERVER_MANAGEMENT>
      <WIN_VER_WINDOWSSEVEN_MEDIUMBUSINESS_SERVER_MESSAGING>False</WIN_VER_WINDOWSSEVEN_MEDIUMBUSINESS_SERVER_MESSAGING>
      <WIN_VER_WINDOWSSEVEN_MEDIUMBUSINESS_SERVER_SECURITY>False</WIN_VER_WINDOWSSEVEN_MEDIUMBUSINESS_SERVER_SECURITY>
      <WIN_VER_WINDOWSSEVEN_STANDARD_SERVER>False</WIN_VER_WINDOWSSEVEN_STANDARD_SERVER>
      <WIN_VER_WINDOWSSEVEN_STANDARD_SERVER_V>False</WIN_VER_WINDOWSSEVEN_STANDARD_SERVER_V>
      <WIN_VER_WINDOWSSEVEN_STANDARD_SERVER_CORE>False</WIN_VER_WINDOWSSEVEN_STANDARD_SERVER_CORE>
      <WIN_VER_WINDOWSSEVEN_STANDARD_SERVER_CORE_V>False</WIN_VER_WINDOWSSEVEN_STANDARD_SERVER_CORE_V>
      <WIN_VER_WINDOWSSEVEN_STARTER>False</WIN_VER_WINDOWSSEVEN_STARTER>
      <WIN_VER_WINDOWSSEVEN_STORAGE_ENTERPRISE_SERVER>False</WIN_VER_WINDOWSSEVEN_STORAGE_ENTERPRISE_SERVER>
      <WIN_VER_WINDOWSSEVEN_STORAGE_EXPRESS_SERVER>False</WIN_VER_WINDOWSSEVEN_STORAGE_EXPRESS_SERVER>
      <WIN_VER_WINDOWSSEVEN_STORAGE_STANDARD_SERVER>False</WIN_VER_WINDOWSSEVEN_STORAGE_STANDARD_SERVER>
      <WIN_VER_WINDOWSSEVEN_STORAGE_WORKGROUP_SERVER>False</WIN_VER_WINDOWSSEVEN_STORAGE_WORKGROUP_SERVER>
      <WIN_VER_WINDOWSSEVEN_UNDEFINED>False</WIN_VER_WINDOWSSEVEN_UNDEFINED>
      <WIN_VER_WINDOWSSEVEN_ULTIMATE>False</WIN_VER_WINDOWSSEVEN_ULTIMATE>
      <WIN_VER_WINDOWSSEVEN_WEB_SERVER>False</WIN_VER_WINDOWSSEVEN_WEB_SERVER>
      <WIN_VER_WINDOWSSEVEN_WEB_SERVER_CORE>False</WIN_VER_WINDOWSSEVEN_WEB_SERVER_CORE>
      <WIN_VER_WINDOWSSEVEN_UNLICENSED>False</WIN_VER_WINDOWSSEVEN_UNLICENSED>
      <ShowMessageBeforeTermination>True</ShowMessageBeforeTermination>
      <Message>
        <Title>Warning</Title>
        <Text>Current Windows Version &quot;%CU_WINVER%&quot; does not support!</Text>
        <UseHL>False</UseHL>
        <HLSentence>Enigma Protector</HLSentence>
        <HLURL>http://www.enigmaprotector.com/</HLURL>
        <Icon>0</Icon>
        <AutoClose>False</AutoClose>
        <CloseDelay>0</CloseDelay>
        <Disable>True</Disable>
        <DisableDelay>10</DisableDelay>
        <FontName/>
        <FontCharset>0</FontCharset>
        <FontColor>0</FontColor>
        <FontSize>0</FontSize>
        <FontBold>False</FontBold>
        <FontItalic>False</FontItalic>
        <FontUnderline>False</FontUnderline>
        <FontStrikeout>False</FontStrikeout>
      </Message>
      <WIN_VER_WINDOWS8>False</WIN_VER_WINDOWS8>
      <WIN_VER_WINDOWS8_UNLICENSED>False</WIN_VER_WINDOWS8_UNLICENSED>
      <WIN_VER_WINDOWS8_BUSINESS>False</WIN_VER_WINDOWS8_BUSINESS>
      <WIN_VER_WINDOWS8_CLUSTER_SERVER>False</WIN_VER_WINDOWS8_CLUSTER_SERVER>
      <WIN_VER_WINDOWS8_CLUSTER_SERVER_V>False</WIN_VER_WINDOWS8_CLUSTER_SERVER_V>
      <WIN_VER_WINDOWS8_CORE>False</WIN_VER_WINDOWS8_CORE>
      <WIN_VER_WINDOWS8_CORE_COUNTRYSPECIFIC>False</WIN_VER_WINDOWS8_CORE_COUNTRYSPECIFIC>
      <WIN_VER_WINDOWS8_CORE_SINGLELANGUAGE>False</WIN_VER_WINDOWS8_CORE_SINGLELANGUAGE>
      <WIN_VER_WINDOWS8_DATACENTER_EVALUATION_SERVER>False</WIN_VER_WINDOWS8_DATACENTER_EVALUATION_SERVER>
      <WIN_VER_WINDOWS8_DATACENTER_SERVER>False</WIN_VER_WINDOWS8_DATACENTER_SERVER>
      <WIN_VER_WINDOWS8_DATACENTER_SERVER_CORE>False</WIN_VER_WINDOWS8_DATACENTER_SERVER_CORE>
      <WIN_VER_WINDOWS8_DATACENTER_SERVER_CORE_V>False</WIN_VER_WINDOWS8_DATACENTER_SERVER_CORE_V>
      <WIN_VER_WINDOWS8_DATACENTER_SERVER_V>False</WIN_VER_WINDOWS8_DATACENTER_SERVER_V>
      <WIN_VER_WINDOWS8_ENTERPRISE>False</WIN_VER_WINDOWS8_ENTERPRISE>
      <WIN_VER_WINDOWS8_ENTERPRISE_N_EVALUATION>False</WIN_VER_WINDOWS8_ENTERPRISE_N_EVALUATION>
      <WIN_VER_WINDOWS8_ENTERPRISE_EVALUATION>False</WIN_VER_WINDOWS8_ENTERPRISE_EVALUATION>
      <WIN_VER_WINDOWS8_ENTERPRISE_SERVER>False</WIN_VER_WINDOWS8_ENTERPRISE_SERVER>
      <WIN_VER_WINDOWS8_ENTERPRISE_SERVER_CORE>False</WIN_VER_WINDOWS8_ENTERPRISE_SERVER_CORE>
      <WIN_VER_WINDOWS8_ENTERPRISE_SERVER_CORE_V>False</WIN_VER_WINDOWS8_ENTERPRISE_SERVER_CORE_V>
      <WIN_VER_WINDOWS8_ENTERPRISE_SERVER_IA64>False</WIN_VER_WINDOWS8_ENTERPRISE_SERVER_IA64>
      <WIN_VER_WINDOWS8_ENTERPRISE_SERVER_V>False</WIN_VER_WINDOWS8_ENTERPRISE_SERVER_V>
      <WIN_VER_WINDOWS8_ESSENTIALBUSINESS_SERVER_MGMT>False</WIN_VER_WINDOWS8_ESSENTIALBUSINESS_SERVER_MGMT>
      <WIN_VER_WINDOWS8_ESSENTIALBUSINESS_SERVER_ADDL>False</WIN_VER_WINDOWS8_ESSENTIALBUSINESS_SERVER_ADDL>
      <WIN_VER_WINDOWS8_ESSENTIALBUSINESS_SERVER_MGMTSVC>False</WIN_VER_WINDOWS8_ESSENTIALBUSINESS_SERVER_MGMTSVC>
      <WIN_VER_WINDOWS8_ESSENTIALBUSINESS_SERVER_ADDLSVC>False</WIN_VER_WINDOWS8_ESSENTIALBUSINESS_SERVER_ADDLSVC>
      <WIN_VER_WINDOWS8_HOME_BASIC>False</WIN_VER_WINDOWS8_HOME_BASIC>
      <WIN_VER_WINDOWS8_HOME_PREMIUM>False</WIN_VER_WINDOWS8_HOME_PREMIUM>
      <WIN_VER_WINDOWS8_HOME_PREMIUM_SERVER>False</WIN_VER_WINDOWS8_HOME_PREMIUM_SERVER>
      <WIN_VER_WINDOWS8_HOME_SERVER>False</WIN_VER_WINDOWS8_HOME_SERVER>
      <WIN_VER_WINDOWS8_HYPERV>False</WIN_VER_WINDOWS8_HYPERV>
      <WIN_VER_WINDOWS8_MEDIUMBUSINESS_SERVER_MANAGEMENT>False</WIN_VER_WINDOWS8_MEDIUMBUSINESS_SERVER_MANAGEMENT>
      <WIN_VER_WINDOWS8_MEDIUMBUSINESS_SERVER_MESSAGING>False</WIN_VER_WINDOWS8_MEDIUMBUSINESS_SERVER_MESSAGING>
      <WIN_VER_WINDOWS8_MEDIUMBUSINESS_SERVER_SECURITY>False</WIN_VER_WINDOWS8_MEDIUMBUSINESS_SERVER_SECURITY>
      <WIN_VER_WINDOWS8_MULTIPOINT_STANDARD_SERVER>False</WIN_VER_WINDOWS8_MULTIPOINT_STANDARD_SERVER>
      <WIN_VER_WINDOWS8_MULTIPOINT_PREMIUM_SERVER>False</WIN_VER_WINDOWS8_MULTIPOINT_PREMIUM_SERVER>
      <WIN_VER_WINDOWS8_PROFESSIONAL>False</WIN_VER_WINDOWS8_PROFESSIONAL>
      <WIN_VER_WINDOWS8_PROFESSIONAL_WMC>False</WIN_VER_WINDOWS8_PROFESSIONAL_WMC>
      <WIN_VER_WINDOWS8_SB_SOLUTION_SERVER_EM>False</WIN_VER_WINDOWS8_SB_SOLUTION_SERVER_EM>
      <WIN_VER_WINDOWS8_SERVER_FOR_SB_SOLUTIONS>False</WIN_VER_WINDOWS8_SERVER_FOR_SB_SOLUTIONS>
      <WIN_VER_WINDOWS8_SERVER_FOR_SB_SOLUTIONS_EM>False</WIN_VER_WINDOWS8_SERVER_FOR_SB_SOLUTIONS_EM>
      <WIN_VER_WINDOWS8_SERVER_FOR_SMALLBUSINESS>False</WIN_VER_WINDOWS8_SERVER_FOR_SMALLBUSINESS>
      <WIN_VER_WINDOWS8_SERVER_FOR_SMALLBUSINESS_V>False</WIN_VER_WINDOWS8_SERVER_FOR_SMALLBUSINESS_V>
      <WIN_VER_WINDOWS8_SERVER_FOUNDATION>False</WIN_VER_WINDOWS8_SERVER_FOUNDATION>
      <WIN_VER_WINDOWS8_SB_SOLUTION_SERVER>False</WIN_VER_WINDOWS8_SB_SOLUTION_SERVER>
      <WIN_VER_WINDOWS8_SMALLBUSINESS_SERVER>False</WIN_VER_WINDOWS8_SMALLBUSINESS_SERVER>
      <WIN_VER_WINDOWS8_SMALLBUSINESS_SERVER_PREMIUM>False</WIN_VER_WINDOWS8_SMALLBUSINESS_SERVER_PREMIUM>
      <WIN_VER_WINDOWS8_SMALLBUSINESS_SERVER_PREMIUM_CORE>False</WIN_VER_WINDOWS8_SMALLBUSINESS_SERVER_PREMIUM_CORE>
      <WIN_VER_WINDOWS8_SOLUTION_EMBEDDEDSERVER>False</WIN_VER_WINDOWS8_SOLUTION_EMBEDDEDSERVER>
      <WIN_VER_WINDOWS8_STANDARD_EVALUATION_SERVER>False</WIN_VER_WINDOWS8_STANDARD_EVALUATION_SERVER>
      <WIN_VER_WINDOWS8_STANDARD_SERVER>False</WIN_VER_WINDOWS8_STANDARD_SERVER>
      <WIN_VER_WINDOWS8_STANDARD_SERVER_CORE>False</WIN_VER_WINDOWS8_STANDARD_SERVER_CORE>
      <WIN_VER_WINDOWS8_STANDARD_SERVER_V>False</WIN_VER_WINDOWS8_STANDARD_SERVER_V>
      <WIN_VER_WINDOWS8_STANDARD_SERVER_CORE_V>False</WIN_VER_WINDOWS8_STANDARD_SERVER_CORE_V>
      <WIN_VER_WINDOWS8_STANDARD_SERVER_SOLUTIONS>False</WIN_VER_WINDOWS8_STANDARD_SERVER_SOLUTIONS>
      <WIN_VER_WINDOWS8_STANDARD_SERVER_SOLUTIONS_CORE>False</WIN_VER_WINDOWS8_STANDARD_SERVER_SOLUTIONS_CORE>
      <WIN_VER_WINDOWS8_STARTER>False</WIN_VER_WINDOWS8_STARTER>
      <WIN_VER_WINDOWS8_STORAGE_ENTERPRISE_SERVER>False</WIN_VER_WINDOWS8_STORAGE_ENTERPRISE_SERVER>
      <WIN_VER_WINDOWS8_STORAGE_ENTERPRISE_SERVER_CORE>False</WIN_VER_WINDOWS8_STORAGE_ENTERPRISE_SERVER_CORE>
      <WIN_VER_WINDOWS8_STORAGE_EXPRESS_SERVER>False</WIN_VER_WINDOWS8_STORAGE_EXPRESS_SERVER>
      <WIN_VER_WINDOWS8_STORAGE_EXPRESS_SERVER_CORE>False</WIN_VER_WINDOWS8_STORAGE_EXPRESS_SERVER_CORE>
      <WIN_VER_WINDOWS8_STORAGE_STANDARD_EVALUATION_SERVER>False</WIN_VER_WINDOWS8_STORAGE_STANDARD_EVALUATION_SERVER>
      <WIN_VER_WINDOWS8_STORAGE_STANDARD_SERVER>False</WIN_VER_WINDOWS8_STORAGE_STANDARD_SERVER>
      <WIN_VER_WINDOWS8_STORAGE_STANDARD_SERVER_CORE>False</WIN_VER_WINDOWS8_STORAGE_STANDARD_SERVER_CORE>
      <WIN_VER_WINDOWS8_STORAGE_WORKGROUP_EVALUATION_SERVER>False</WIN_VER_WINDOWS8_STORAGE_WORKGROUP_EVALUATION_SERVER>
      <WIN_VER_WINDOWS8_STORAGE_WORKGROUP_SERVER>False</WIN_VER_WINDOWS8_STORAGE_WORKGROUP_SERVER>
      <WIN_VER_WINDOWS8_STORAGE_WORKGROUP_SERVER_CORE>False</WIN_VER_WINDOWS8_STORAGE_WORKGROUP_SERVER_CORE>
      <WIN_VER_WINDOWS8_UNDEFINED>False</WIN_VER_WINDOWS8_UNDEFINED>
      <WIN_VER_WINDOWS8_ULTIMATE>False</WIN_VER_WINDOWS8_ULTIMATE>
      <WIN_VER_WINDOWS8_WEB_SERVER>False</WIN_VER_WINDOWS8_WEB_SERVER>
      <WIN_VER_WINDOWS8_WEB_SERVER_CORE>False</WIN_VER_WINDOWS8_WEB_SERVER_CORE>
      <WIN_VER_WINDOWS2012>False</WIN_VER_WINDOWS2012>
      <WIN_VER_WINDOWS2012_UNLICENSED>False</WIN_VER_WINDOWS2012_UNLICENSED>
      <WIN_VER_WINDOWS2012_BUSINESS>False</WIN_VER_WINDOWS2012_BUSINESS>
      <WIN_VER_WINDOWS2012_CLUSTER_SERVER>False</WIN_VER_WINDOWS2012_CLUSTER_SERVER>
      <WIN_VER_WINDOWS2012_CLUSTER_SERVER_V>False</WIN_VER_WINDOWS2012_CLUSTER_SERVER_V>
      <WIN_VER_WINDOWS2012_CORE>False</WIN_VER_WINDOWS2012_CORE>
      <WIN_VER_WINDOWS2012_CORE_COUNTRYSPECIFIC>False</WIN_VER_WINDOWS2012_CORE_COUNTRYSPECIFIC>
      <WIN_VER_WINDOWS2012_CORE_SINGLELANGUAGE>False</WIN_VER_WINDOWS2012_CORE_SINGLELANGUAGE>
      <WIN_VER_WINDOWS2012_DATACENTER_EVALUATION_SERVER>False</WIN_VER_WINDOWS2012_DATACENTER_EVALUATION_SERVER>
      <WIN_VER_WINDOWS2012_DATACENTER_SERVER>False</WIN_VER_WINDOWS2012_DATACENTER_SERVER>
      <WIN_VER_WINDOWS2012_DATACENTER_SERVER_CORE>False</WIN_VER_WINDOWS2012_DATACENTER_SERVER_CORE>
      <WIN_VER_WINDOWS2012_DATACENTER_SERVER_CORE_V>False</WIN_VER_WINDOWS2012_DATACENTER_SERVER_CORE_V>
      <WIN_VER_WINDOWS2012_DATACENTER_SERVER_V>False</WIN_VER_WINDOWS2012_DATACENTER_SERVER_V>
      <WIN_VER_WINDOWS2012_ENTERPRISE>False</WIN_VER_WINDOWS2012_ENTERPRISE>
      <WIN_VER_WINDOWS2012_ENTERPRISE_N_EVALUATION>False</WIN_VER_WINDOWS2012_ENTERPRISE_N_EVALUATION>
      <WIN_VER_WINDOWS2012_ENTERPRISE_EVALUATION>False</WIN_VER_WINDOWS2012_ENTERPRISE_EVALUATION>
      <WIN_VER_WINDOWS2012_ENTERPRISE_SERVER>False</WIN_VER_WINDOWS2012_ENTERPRISE_SERVER>
      <WIN_VER_WINDOWS2012_ENTERPRISE_SERVER_CORE>False</WIN_VER_WINDOWS2012_ENTERPRISE_SERVER_CORE>
      <WIN_VER_WINDOWS2012_ENTERPRISE_SERVER_CORE_V>False</WIN_VER_WINDOWS2012_ENTERPRISE_SERVER_CORE_V>
      <WIN_VER_WINDOWS2012_ENTERPRISE_SERVER_IA64>False</WIN_VER_WINDOWS2012_ENTERPRISE_SERVER_IA64>
      <WIN_VER_WINDOWS2012_ENTERPRISE_SERVER_V>False</WIN_VER_WINDOWS2012_ENTERPRISE_SERVER_V>
      <WIN_VER_WINDOWS2012_ESSENTIALBUSINESS_SERVER_MGMT>False</WIN_VER_WINDOWS2012_ESSENTIALBUSINESS_SERVER_MGMT>
      <WIN_VER_WINDOWS2012_ESSENTIALBUSINESS_SERVER_ADDL>False</WIN_VER_WINDOWS2012_ESSENTIALBUSINESS_SERVER_ADDL>
      <WIN_VER_WINDOWS2012_ESSENTIALBUSINESS_SERVER_MGMTSVC>False</WIN_VER_WINDOWS2012_ESSENTIALBUSINESS_SERVER_MGMTSVC>
      <WIN_VER_WINDOWS2012_ESSENTIALBUSINESS_SERVER_ADDLSVC>False</WIN_VER_WINDOWS2012_ESSENTIALBUSINESS_SERVER_ADDLSVC>
      <WIN_VER_WINDOWS2012_HOME_BASIC>False</WIN_VER_WINDOWS2012_HOME_BASIC>
      <WIN_VER_WINDOWS2012_HOME_PREMIUM>False</WIN_VER_WINDOWS2012_HOME_PREMIUM>
      <WIN_VER_WINDOWS2012_HOME_PREMIUM_SERVER>False</WIN_VER_WINDOWS2012_HOME_PREMIUM_SERVER>
      <WIN_VER_WINDOWS2012_HOME_SERVER>False</WIN_VER_WINDOWS2012_HOME_SERVER>
      <WIN_VER_WINDOWS2012_HYPERV>False</WIN_VER_WINDOWS2012_HYPERV>
      <WIN_VER_WINDOWS2012_MEDIUMBUSINESS_SERVER_MANAGEMENT>False</WIN_VER_WINDOWS2012_MEDIUMBUSINESS_SERVER_MANAGEMENT>
      <WIN_VER_WINDOWS2012_MEDIUMBUSINESS_SERVER_MESSAGING>False</WIN_VER_WINDOWS2012_MEDIUMBUSINESS_SERVER_MESSAGING>
      <WIN_VER_WINDOWS2012_MEDIUMBUSINESS_SERVER_SECURITY>False</WIN_VER_WINDOWS2012_MEDIUMBUSINESS_SERVER_SECURITY>
      <WIN_VER_WINDOWS2012_MULTIPOINT_STANDARD_SERVER>False</WIN_VER_WINDOWS2012_MULTIPOINT_STANDARD_SERVER>
      <WIN_VER_WINDOWS2012_MULTIPOINT_PREMIUM_SERVER>False</WIN_VER_WINDOWS2012_MULTIPOINT_PREMIUM_SERVER>
      <WIN_VER_WINDOWS2012_PROFESSIONAL>False</WIN_VER_WINDOWS2012_PROFESSIONAL>
      <WIN_VER_WINDOWS2012_PROFESSIONAL_WMC>False</WIN_VER_WINDOWS2012_PROFESSIONAL_WMC>
      <WIN_VER_WINDOWS2012_SB_SOLUTION_SERVER_EM>False</WIN_VER_WINDOWS2012_SB_SOLUTION_SERVER_EM>
      <WIN_VER_WINDOWS2012_SERVER_FOR_SB_SOLUTIONS>False</WIN_VER_WINDOWS2012_SERVER_FOR_SB_SOLUTIONS>
      <WIN_VER_WINDOWS2012_SERVER_FOR_SB_SOLUTIONS_EM>False</WIN_VER_WINDOWS2012_SERVER_FOR_SB_SOLUTIONS_EM>
      <WIN_VER_WINDOWS2012_SERVER_FOR_SMALLBUSINESS>False</WIN_VER_WINDOWS2012_SERVER_FOR_SMALLBUSINESS>
      <WIN_VER_WINDOWS2012_SERVER_FOR_SMALLBUSINESS_V>False</WIN_VER_WINDOWS2012_SERVER_FOR_SMALLBUSINESS_V>
      <WIN_VER_WINDOWS2012_SERVER_FOUNDATION>False</WIN_VER_WINDOWS2012_SERVER_FOUNDATION>
      <WIN_VER_WINDOWS2012_SB_SOLUTION_SERVER>False</WIN_VER_WINDOWS2012_SB_SOLUTION_SERVER>
      <WIN_VER_WINDOWS2012_SMALLBUSINESS_SERVER>False</WIN_VER_WINDOWS2012_SMALLBUSINESS_SERVER>
      <WIN_VER_WINDOWS2012_SMALLBUSINESS_SERVER_PREMIUM>False</WIN_VER_WINDOWS2012_SMALLBUSINESS_SERVER_PREMIUM>
      <WIN_VER_WINDOWS2012_SMALLBUSINESS_SERVER_PREMIUM_CORE>False</WIN_VER_WINDOWS2012_SMALLBUSINESS_SERVER_PREMIUM_CORE>
      <WIN_VER_WINDOWS2012_SOLUTION_EMBEDDEDSERVER>False</WIN_VER_WINDOWS2012_SOLUTION_EMBEDDEDSERVER>
      <WIN_VER_WINDOWS2012_STANDARD_EVALUATION_SERVER>False</WIN_VER_WINDOWS2012_STANDARD_EVALUATION_SERVER>
      <WIN_VER_WINDOWS2012_STANDARD_SERVER>False</WIN_VER_WINDOWS2012_STANDARD_SERVER>
      <WIN_VER_WINDOWS2012_STANDARD_SERVER_CORE>False</WIN_VER_WINDOWS2012_STANDARD_SERVER_CORE>
      <WIN_VER_WINDOWS2012_STANDARD_SERVER_V>False</WIN_VER_WINDOWS2012_STANDARD_SERVER_V>
      <WIN_VER_WINDOWS2012_STANDARD_SERVER_CORE_V>False</WIN_VER_WINDOWS2012_STANDARD_SERVER_CORE_V>
      <WIN_VER_WINDOWS2012_STANDARD_SERVER_SOLUTIONS>False</WIN_VER_WINDOWS2012_STANDARD_SERVER_SOLUTIONS>
      <WIN_VER_WINDOWS2012_STANDARD_SERVER_SOLUTIONS_CORE>False</WIN_VER_WINDOWS2012_STANDARD_SERVER_SOLUTIONS_CORE>
      <WIN_VER_WINDOWS2012_STARTER>False</WIN_VER_WINDOWS2012_STARTER>
      <WIN_VER_WINDOWS2012_STORAGE_ENTERPRISE_SERVER>False</WIN_VER_WINDOWS2012_STORAGE_ENTERPRISE_SERVER>
      <WIN_VER_WINDOWS2012_STORAGE_ENTERPRISE_SERVER_CORE>False</WIN_VER_WINDOWS2012_STORAGE_ENTERPRISE_SERVER_CORE>
      <WIN_VER_WINDOWS2012_STORAGE_EXPRESS_SERVER>False</WIN_VER_WINDOWS2012_STORAGE_EXPRESS_SERVER>
      <WIN_VER_WINDOWS2012_STORAGE_EXPRESS_SERVER_CORE>False</WIN_VER_WINDOWS2012_STORAGE_EXPRESS_SERVER_CORE>
      <WIN_VER_WINDOWS2012_STORAGE_STANDARD_EVALUATION_SERVER>False</WIN_VER_WINDOWS2012_STORAGE_STANDARD_EVALUATION_SERVER>
      <WIN_VER_WINDOWS2012_STORAGE_STANDARD_SERVER>False</WIN_VER_WINDOWS2012_STORAGE_STANDARD_SERVER>
      <WIN_VER_WINDOWS2012_STORAGE_STANDARD_SERVER_CORE>False</WIN_VER_WINDOWS2012_STORAGE_STANDARD_SERVER_CORE>
      <WIN_VER_WINDOWS2012_STORAGE_WORKGROUP_EVALUATION_SERVER>False</WIN_VER_WINDOWS2012_STORAGE_WORKGROUP_EVALUATION_SERVER>
      <WIN_VER_WINDOWS2012_STORAGE_WORKGROUP_SERVER>False</WIN_VER_WINDOWS2012_STORAGE_WORKGROUP_SERVER>
      <WIN_VER_WINDOWS2012_STORAGE_WORKGROUP_SERVER_CORE>False</WIN_VER_WINDOWS2012_STORAGE_WORKGROUP_SERVER_CORE>
      <WIN_VER_WINDOWS2012_UNDEFINED>False</WIN_VER_WINDOWS2012_UNDEFINED>
      <WIN_VER_WINDOWS2012_ULTIMATE>False</WIN_VER_WINDOWS2012_ULTIMATE>
      <WIN_VER_WINDOWS2012_WEB_SERVER>False</WIN_VER_WINDOWS2012_WEB_SERVER>
      <WIN_VER_WINDOWS2012_WEB_SERVER_CORE>False</WIN_VER_WINDOWS2012_WEB_SERVER_CORE>
      <WIN_VER_WINDOWS81>False</WIN_VER_WINDOWS81>
      <WIN_VER_WINDOWS81_UNLICENSED>False</WIN_VER_WINDOWS81_UNLICENSED>
      <WIN_VER_WINDOWS81_BUSINESS>False</WIN_VER_WINDOWS81_BUSINESS>
      <WIN_VER_WINDOWS81_CLUSTER_SERVER>False</WIN_VER_WINDOWS81_CLUSTER_SERVER>
      <WIN_VER_WINDOWS81_CLUSTER_SERVER_V>False</WIN_VER_WINDOWS81_CLUSTER_SERVER_V>
      <WIN_VER_WINDOWS81_CORE>False</WIN_VER_WINDOWS81_CORE>
      <WIN_VER_WINDOWS81_CORE_COUNTRYSPECIFIC>False</WIN_VER_WINDOWS81_CORE_COUNTRYSPECIFIC>
      <WIN_VER_WINDOWS81_CORE_SINGLELANGUAGE>False</WIN_VER_WINDOWS81_CORE_SINGLELANGUAGE>
      <WIN_VER_WINDOWS81_DATACENTER_EVALUATION_SERVER>False</WIN_VER_WINDOWS81_DATACENTER_EVALUATION_SERVER>
      <WIN_VER_WINDOWS81_DATACENTER_SERVER>False</WIN_VER_WINDOWS81_DATACENTER_SERVER>
      <WIN_VER_WINDOWS81_DATACENTER_SERVER_CORE>False</WIN_VER_WINDOWS81_DATACENTER_SERVER_CORE>
      <WIN_VER_WINDOWS81_DATACENTER_SERVER_CORE_V>False</WIN_VER_WINDOWS81_DATACENTER_SERVER_CORE_V>
      <WIN_VER_WINDOWS81_DATACENTER_SERVER_V>False</WIN_VER_WINDOWS81_DATACENTER_SERVER_V>
      <WIN_VER_WINDOWS81_ENTERPRISE>False</WIN_VER_WINDOWS81_ENTERPRISE>
      <WIN_VER_WINDOWS81_ENTERPRISE_N_EVALUATION>False</WIN_VER_WINDOWS81_ENTERPRISE_N_EVALUATION>
      <WIN_VER_WINDOWS81_ENTERPRISE_EVALUATION>False</WIN_VER_WINDOWS81_ENTERPRISE_EVALUATION>
      <WIN_VER_WINDOWS81_ENTERPRISE_SERVER>False</WIN_VER_WINDOWS81_ENTERPRISE_SERVER>
      <WIN_VER_WINDOWS81_ENTERPRISE_SERVER_CORE>False</WIN_VER_WINDOWS81_ENTERPRISE_SERVER_CORE>
      <WIN_VER_WINDOWS81_ENTERPRISE_SERVER_CORE_V>False</WIN_VER_WINDOWS81_ENTERPRISE_SERVER_CORE_V>
      <WIN_VER_WINDOWS81_ENTERPRISE_SERVER_IA64>False</WIN_VER_WINDOWS81_ENTERPRISE_SERVER_IA64>
      <WIN_VER_WINDOWS81_ENTERPRISE_SERVER_V>False</WIN_VER_WINDOWS81_ENTERPRISE_SERVER_V>
      <WIN_VER_WINDOWS81_ESSENTIALBUSINESS_SERVER_MGMT>False</WIN_VER_WINDOWS81_ESSENTIALBUSINESS_SERVER_MGMT>
      <WIN_VER_WINDOWS81_ESSENTIALBUSINESS_SERVER_ADDL>False</WIN_VER_WINDOWS81_ESSENTIALBUSINESS_SERVER_ADDL>
      <WIN_VER_WINDOWS81_ESSENTIALBUSINESS_SERVER_MGMTSVC>False</WIN_VER_WINDOWS81_ESSENTIALBUSINESS_SERVER_MGMTSVC>
      <WIN_VER_WINDOWS81_ESSENTIALBUSINESS_SERVER_ADDLSVC>False</WIN_VER_WINDOWS81_ESSENTIALBUSINESS_SERVER_ADDLSVC>
      <WIN_VER_WINDOWS81_HOME_BASIC>False</WIN_VER_WINDOWS81_HOME_BASIC>
      <WIN_VER_WINDOWS81_HOME_PREMIUM>False</WIN_VER_WINDOWS81_HOME_PREMIUM>
      <WIN_VER_WINDOWS81_HOME_PREMIUM_SERVER>False</WIN_VER_WINDOWS81_HOME_PREMIUM_SERVER>
      <WIN_VER_WINDOWS81_HOME_SERVER>False</WIN_VER_WINDOWS81_HOME_SERVER>
      <WIN_VER_WINDOWS81_HYPERV>False</WIN_VER_WINDOWS81_HYPERV>
      <WIN_VER_WINDOWS81_MEDIUMBUSINESS_SERVER_MANAGEMENT>False</WIN_VER_WINDOWS81_MEDIUMBUSINESS_SERVER_MANAGEMENT>
      <WIN_VER_WINDOWS81_MEDIUMBUSINESS_SERVER_MESSAGING>False</WIN_VER_WINDOWS81_MEDIUMBUSINESS_SERVER_MESSAGING>
      <WIN_VER_WINDOWS81_MEDIUMBUSINESS_SERVER_SECURITY>False</WIN_VER_WINDOWS81_MEDIUMBUSINESS_SERVER_SECURITY>
      <WIN_VER_WINDOWS81_MULTIPOINT_STANDARD_SERVER>False</WIN_VER_WINDOWS81_MULTIPOINT_STANDARD_SERVER>
      <WIN_VER_WINDOWS81_MULTIPOINT_PREMIUM_SERVER>False</WIN_VER_WINDOWS81_MULTIPOINT_PREMIUM_SERVER>
      <WIN_VER_WINDOWS81_PROFESSIONAL>False</WIN_VER_WINDOWS81_PROFESSIONAL>
      <WIN_VER_WINDOWS81_PROFESSIONAL_WMC>False</WIN_VER_WINDOWS81_PROFESSIONAL_WMC>
      <WIN_VER_WINDOWS81_SB_SOLUTION_SERVER_EM>False</WIN_VER_WINDOWS81_SB_SOLUTION_SERVER_EM>
      <WIN_VER_WINDOWS81_SERVER_FOR_SB_SOLUTIONS>False</WIN_VER_WINDOWS81_SERVER_FOR_SB_SOLUTIONS>
      <WIN_VER_WINDOWS81_SERVER_FOR_SB_SOLUTIONS_EM>False</WIN_VER_WINDOWS81_SERVER_FOR_SB_SOLUTIONS_EM>
      <WIN_VER_WINDOWS81_SERVER_FOR_SMALLBUSINESS>False</WIN_VER_WINDOWS81_SERVER_FOR_SMALLBUSINESS>
      <WIN_VER_WINDOWS81_SERVER_FOR_SMALLBUSINESS_V>False</WIN_VER_WINDOWS81_SERVER_FOR_SMALLBUSINESS_V>
      <WIN_VER_WINDOWS81_SERVER_FOUNDATION>False</WIN_VER_WINDOWS81_SERVER_FOUNDATION>
      <WIN_VER_WINDOWS81_SB_SOLUTION_SERVER>False</WIN_VER_WINDOWS81_SB_SOLUTION_SERVER>
      <WIN_VER_WINDOWS81_SMALLBUSINESS_SERVER>False</WIN_VER_WINDOWS81_SMALLBUSINESS_SERVER>
      <WIN_VER_WINDOWS81_SMALLBUSINESS_SERVER_PREMIUM>False</WIN_VER_WINDOWS81_SMALLBUSINESS_SERVER_PREMIUM>
      <WIN_VER_WINDOWS81_SMALLBUSINESS_SERVER_PREMIUM_CORE>False</WIN_VER_WINDOWS81_SMALLBUSINESS_SERVER_PREMIUM_CORE>
      <WIN_VER_WINDOWS81_SOLUTION_EMBEDDEDSERVER>False</WIN_VER_WINDOWS81_SOLUTION_EMBEDDEDSERVER>
      <WIN_VER_WINDOWS81_STANDARD_EVALUATION_SERVER>False</WIN_VER_WINDOWS81_STANDARD_EVALUATION_SERVER>
      <WIN_VER_WINDOWS81_STANDARD_SERVER>False</WIN_VER_WINDOWS81_STANDARD_SERVER>
      <WIN_VER_WINDOWS81_STANDARD_SERVER_CORE>False</WIN_VER_WINDOWS81_STANDARD_SERVER_CORE>
      <WIN_VER_WINDOWS81_STANDARD_SERVER_V>False</WIN_VER_WINDOWS81_STANDARD_SERVER_V>
      <WIN_VER_WINDOWS81_STANDARD_SERVER_CORE_V>False</WIN_VER_WINDOWS81_STANDARD_SERVER_CORE_V>
      <WIN_VER_WINDOWS81_STANDARD_SERVER_SOLUTIONS>False</WIN_VER_WINDOWS81_STANDARD_SERVER_SOLUTIONS>
      <WIN_VER_WINDOWS81_STANDARD_SERVER_SOLUTIONS_CORE>False</WIN_VER_WINDOWS81_STANDARD_SERVER_SOLUTIONS_CORE>
      <WIN_VER_WINDOWS81_STARTER>False</WIN_VER_WINDOWS81_STARTER>
      <WIN_VER_WINDOWS81_STORAGE_ENTERPRISE_SERVER>False</WIN_VER_WINDOWS81_STORAGE_ENTERPRISE_SERVER>
      <WIN_VER_WINDOWS81_STORAGE_ENTERPRISE_SERVER_CORE>False</WIN_VER_WINDOWS81_STORAGE_ENTERPRISE_SERVER_CORE>
      <WIN_VER_WINDOWS81_STORAGE_EXPRESS_SERVER>False</WIN_VER_WINDOWS81_STORAGE_EXPRESS_SERVER>
      <WIN_VER_WINDOWS81_STORAGE_EXPRESS_SERVER_CORE>False</WIN_VER_WINDOWS81_STORAGE_EXPRESS_SERVER_CORE>
      <WIN_VER_WINDOWS81_STORAGE_STANDARD_EVALUATION_SERVER>False</WIN_VER_WINDOWS81_STORAGE_STANDARD_EVALUATION_SERVER>
      <WIN_VER_WINDOWS81_STORAGE_STANDARD_SERVER>False</WIN_VER_WINDOWS81_STORAGE_STANDARD_SERVER>
      <WIN_VER_WINDOWS81_STORAGE_STANDARD_SERVER_CORE>False</WIN_VER_WINDOWS81_STORAGE_STANDARD_SERVER_CORE>
      <WIN_VER_WINDOWS81_STORAGE_WORKGROUP_EVALUATION_SERVER>False</WIN_VER_WINDOWS81_STORAGE_WORKGROUP_EVALUATION_SERVER>
      <WIN_VER_WINDOWS81_STORAGE_WORKGROUP_SERVER>False</WIN_VER_WINDOWS81_STORAGE_WORKGROUP_SERVER>
      <WIN_VER_WINDOWS81_STORAGE_WORKGROUP_SERVER_CORE>False</WIN_VER_WINDOWS81_STORAGE_WORKGROUP_SERVER_CORE>
      <WIN_VER_WINDOWS81_UNDEFINED>False</WIN_VER_WINDOWS81_UNDEFINED>
      <WIN_VER_WINDOWS81_ULTIMATE>False</WIN_VER_WINDOWS81_ULTIMATE>
      <WIN_VER_WINDOWS81_WEB_SERVER>False</WIN_VER_WINDOWS81_WEB_SERVER>
      <WIN_VER_WINDOWS81_WEB_SERVER_CORE>False</WIN_VER_WINDOWS81_WEB_SERVER_CORE>
      <WIN_VER_WINDOWS2012R2>False</WIN_VER_WINDOWS2012R2>
      <WIN_VER_WINDOWS2012R2_UNLICENSED>False</WIN_VER_WINDOWS2012R2_UNLICENSED>
      <WIN_VER_WINDOWS2012R2_BUSINESS>False</WIN_VER_WINDOWS2012R2_BUSINESS>
      <WIN_VER_WINDOWS2012R2_CLUSTER_SERVER>False</WIN_VER_WINDOWS2012R2_CLUSTER_SERVER>
      <WIN_VER_WINDOWS2012R2_CLUSTER_SERVER_V>False</WIN_VER_WINDOWS2012R2_CLUSTER_SERVER_V>
      <WIN_VER_WINDOWS2012R2_CORE>False</WIN_VER_WINDOWS2012R2_CORE>
      <WIN_VER_WINDOWS2012R2_CORE_COUNTRYSPECIFIC>False</WIN_VER_WINDOWS2012R2_CORE_COUNTRYSPECIFIC>
      <WIN_VER_WINDOWS2012R2_CORE_SINGLELANGUAGE>False</WIN_VER_WINDOWS2012R2_CORE_SINGLELANGUAGE>
      <WIN_VER_WINDOWS2012R2_DATACENTER_EVALUATION_SERVER>False</WIN_VER_WINDOWS2012R2_DATACENTER_EVALUATION_SERVER>
      <WIN_VER_WINDOWS2012R2_DATACENTER_SERVER>False</WIN_VER_WINDOWS2012R2_DATACENTER_SERVER>
      <WIN_VER_WINDOWS2012R2_DATACENTER_SERVER_CORE>False</WIN_VER_WINDOWS2012R2_DATACENTER_SERVER_CORE>
      <WIN_VER_WINDOWS2012R2_DATACENTER_SERVER_CORE_V>False</WIN_VER_WINDOWS2012R2_DATACENTER_SERVER_CORE_V>
      <WIN_VER_WINDOWS2012R2_DATACENTER_SERVER_V>False</WIN_VER_WINDOWS2012R2_DATACENTER_SERVER_V>
      <WIN_VER_WINDOWS2012R2_ENTERPRISE>False</WIN_VER_WINDOWS2012R2_ENTERPRISE>
      <WIN_VER_WINDOWS2012R2_ENTERPRISE_N_EVALUATION>False</WIN_VER_WINDOWS2012R2_ENTERPRISE_N_EVALUATION>
      <WIN_VER_WINDOWS2012R2_ENTERPRISE_EVALUATION>False</WIN_VER_WINDOWS2012R2_ENTERPRISE_EVALUATION>
      <WIN_VER_WINDOWS2012R2_ENTERPRISE_SERVER>False</WIN_VER_WINDOWS2012R2_ENTERPRISE_SERVER>
      <WIN_VER_WINDOWS2012R2_ENTERPRISE_SERVER_CORE>False</WIN_VER_WINDOWS2012R2_ENTERPRISE_SERVER_CORE>
      <WIN_VER_WINDOWS2012R2_ENTERPRISE_SERVER_CORE_V>False</WIN_VER_WINDOWS2012R2_ENTERPRISE_SERVER_CORE_V>
      <WIN_VER_WINDOWS2012R2_ENTERPRISE_SERVER_IA64>False</WIN_VER_WINDOWS2012R2_ENTERPRISE_SERVER_IA64>
      <WIN_VER_WINDOWS2012R2_ENTERPRISE_SERVER_V>False</WIN_VER_WINDOWS2012R2_ENTERPRISE_SERVER_V>
      <WIN_VER_WINDOWS2012R2_ESSENTIALBUSINESS_SERVER_MGMT>False</WIN_VER_WINDOWS2012R2_ESSENTIALBUSINESS_SERVER_MGMT>
      <WIN_VER_WINDOWS2012R2_ESSENTIALBUSINESS_SERVER_ADDL>False</WIN_VER_WINDOWS2012R2_ESSENTIALBUSINESS_SERVER_ADDL>
      <WIN_VER_WINDOWS2012R2_ESSENTIALBUSINESS_SERVER_MGMTSVC>False</WIN_VER_WINDOWS2012R2_ESSENTIALBUSINESS_SERVER_MGMTSVC>
      <WIN_VER_WINDOWS2012R2_ESSENTIALBUSINESS_SERVER_ADDLSVC>False</WIN_VER_WINDOWS2012R2_ESSENTIALBUSINESS_SERVER_ADDLSVC>
      <WIN_VER_WINDOWS2012R2_HOME_BASIC>False</WIN_VER_WINDOWS2012R2_HOME_BASIC>
      <WIN_VER_WINDOWS2012R2_HOME_PREMIUM>False</WIN_VER_WINDOWS2012R2_HOME_PREMIUM>
      <WIN_VER_WINDOWS2012R2_HOME_PREMIUM_SERVER>False</WIN_VER_WINDOWS2012R2_HOME_PREMIUM_SERVER>
      <WIN_VER_WINDOWS2012R2_HOME_SERVER>False</WIN_VER_WINDOWS2012R2_HOME_SERVER>
      <WIN_VER_WINDOWS2012R2_HYPERV>False</WIN_VER_WINDOWS2012R2_HYPERV>
      <WIN_VER_WINDOWS2012R2_MEDIUMBUSINESS_SERVER_MANAGEMENT>False</WIN_VER_WINDOWS2012R2_MEDIUMBUSINESS_SERVER_MANAGEMENT>
      <WIN_VER_WINDOWS2012R2_MEDIUMBUSINESS_SERVER_MESSAGING>False</WIN_VER_WINDOWS2012R2_MEDIUMBUSINESS_SERVER_MESSAGING>
      <WIN_VER_WINDOWS2012R2_MEDIUMBUSINESS_SERVER_SECURITY>False</WIN_VER_WINDOWS2012R2_MEDIUMBUSINESS_SERVER_SECURITY>
      <WIN_VER_WINDOWS2012R2_MULTIPOINT_STANDARD_SERVER>False</WIN_VER_WINDOWS2012R2_MULTIPOINT_STANDARD_SERVER>
      <WIN_VER_WINDOWS2012R2_MULTIPOINT_PREMIUM_SERVER>False</WIN_VER_WINDOWS2012R2_MULTIPOINT_PREMIUM_SERVER>
      <WIN_VER_WINDOWS2012R2_PROFESSIONAL>False</WIN_VER_WINDOWS2012R2_PROFESSIONAL>
      <WIN_VER_WINDOWS2012R2_PROFESSIONAL_WMC>False</WIN_VER_WINDOWS2012R2_PROFESSIONAL_WMC>
      <WIN_VER_WINDOWS2012R2_SB_SOLUTION_SERVER_EM>False</WIN_VER_WINDOWS2012R2_SB_SOLUTION_SERVER_EM>
      <WIN_VER_WINDOWS2012R2_SERVER_FOR_SB_SOLUTIONS>False</WIN_VER_WINDOWS2012R2_SERVER_FOR_SB_SOLUTIONS>
      <WIN_VER_WINDOWS2012R2_SERVER_FOR_SB_SOLUTIONS_EM>False</WIN_VER_WINDOWS2012R2_SERVER_FOR_SB_SOLUTIONS_EM>
      <WIN_VER_WINDOWS2012R2_SERVER_FOR_SMALLBUSINESS>False</WIN_VER_WINDOWS2012R2_SERVER_FOR_SMALLBUSINESS>
      <WIN_VER_WINDOWS2012R2_SERVER_FOR_SMALLBUSINESS_V>False</WIN_VER_WINDOWS2012R2_SERVER_FOR_SMALLBUSINESS_V>
      <WIN_VER_WINDOWS2012R2_SERVER_FOUNDATION>False</WIN_VER_WINDOWS2012R2_SERVER_FOUNDATION>
      <WIN_VER_WINDOWS2012R2_SB_SOLUTION_SERVER>False</WIN_VER_WINDOWS2012R2_SB_SOLUTION_SERVER>
      <WIN_VER_WINDOWS2012R2_SMALLBUSINESS_SERVER>False</WIN_VER_WINDOWS2012R2_SMALLBUSINESS_SERVER>
      <WIN_VER_WINDOWS2012R2_SMALLBUSINESS_SERVER_PREMIUM>False</WIN_VER_WINDOWS2012R2_SMALLBUSINESS_SERVER_PREMIUM>
      <WIN_VER_WINDOWS2012R2_SMALLBUSINESS_SERVER_PREMIUM_CORE>False</WIN_VER_WINDOWS2012R2_SMALLBUSINESS_SERVER_PREMIUM_CORE>
      <WIN_VER_WINDOWS2012R2_SOLUTION_EMBEDDEDSERVER>False</WIN_VER_WINDOWS2012R2_SOLUTION_EMBEDDEDSERVER>
      <WIN_VER_WINDOWS2012R2_STANDARD_EVALUATION_SERVER>False</WIN_VER_WINDOWS2012R2_STANDARD_EVALUATION_SERVER>
      <WIN_VER_WINDOWS2012R2_STANDARD_SERVER>False</WIN_VER_WINDOWS2012R2_STANDARD_SERVER>
      <WIN_VER_WINDOWS2012R2_STANDARD_SERVER_CORE>False</WIN_VER_WINDOWS2012R2_STANDARD_SERVER_CORE>
      <WIN_VER_WINDOWS2012R2_STANDARD_SERVER_V>False</WIN_VER_WINDOWS2012R2_STANDARD_SERVER_V>
      <WIN_VER_WINDOWS2012R2_STANDARD_SERVER_CORE_V>False</WIN_VER_WINDOWS2012R2_STANDARD_SERVER_CORE_V>
      <WIN_VER_WINDOWS2012R2_STANDARD_SERVER_SOLUTIONS>False</WIN_VER_WINDOWS2012R2_STANDARD_SERVER_SOLUTIONS>
      <WIN_VER_WINDOWS2012R2_STANDARD_SERVER_SOLUTIONS_CORE>False</WIN_VER_WINDOWS2012R2_STANDARD_SERVER_SOLUTIONS_CORE>
      <WIN_VER_WINDOWS2012R2_STARTER>False</WIN_VER_WINDOWS2012R2_STARTER>
      <WIN_VER_WINDOWS2012R2_STORAGE_ENTERPRISE_SERVER>False</WIN_VER_WINDOWS2012R2_STORAGE_ENTERPRISE_SERVER>
      <WIN_VER_WINDOWS2012R2_STORAGE_ENTERPRISE_SERVER_CORE>False</WIN_VER_WINDOWS2012R2_STORAGE_ENTERPRISE_SERVER_CORE>
      <WIN_VER_WINDOWS2012R2_STORAGE_EXPRESS_SERVER>False</WIN_VER_WINDOWS2012R2_STORAGE_EXPRESS_SERVER>
      <WIN_VER_WINDOWS2012R2_STORAGE_EXPRESS_SERVER_CORE>False</WIN_VER_WINDOWS2012R2_STORAGE_EXPRESS_SERVER_CORE>
      <WIN_VER_WINDOWS2012R2_STORAGE_STANDARD_EVALUATION_SERVER>False</WIN_VER_WINDOWS2012R2_STORAGE_STANDARD_EVALUATION_SERVER>
      <WIN_VER_WINDOWS2012R2_STORAGE_STANDARD_SERVER>False</WIN_VER_WINDOWS2012R2_STORAGE_STANDARD_SERVER>
      <WIN_VER_WINDOWS2012R2_STORAGE_STANDARD_SERVER_CORE>False</WIN_VER_WINDOWS2012R2_STORAGE_STANDARD_SERVER_CORE>
      <WIN_VER_WINDOWS2012R2_STORAGE_WORKGROUP_EVALUATION_SERVER>False</WIN_VER_WINDOWS2012R2_STORAGE_WORKGROUP_EVALUATION_SERVER>
      <WIN_VER_WINDOWS2012R2_STORAGE_WORKGROUP_SERVER>False</WIN_VER_WINDOWS2012R2_STORAGE_WORKGROUP_SERVER>
      <WIN_VER_WINDOWS2012R2_STORAGE_WORKGROUP_SERVER_CORE>False</WIN_VER_WINDOWS2012R2_STORAGE_WORKGROUP_SERVER_CORE>
      <WIN_VER_WINDOWS2012R2_UNDEFINED>False</WIN_VER_WINDOWS2012R2_UNDEFINED>
      <WIN_VER_WINDOWS2012R2_ULTIMATE>False</WIN_VER_WINDOWS2012R2_ULTIMATE>
      <WIN_VER_WINDOWS2012R2_WEB_SERVER>False</WIN_VER_WINDOWS2012R2_WEB_SERVER>
      <WIN_VER_WINDOWS2012R2_WEB_SERVER_CORE>False</WIN_VER_WINDOWS2012R2_WEB_SERVER_CORE>
      <WIN_VER_WINDOWS10>False</WIN_VER_WINDOWS10>
      <WIN_VER_WINDOWS10_UNLICENSED>False</WIN_VER_WINDOWS10_UNLICENSED>
      <WIN_VER_WINDOWS10_BUSINESS>False</WIN_VER_WINDOWS10_BUSINESS>
      <WIN_VER_WINDOWS10_CLUSTER_SERVER>False</WIN_VER_WINDOWS10_CLUSTER_SERVER>
      <WIN_VER_WINDOWS10_CLUSTER_SERVER_V>False</WIN_VER_WINDOWS10_CLUSTER_SERVER_V>
      <WIN_VER_WINDOWS10_CORE>False</WIN_VER_WINDOWS10_CORE>
      <WIN_VER_WINDOWS10_CORE_COUNTRYSPECIFIC>False</WIN_VER_WINDOWS10_CORE_COUNTRYSPECIFIC>
      <WIN_VER_WINDOWS10_CORE_SINGLELANGUAGE>False</WIN_VER_WINDOWS10_CORE_SINGLELANGUAGE>
      <WIN_VER_WINDOWS10_DATACENTER_EVALUATION_SERVER>False</WIN_VER_WINDOWS10_DATACENTER_EVALUATION_SERVER>
      <WIN_VER_WINDOWS10_DATACENTER_SERVER>False</WIN_VER_WINDOWS10_DATACENTER_SERVER>
      <WIN_VER_WINDOWS10_DATACENTER_SERVER_CORE>False</WIN_VER_WINDOWS10_DATACENTER_SERVER_CORE>
      <WIN_VER_WINDOWS10_DATACENTER_SERVER_CORE_V>False</WIN_VER_WINDOWS10_DATACENTER_SERVER_CORE_V>
      <WIN_VER_WINDOWS10_DATACENTER_SERVER_V>False</WIN_VER_WINDOWS10_DATACENTER_SERVER_V>
      <WIN_VER_WINDOWS10_ENTERPRISE>False</WIN_VER_WINDOWS10_ENTERPRISE>
      <WIN_VER_WINDOWS10_ENTERPRISE_N_EVALUATION>False</WIN_VER_WINDOWS10_ENTERPRISE_N_EVALUATION>
      <WIN_VER_WINDOWS10_ENTERPRISE_EVALUATION>False</WIN_VER_WINDOWS10_ENTERPRISE_EVALUATION>
      <WIN_VER_WINDOWS10_ENTERPRISE_SERVER>False</WIN_VER_WINDOWS10_ENTERPRISE_SERVER>
      <WIN_VER_WINDOWS10_ENTERPRISE_SERVER_CORE>False</WIN_VER_WINDOWS10_ENTERPRISE_SERVER_CORE>
      <WIN_VER_WINDOWS10_ENTERPRISE_SERVER_CORE_V>False</WIN_VER_WINDOWS10_ENTERPRISE_SERVER_CORE_V>
      <WIN_VER_WINDOWS10_ENTERPRISE_SERVER_IA64>False</WIN_VER_WINDOWS10_ENTERPRISE_SERVER_IA64>
      <WIN_VER_WINDOWS10_ENTERPRISE_SERVER_V>False</WIN_VER_WINDOWS10_ENTERPRISE_SERVER_V>
      <WIN_VER_WINDOWS10_ESSENTIALBUSINESS_SERVER_MGMT>False</WIN_VER_WINDOWS10_ESSENTIALBUSINESS_SERVER_MGMT>
      <WIN_VER_WINDOWS10_ESSENTIALBUSINESS_SERVER_ADDL>False</WIN_VER_WINDOWS10_ESSENTIALBUSINESS_SERVER_ADDL>
      <WIN_VER_WINDOWS10_ESSENTIALBUSINESS_SERVER_MGMTSVC>False</WIN_VER_WINDOWS10_ESSENTIALBUSINESS_SERVER_MGMTSVC>
      <WIN_VER_WINDOWS10_ESSENTIALBUSINESS_SERVER_ADDLSVC>False</WIN_VER_WINDOWS10_ESSENTIALBUSINESS_SERVER_ADDLSVC>
      <WIN_VER_WINDOWS10_HOME_BASIC>False</WIN_VER_WINDOWS10_HOME_BASIC>
      <WIN_VER_WINDOWS10_HOME_PREMIUM>False</WIN_VER_WINDOWS10_HOME_PREMIUM>
      <WIN_VER_WINDOWS10_HOME_PREMIUM_SERVER>False</WIN_VER_WINDOWS10_HOME_PREMIUM_SERVER>
      <WIN_VER_WINDOWS10_HOME_SERVER>False</WIN_VER_WINDOWS10_HOME_SERVER>
      <WIN_VER_WINDOWS10_HYPERV>False</WIN_VER_WINDOWS10_HYPERV>
      <WIN_VER_WINDOWS10_MEDIUMBUSINESS_SERVER_MANAGEMENT>False</WIN_VER_WINDOWS10_MEDIUMBUSINESS_SERVER_MANAGEMENT>
      <WIN_VER_WINDOWS10_MEDIUMBUSINESS_SERVER_MESSAGING>False</WIN_VER_WINDOWS10_MEDIUMBUSINESS_SERVER_MESSAGING>
      <WIN_VER_WINDOWS10_MEDIUMBUSINESS_SERVER_SECURITY>False</WIN_VER_WINDOWS10_MEDIUMBUSINESS_SERVER_SECURITY>
      <WIN_VER_WINDOWS10_MULTIPOINT_STANDARD_SERVER>False</WIN_VER_WINDOWS10_MULTIPOINT_STANDARD_SERVER>
      <WIN_VER_WINDOWS10_MULTIPOINT_PREMIUM_SERVER>False</WIN_VER_WINDOWS10_MULTIPOINT_PREMIUM_SERVER>
      <WIN_VER_WINDOWS10_PROFESSIONAL>False</WIN_VER_WINDOWS10_PROFESSIONAL>
      <WIN_VER_WINDOWS10_PROFESSIONAL_WMC>False</WIN_VER_WINDOWS10_PROFESSIONAL_WMC>
      <WIN_VER_WINDOWS10_SB_SOLUTION_SERVER_EM>False</WIN_VER_WINDOWS10_SB_SOLUTION_SERVER_EM>
      <WIN_VER_WINDOWS10_SERVER_FOR_SB_SOLUTIONS>False</WIN_VER_WINDOWS10_SERVER_FOR_SB_SOLUTIONS>
      <WIN_VER_WINDOWS10_SERVER_FOR_SB_SOLUTIONS_EM>False</WIN_VER_WINDOWS10_SERVER_FOR_SB_SOLUTIONS_EM>
      <WIN_VER_WINDOWS10_SERVER_FOR_SMALLBUSINESS>False</WIN_VER_WINDOWS10_SERVER_FOR_SMALLBUSINESS>
      <WIN_VER_WINDOWS10_SERVER_FOR_SMALLBUSINESS_V>False</WIN_VER_WINDOWS10_SERVER_FOR_SMALLBUSINESS_V>
      <WIN_VER_WINDOWS10_SERVER_FOUNDATION>False</WIN_VER_WINDOWS10_SERVER_FOUNDATION>
      <WIN_VER_WINDOWS10_SB_SOLUTION_SERVER>False</WIN_VER_WINDOWS10_SB_SOLUTION_SERVER>
      <WIN_VER_WINDOWS10_SMALLBUSINESS_SERVER>False</WIN_VER_WINDOWS10_SMALLBUSINESS_SERVER>
      <WIN_VER_WINDOWS10_SMALLBUSINESS_SERVER_PREMIUM>False</WIN_VER_WINDOWS10_SMALLBUSINESS_SERVER_PREMIUM>
      <WIN_VER_WINDOWS10_SMALLBUSINESS_SERVER_PREMIUM_CORE>False</WIN_VER_WINDOWS10_SMALLBUSINESS_SERVER_PREMIUM_CORE>
      <WIN_VER_WINDOWS10_SOLUTION_EMBEDDEDSERVER>False</WIN_VER_WINDOWS10_SOLUTION_EMBEDDEDSERVER>
      <WIN_VER_WINDOWS10_STANDARD_EVALUATION_SERVER>False</WIN_VER_WINDOWS10_STANDARD_EVALUATION_SERVER>
      <WIN_VER_WINDOWS10_STANDARD_SERVER>False</WIN_VER_WINDOWS10_STANDARD_SERVER>
      <WIN_VER_WINDOWS10_STANDARD_SERVER_CORE>False</WIN_VER_WINDOWS10_STANDARD_SERVER_CORE>
      <WIN_VER_WINDOWS10_STANDARD_SERVER_V>False</WIN_VER_WINDOWS10_STANDARD_SERVER_V>
      <WIN_VER_WINDOWS10_STANDARD_SERVER_CORE_V>False</WIN_VER_WINDOWS10_STANDARD_SERVER_CORE_V>
      <WIN_VER_WINDOWS10_STANDARD_SERVER_SOLUTIONS>False</WIN_VER_WINDOWS10_STANDARD_SERVER_SOLUTIONS>
      <WIN_VER_WINDOWS10_STANDARD_SERVER_SOLUTIONS_CORE>False</WIN_VER_WINDOWS10_STANDARD_SERVER_SOLUTIONS_CORE>
      <WIN_VER_WINDOWS10_STARTER>False</WIN_VER_WINDOWS10_STARTER>
      <WIN_VER_WINDOWS10_STORAGE_ENTERPRISE_SERVER>False</WIN_VER_WINDOWS10_STORAGE_ENTERPRISE_SERVER>
      <WIN_VER_WINDOWS10_STORAGE_ENTERPRISE_SERVER_CORE>False</WIN_VER_WINDOWS10_STORAGE_ENTERPRISE_SERVER_CORE>
      <WIN_VER_WINDOWS10_STORAGE_EXPRESS_SERVER>False</WIN_VER_WINDOWS10_STORAGE_EXPRESS_SERVER>
      <WIN_VER_WINDOWS10_STORAGE_EXPRESS_SERVER_CORE>False</WIN_VER_WINDOWS10_STORAGE_EXPRESS_SERVER_CORE>
      <WIN_VER_WINDOWS10_STORAGE_STANDARD_EVALUATION_SERVER>False</WIN_VER_WINDOWS10_STORAGE_STANDARD_EVALUATION_SERVER>
      <WIN_VER_WINDOWS10_STORAGE_STANDARD_SERVER>False</WIN_VER_WINDOWS10_STORAGE_STANDARD_SERVER>
      <WIN_VER_WINDOWS10_STORAGE_STANDARD_SERVER_CORE>False</WIN_VER_WINDOWS10_STORAGE_STANDARD_SERVER_CORE>
      <WIN_VER_WINDOWS10_STORAGE_WORKGROUP_EVALUATION_SERVER>False</WIN_VER_WINDOWS10_STORAGE_WORKGROUP_EVALUATION_SERVER>
      <WIN_VER_WINDOWS10_STORAGE_WORKGROUP_SERVER>False</WIN_VER_WINDOWS10_STORAGE_WORKGROUP_SERVER>
      <WIN_VER_WINDOWS10_STORAGE_WORKGROUP_SERVER_CORE>False</WIN_VER_WINDOWS10_STORAGE_WORKGROUP_SERVER_CORE>
      <WIN_VER_WINDOWS10_UNDEFINED>False</WIN_VER_WINDOWS10_UNDEFINED>
      <WIN_VER_WINDOWS10_ULTIMATE>False</WIN_VER_WINDOWS10_ULTIMATE>
      <WIN_VER_WINDOWS10_WEB_SERVER>False</WIN_VER_WINDOWS10_WEB_SERVER>
      <WIN_VER_WINDOWS10_WEB_SERVER_CORE>False</WIN_VER_WINDOWS10_WEB_SERVER_CORE>
      <WIN_VER_WINDOWS10_MOBILE_CORE>False</WIN_VER_WINDOWS10_MOBILE_CORE>
      <WIN_VER_WINDOWS10_EMBEDDED_INDUSTRY_EVAL>False</WIN_VER_WINDOWS10_EMBEDDED_INDUSTRY_EVAL>
      <WIN_VER_WINDOWS10_EMBEDDED_INDUSTRY_E_EVAL>False</WIN_VER_WINDOWS10_EMBEDDED_INDUSTRY_E_EVAL>
      <WIN_VER_WINDOWS10_EMBEDDED_EVAL>False</WIN_VER_WINDOWS10_EMBEDDED_EVAL>
      <WIN_VER_WINDOWS10_EMBEDDED_E_EVAL>False</WIN_VER_WINDOWS10_EMBEDDED_E_EVAL>
      <WIN_VER_WINDOWS10_NANO_SERVER>False</WIN_VER_WINDOWS10_NANO_SERVER>
      <WIN_VER_WINDOWS10_CLOUD_STORAGE_SERVER>False</WIN_VER_WINDOWS10_CLOUD_STORAGE_SERVER>
      <WIN_VER_WINDOWS10_CORE_CONNECTED>False</WIN_VER_WINDOWS10_CORE_CONNECTED>
      <WIN_VER_WINDOWS10_PROFESSIONAL_STUDENT>False</WIN_VER_WINDOWS10_PROFESSIONAL_STUDENT>
      <WIN_VER_WINDOWS10_CORE_CONNECTED_SINGLELANGUAGE>False</WIN_VER_WINDOWS10_CORE_CONNECTED_SINGLELANGUAGE>
      <WIN_VER_WINDOWS10_CORE_CONNECTED_COUNTRYSPECIFIC>False</WIN_VER_WINDOWS10_CORE_CONNECTED_COUNTRYSPECIFIC>
      <WIN_VER_WINDOWS10_CONNECTED_CAR>False</WIN_VER_WINDOWS10_CONNECTED_CAR>
      <WIN_VER_WINDOWS10_INDUSTRY_HANDHELD>False</WIN_VER_WINDOWS10_INDUSTRY_HANDHELD>
      <WIN_VER_WINDOWS10_PPI_PRO>False</WIN_VER_WINDOWS10_PPI_PRO>
      <WIN_VER_WINDOWS10_ARM64_SERVER>False</WIN_VER_WINDOWS10_ARM64_SERVER>
      <WIN_VER_WINDOWS10_EDUCATION>False</WIN_VER_WINDOWS10_EDUCATION>
      <WIN_VER_WINDOWS10_IOTUAP>False</WIN_VER_WINDOWS10_IOTUAP>
      <WIN_VER_WINDOWS10_CLOUD_HOST_INFRASTRUCTURE_SERVER>False</WIN_VER_WINDOWS10_CLOUD_HOST_INFRASTRUCTURE_SERVER>
      <WIN_VER_WINDOWS10_ENTERPRISE_S>False</WIN_VER_WINDOWS10_ENTERPRISE_S>
      <WIN_VER_WINDOWS10_PROFESSIONAL_S>False</WIN_VER_WINDOWS10_PROFESSIONAL_S>
      <WIN_VER_WINDOWS10_ENTERPRISE_S_EVALUATION>False</WIN_VER_WINDOWS10_ENTERPRISE_S_EVALUATION>
      <WIN_VER_WINDOWS10_IOTUAPCOMMERCIAL>False</WIN_VER_WINDOWS10_IOTUAPCOMMERCIAL>
      <WIN_VER_WINDOWS10_MOBILE_ENTERPRISE>False</WIN_VER_WINDOWS10_MOBILE_ENTERPRISE>
      <WIN_VER_WINDOWS10_HOLOGRAPHIC>False</WIN_VER_WINDOWS10_HOLOGRAPHIC>
      <WIN_VER_WINDOWS10_PRO_SINGLE_LANGUAGE>False</WIN_VER_WINDOWS10_PRO_SINGLE_LANGUAGE>
      <WIN_VER_WINDOWS10_PRO_CHINA>False</WIN_VER_WINDOWS10_PRO_CHINA>
      <WIN_VER_WINDOWS10_ENTERPRISE_SUBSCRIPTION>False</WIN_VER_WINDOWS10_ENTERPRISE_SUBSCRIPTION>
      <WIN_VER_WINDOWS10_DATACENTER_NANO_SERVER>False</WIN_VER_WINDOWS10_DATACENTER_NANO_SERVER>
      <WIN_VER_WINDOWS10_STANDARD_NANO_SERVER>False</WIN_VER_WINDOWS10_STANDARD_NANO_SERVER>
      <WIN_VER_WINDOWS10_DATACENTER_A_SERVER_CORE>False</WIN_VER_WINDOWS10_DATACENTER_A_SERVER_CORE>
      <WIN_VER_WINDOWS10_STANDARD_A_SERVER_CORE>False</WIN_VER_WINDOWS10_STANDARD_A_SERVER_CORE>
      <WIN_VER_WINDOWS10_DATACENTER_WS_SERVER_CORE>False</WIN_VER_WINDOWS10_DATACENTER_WS_SERVER_CORE>
      <WIN_VER_WINDOWS10_STANDARD_WS_SERVER_CORE>False</WIN_VER_WINDOWS10_STANDARD_WS_SERVER_CORE>
      <WIN_VER_WINDOWS10_UTILITY_VM>False</WIN_VER_WINDOWS10_UTILITY_VM>
      <WIN_VER_WINDOWS10_DATACENTER_EVALUATION_SERVER_CORE>False</WIN_VER_WINDOWS10_DATACENTER_EVALUATION_SERVER_CORE>
      <WIN_VER_WINDOWS10_STANDARD_EVALUATION_SERVER_CORE>False</WIN_VER_WINDOWS10_STANDARD_EVALUATION_SERVER_CORE>
      <WIN_VER_WINDOWS10_PRO_WORKSTATION>False</WIN_VER_WINDOWS10_PRO_WORKSTATION>
      <WIN_VER_WINDOWS10_PRO_FOR_EDUCATION>False</WIN_VER_WINDOWS10_PRO_FOR_EDUCATION>
      <WIN_VER_WINDOWS10_AZURE_SERVER_CORE>False</WIN_VER_WINDOWS10_AZURE_SERVER_CORE>
      <WIN_VER_WINDOWS10_AZURE_NANO_SERVER>False</WIN_VER_WINDOWS10_AZURE_NANO_SERVER>
      <WIN_VER_WINDOWS2016>False</WIN_VER_WINDOWS2016>
      <WIN_VER_WINDOWS2016_UNLICENSED>False</WIN_VER_WINDOWS2016_UNLICENSED>
      <WIN_VER_WINDOWS2016_BUSINESS>False</WIN_VER_WINDOWS2016_BUSINESS>
      <WIN_VER_WINDOWS2016_CLUSTER_SERVER>False</WIN_VER_WINDOWS2016_CLUSTER_SERVER>
      <WIN_VER_WINDOWS2016_CLUSTER_SERVER_V>False</WIN_VER_WINDOWS2016_CLUSTER_SERVER_V>
      <WIN_VER_WINDOWS2016_CORE>False</WIN_VER_WINDOWS2016_CORE>
      <WIN_VER_WINDOWS2016_CORE_COUNTRYSPECIFIC>False</WIN_VER_WINDOWS2016_CORE_COUNTRYSPECIFIC>
      <WIN_VER_WINDOWS2016_CORE_SINGLELANGUAGE>False</WIN_VER_WINDOWS2016_CORE_SINGLELANGUAGE>
      <WIN_VER_WINDOWS2016_DATACENTER_EVALUATION_SERVER>False</WIN_VER_WINDOWS2016_DATACENTER_EVALUATION_SERVER>
      <WIN_VER_WINDOWS2016_DATACENTER_SERVER>False</WIN_VER_WINDOWS2016_DATACENTER_SERVER>
      <WIN_VER_WINDOWS2016_DATACENTER_SERVER_CORE>False</WIN_VER_WINDOWS2016_DATACENTER_SERVER_CORE>
      <WIN_VER_WINDOWS2016_DATACENTER_SERVER_CORE_V>False</WIN_VER_WINDOWS2016_DATACENTER_SERVER_CORE_V>
      <WIN_VER_WINDOWS2016_DATACENTER_SERVER_V>False</WIN_VER_WINDOWS2016_DATACENTER_SERVER_V>
      <WIN_VER_WINDOWS2016_ENTERPRISE>False</WIN_VER_WINDOWS2016_ENTERPRISE>
      <WIN_VER_WINDOWS2016_ENTERPRISE_N_EVALUATION>False</WIN_VER_WINDOWS2016_ENTERPRISE_N_EVALUATION>
      <WIN_VER_WINDOWS2016_ENTERPRISE_EVALUATION>False</WIN_VER_WINDOWS2016_ENTERPRISE_EVALUATION>
      <WIN_VER_WINDOWS2016_ENTERPRISE_SERVER>False</WIN_VER_WINDOWS2016_ENTERPRISE_SERVER>
      <WIN_VER_WINDOWS2016_ENTERPRISE_SERVER_CORE>False</WIN_VER_WINDOWS2016_ENTERPRISE_SERVER_CORE>
      <WIN_VER_WINDOWS2016_ENTERPRISE_SERVER_CORE_V>False</WIN_VER_WINDOWS2016_ENTERPRISE_SERVER_CORE_V>
      <WIN_VER_WINDOWS2016_ENTERPRISE_SERVER_IA64>False</WIN_VER_WINDOWS2016_ENTERPRISE_SERVER_IA64>
      <WIN_VER_WINDOWS2016_ENTERPRISE_SERVER_V>False</WIN_VER_WINDOWS2016_ENTERPRISE_SERVER_V>
      <WIN_VER_WINDOWS2016_ESSENTIALBUSINESS_SERVER_MGMT>False</WIN_VER_WINDOWS2016_ESSENTIALBUSINESS_SERVER_MGMT>
      <WIN_VER_WINDOWS2016_ESSENTIALBUSINESS_SERVER_ADDL>False</WIN_VER_WINDOWS2016_ESSENTIALBUSINESS_SERVER_ADDL>
      <WIN_VER_WINDOWS2016_ESSENTIALBUSINESS_SERVER_MGMTSVC>False</WIN_VER_WINDOWS2016_ESSENTIALBUSINESS_SERVER_MGMTSVC>
      <WIN_VER_WINDOWS2016_ESSENTIALBUSINESS_SERVER_ADDLSVC>False</WIN_VER_WINDOWS2016_ESSENTIALBUSINESS_SERVER_ADDLSVC>
      <WIN_VER_WINDOWS2016_HOME_BASIC>False</WIN_VER_WINDOWS2016_HOME_BASIC>
      <WIN_VER_WINDOWS2016_HOME_PREMIUM>False</WIN_VER_WINDOWS2016_HOME_PREMIUM>
      <WIN_VER_WINDOWS2016_HOME_PREMIUM_SERVER>False</WIN_VER_WINDOWS2016_HOME_PREMIUM_SERVER>
      <WIN_VER_WINDOWS2016_HOME_SERVER>False</WIN_VER_WINDOWS2016_HOME_SERVER>
      <WIN_VER_WINDOWS2016_HYPERV>False</WIN_VER_WINDOWS2016_HYPERV>
      <WIN_VER_WINDOWS2016_MEDIUMBUSINESS_SERVER_MANAGEMENT>False</WIN_VER_WINDOWS2016_MEDIUMBUSINESS_SERVER_MANAGEMENT>
      <WIN_VER_WINDOWS2016_MEDIUMBUSINESS_SERVER_MESSAGING>False</WIN_VER_WINDOWS2016_MEDIUMBUSINESS_SERVER_MESSAGING>
      <WIN_VER_WINDOWS2016_MEDIUMBUSINESS_SERVER_SECURITY>False</WIN_VER_WINDOWS2016_MEDIUMBUSINESS_SERVER_SECURITY>
      <WIN_VER_WINDOWS2016_MULTIPOINT_STANDARD_SERVER>False</WIN_VER_WINDOWS2016_MULTIPOINT_STANDARD_SERVER>
      <WIN_VER_WINDOWS2016_MULTIPOINT_PREMIUM_SERVER>False</WIN_VER_WINDOWS2016_MULTIPOINT_PREMIUM_SERVER>
      <WIN_VER_WINDOWS2016_PROFESSIONAL>False</WIN_VER_WINDOWS2016_PROFESSIONAL>
      <WIN_VER_WINDOWS2016_PROFESSIONAL_WMC>False</WIN_VER_WINDOWS2016_PROFESSIONAL_WMC>
      <WIN_VER_WINDOWS2016_SB_SOLUTION_SERVER_EM>False</WIN_VER_WINDOWS2016_SB_SOLUTION_SERVER_EM>
      <WIN_VER_WINDOWS2016_SERVER_FOR_SB_SOLUTIONS>False</WIN_VER_WINDOWS2016_SERVER_FOR_SB_SOLUTIONS>
      <WIN_VER_WINDOWS2016_SERVER_FOR_SB_SOLUTIONS_EM>False</WIN_VER_WINDOWS2016_SERVER_FOR_SB_SOLUTIONS_EM>
      <WIN_VER_WINDOWS2016_SERVER_FOR_SMALLBUSINESS>False</WIN_VER_WINDOWS2016_SERVER_FOR_SMALLBUSINESS>
      <WIN_VER_WINDOWS2016_SERVER_FOR_SMALLBUSINESS_V>False</WIN_VER_WINDOWS2016_SERVER_FOR_SMALLBUSINESS_V>
      <WIN_VER_WINDOWS2016_SERVER_FOUNDATION>False</WIN_VER_WINDOWS2016_SERVER_FOUNDATION>
      <WIN_VER_WINDOWS2016_SB_SOLUTION_SERVER>False</WIN_VER_WINDOWS2016_SB_SOLUTION_SERVER>
      <WIN_VER_WINDOWS2016_SMALLBUSINESS_SERVER>False</WIN_VER_WINDOWS2016_SMALLBUSINESS_SERVER>
      <WIN_VER_WINDOWS2016_SMALLBUSINESS_SERVER_PREMIUM>False</WIN_VER_WINDOWS2016_SMALLBUSINESS_SERVER_PREMIUM>
      <WIN_VER_WINDOWS2016_SMALLBUSINESS_SERVER_PREMIUM_CORE>False</WIN_VER_WINDOWS2016_SMALLBUSINESS_SERVER_PREMIUM_CORE>
      <WIN_VER_WINDOWS2016_SOLUTION_EMBEDDEDSERVER>False</WIN_VER_WINDOWS2016_SOLUTION_EMBEDDEDSERVER>
      <WIN_VER_WINDOWS2016_STANDARD_EVALUATION_SERVER>False</WIN_VER_WINDOWS2016_STANDARD_EVALUATION_SERVER>
      <WIN_VER_WINDOWS2016_STANDARD_SERVER>False</WIN_VER_WINDOWS2016_STANDARD_SERVER>
      <WIN_VER_WINDOWS2016_STANDARD_SERVER_CORE>False</WIN_VER_WINDOWS2016_STANDARD_SERVER_CORE>
      <WIN_VER_WINDOWS2016_STANDARD_SERVER_V>False</WIN_VER_WINDOWS2016_STANDARD_SERVER_V>
      <WIN_VER_WINDOWS2016_STANDARD_SERVER_CORE_V>False</WIN_VER_WINDOWS2016_STANDARD_SERVER_CORE_V>
      <WIN_VER_WINDOWS2016_STANDARD_SERVER_SOLUTIONS>False</WIN_VER_WINDOWS2016_STANDARD_SERVER_SOLUTIONS>
      <WIN_VER_WINDOWS2016_STANDARD_SERVER_SOLUTIONS_CORE>False</WIN_VER_WINDOWS2016_STANDARD_SERVER_SOLUTIONS_CORE>
      <WIN_VER_WINDOWS2016_STARTER>False</WIN_VER_WINDOWS2016_STARTER>
      <WIN_VER_WINDOWS2016_STORAGE_ENTERPRISE_SERVER>False</WIN_VER_WINDOWS2016_STORAGE_ENTERPRISE_SERVER>
      <WIN_VER_WINDOWS2016_STORAGE_ENTERPRISE_SERVER_CORE>False</WIN_VER_WINDOWS2016_STORAGE_ENTERPRISE_SERVER_CORE>
      <WIN_VER_WINDOWS2016_STORAGE_EXPRESS_SERVER>False</WIN_VER_WINDOWS2016_STORAGE_EXPRESS_SERVER>
      <WIN_VER_WINDOWS2016_STORAGE_EXPRESS_SERVER_CORE>False</WIN_VER_WINDOWS2016_STORAGE_EXPRESS_SERVER_CORE>
      <WIN_VER_WINDOWS2016_STORAGE_STANDARD_EVALUATION_SERVER>False</WIN_VER_WINDOWS2016_STORAGE_STANDARD_EVALUATION_SERVER>
      <WIN_VER_WINDOWS2016_STORAGE_STANDARD_SERVER>False</WIN_VER_WINDOWS2016_STORAGE_STANDARD_SERVER>
      <WIN_VER_WINDOWS2016_STORAGE_STANDARD_SERVER_CORE>False</WIN_VER_WINDOWS2016_STORAGE_STANDARD_SERVER_CORE>
      <WIN_VER_WINDOWS2016_STORAGE_WORKGROUP_EVALUATION_SERVER>False</WIN_VER_WINDOWS2016_STORAGE_WORKGROUP_EVALUATION_SERVER>
      <WIN_VER_WINDOWS2016_STORAGE_WORKGROUP_SERVER>False</WIN_VER_WINDOWS2016_STORAGE_WORKGROUP_SERVER>
      <WIN_VER_WINDOWS2016_STORAGE_WORKGROUP_SERVER_CORE>False</WIN_VER_WINDOWS2016_STORAGE_WORKGROUP_SERVER_CORE>
      <WIN_VER_WINDOWS2016_UNDEFINED>False</WIN_VER_WINDOWS2016_UNDEFINED>
      <WIN_VER_WINDOWS2016_ULTIMATE>False</WIN_VER_WINDOWS2016_ULTIMATE>
      <WIN_VER_WINDOWS2016_WEB_SERVER>False</WIN_VER_WINDOWS2016_WEB_SERVER>
      <WIN_VER_WINDOWS2016_WEB_SERVER_CORE>False</WIN_VER_WINDOWS2016_WEB_SERVER_CORE>
      <WIN_VER_WINDOWS2016_MOBILE_CORE>False</WIN_VER_WINDOWS2016_MOBILE_CORE>
      <WIN_VER_WINDOWS2016_EMBEDDED_INDUSTRY_EVAL>False</WIN_VER_WINDOWS2016_EMBEDDED_INDUSTRY_EVAL>
      <WIN_VER_WINDOWS2016_EMBEDDED_INDUSTRY_E_EVAL>False</WIN_VER_WINDOWS2016_EMBEDDED_INDUSTRY_E_EVAL>
      <WIN_VER_WINDOWS2016_EMBEDDED_EVAL>False</WIN_VER_WINDOWS2016_EMBEDDED_EVAL>
      <WIN_VER_WINDOWS2016_EMBEDDED_E_EVAL>False</WIN_VER_WINDOWS2016_EMBEDDED_E_EVAL>
      <WIN_VER_WINDOWS2016_NANO_SERVER>False</WIN_VER_WINDOWS2016_NANO_SERVER>
      <WIN_VER_WINDOWS2016_CLOUD_STORAGE_SERVER>False</WIN_VER_WINDOWS2016_CLOUD_STORAGE_SERVER>
      <WIN_VER_WINDOWS2016_CORE_CONNECTED>False</WIN_VER_WINDOWS2016_CORE_CONNECTED>
      <WIN_VER_WINDOWS2016_PROFESSIONAL_STUDENT>False</WIN_VER_WINDOWS2016_PROFESSIONAL_STUDENT>
      <WIN_VER_WINDOWS2016_CORE_CONNECTED_SINGLELANGUAGE>False</WIN_VER_WINDOWS2016_CORE_CONNECTED_SINGLELANGUAGE>
      <WIN_VER_WINDOWS2016_CORE_CONNECTED_COUNTRYSPECIFIC>False</WIN_VER_WINDOWS2016_CORE_CONNECTED_COUNTRYSPECIFIC>
      <WIN_VER_WINDOWS2016_CONNECTED_CAR>False</WIN_VER_WINDOWS2016_CONNECTED_CAR>
      <WIN_VER_WINDOWS2016_INDUSTRY_HANDHELD>False</WIN_VER_WINDOWS2016_INDUSTRY_HANDHELD>
      <WIN_VER_WINDOWS2016_PPI_PRO>False</WIN_VER_WINDOWS2016_PPI_PRO>
      <WIN_VER_WINDOWS2016_ARM64_SERVER>False</WIN_VER_WINDOWS2016_ARM64_SERVER>
      <WIN_VER_WINDOWS2016_EDUCATION>False</WIN_VER_WINDOWS2016_EDUCATION>
      <WIN_VER_WINDOWS2016_IOTUAP>False</WIN_VER_WINDOWS2016_IOTUAP>
      <WIN_VER_WINDOWS2016_CLOUD_HOST_INFRASTRUCTURE_SERVER>False</WIN_VER_WINDOWS2016_CLOUD_HOST_INFRASTRUCTURE_SERVER>
      <WIN_VER_WINDOWS2016_ENTERPRISE_S>False</WIN_VER_WINDOWS2016_ENTERPRISE_S>
      <WIN_VER_WINDOWS2016_PROFESSIONAL_S>False</WIN_VER_WINDOWS2016_PROFESSIONAL_S>
      <WIN_VER_WINDOWS2016_ENTERPRISE_S_EVALUATION>False</WIN_VER_WINDOWS2016_ENTERPRISE_S_EVALUATION>
      <WIN_VER_WINDOWS2016_IOTUAPCOMMERCIAL>False</WIN_VER_WINDOWS2016_IOTUAPCOMMERCIAL>
      <WIN_VER_WINDOWS2016_MOBILE_ENTERPRISE>False</WIN_VER_WINDOWS2016_MOBILE_ENTERPRISE>
      <WIN_VER_WINDOWS2016_HOLOGRAPHIC>False</WIN_VER_WINDOWS2016_HOLOGRAPHIC>
      <WIN_VER_WINDOWS2016_PRO_SINGLE_LANGUAGE>False</WIN_VER_WINDOWS2016_PRO_SINGLE_LANGUAGE>
      <WIN_VER_WINDOWS2016_PRO_CHINA>False</WIN_VER_WINDOWS2016_PRO_CHINA>
      <WIN_VER_WINDOWS2016_ENTERPRISE_SUBSCRIPTION>False</WIN_VER_WINDOWS2016_ENTERPRISE_SUBSCRIPTION>
      <WIN_VER_WINDOWS2016_DATACENTER_NANO_SERVER>False</WIN_VER_WINDOWS2016_DATACENTER_NANO_SERVER>
      <WIN_VER_WINDOWS2016_STANDARD_NANO_SERVER>False</WIN_VER_WINDOWS2016_STANDARD_NANO_SERVER>
      <WIN_VER_WINDOWS2016_DATACENTER_A_SERVER_CORE>False</WIN_VER_WINDOWS2016_DATACENTER_A_SERVER_CORE>
      <WIN_VER_WINDOWS2016_STANDARD_A_SERVER_CORE>False</WIN_VER_WINDOWS2016_STANDARD_A_SERVER_CORE>
      <WIN_VER_WINDOWS2016_DATACENTER_WS_SERVER_CORE>False</WIN_VER_WINDOWS2016_DATACENTER_WS_SERVER_CORE>
      <WIN_VER_WINDOWS2016_STANDARD_WS_SERVER_CORE>False</WIN_VER_WINDOWS2016_STANDARD_WS_SERVER_CORE>
      <WIN_VER_WINDOWS2016_UTILITY_VM>False</WIN_VER_WINDOWS2016_UTILITY_VM>
      <WIN_VER_WINDOWS2016_DATACENTER_EVALUATION_SERVER_CORE>False</WIN_VER_WINDOWS2016_DATACENTER_EVALUATION_SERVER_CORE>
      <WIN_VER_WINDOWS2016_STANDARD_EVALUATION_SERVER_CORE>False</WIN_VER_WINDOWS2016_STANDARD_EVALUATION_SERVER_CORE>
      <WIN_VER_WINDOWS2016_PRO_WORKSTATION>False</WIN_VER_WINDOWS2016_PRO_WORKSTATION>
      <WIN_VER_WINDOWS2016_PRO_FOR_EDUCATION>False</WIN_VER_WINDOWS2016_PRO_FOR_EDUCATION>
      <WIN_VER_WINDOWS2016_AZURE_SERVER_CORE>False</WIN_VER_WINDOWS2016_AZURE_SERVER_CORE>
      <WIN_VER_WINDOWS2016_AZURE_NANO_SERVER>False</WIN_VER_WINDOWS2016_AZURE_NANO_SERVER>
    </WindowsVersion>
    <VirtualizationTools>
      <Enabled>False</Enabled>
      <VMWare>False</VMWare>
      <VirtualPC>False</VirtualPC>
      <Sandboxie>False</Sandboxie>
      <TerminateExecution>True</TerminateExecution>
      <ShowMessageBeforeTermination>True</ShowMessageBeforeTermination>
      <Message>
        <Title>Warning</Title>
        <Text>This program can not be run under %CU_VIRTTOOLS%!</Text>
        <UseHL>False</UseHL>
        <HLSentence>Enigma Protector</HLSentence>
        <HLURL>http://www.enigmaprotector.com/</HLURL>
        <Icon>0</Icon>
        <AutoClose>False</AutoClose>
        <CloseDelay>0</CloseDelay>
        <Disable>True</Disable>
        <DisableDelay>10</DisableDelay>
        <FontName/>
        <FontCharset>0</FontCharset>
        <FontColor>0</FontColor>
        <FontSize>0</FontSize>
        <FontBold>False</FontBold>
        <FontItalic>False</FontItalic>
        <FontUnderline>False</FontUnderline>
        <FontStrikeout>False</FontStrikeout>
      </Message>
      <VirtualBox>False</VirtualBox>
      <HyperVGuest>True</HyperVGuest>
      <HyperV>False</HyperV>
    </VirtualizationTools>
    <Privileges>
      <Administrator>
        <Enabled>False</Enabled>
        <ShowMessageBeforeTermination>False</ShowMessageBeforeTermination>
        <Message>
          <Title>Warning</Title>
          <Text>You have not enough permissions to run this application. 
Please switch on administrator privileges of your Windows account and 
try again!</Text>
          <UseHL>False</UseHL>
          <HLSentence>Enigma Protector</HLSentence>
          <HLURL>http://enigmaprotector.com/</HLURL>
          <Icon>3</Icon>
          <AutoClose>False</AutoClose>
          <CloseDelay>0</CloseDelay>
          <Disable>False</Disable>
          <DisableDelay>0</DisableDelay>
          <FontName/>
          <FontCharset>0</FontCharset>
          <FontColor>0</FontColor>
          <FontSize>0</FontSize>
          <FontBold>False</FontBold>
          <FontItalic>False</FontItalic>
          <FontUnderline>False</FontUnderline>
          <FontStrikeout>False</FontStrikeout>
        </Message>
      </Administrator>
    </Privileges>
  </CheckUp>
  <Protection>
    <FileAnalyzerDeception>
      <NameOfTheProtectionCodeSection>.data</NameOfTheProtectionCodeSection>
    </FileAnalyzerDeception>
    <OriginalFileSizePreservation>
      <Enabled>False</Enabled>
    </OriginalFileSizePreservation>
    <AdvanceForceImportProtection>
      <Enabled>False</Enabled>
    </AdvanceForceImportProtection>
    <WinAPIFunctionsRedirection>
      <Enabled>False</Enabled>
    </WinAPIFunctionsRedirection>
    <WinAPIFunctionsEmulation>
      <Enabled>True</Enabled>
    </WinAPIFunctionsEmulation>
    <InlinePatching>
      <Enabled>False</Enabled>
      <Threads>3</Threads>
      <Interval>1000</Interval>
    </InlinePatching>
    <ProtectedStrings>
      <Strings Count="4">
        <String>
          <ID>1</ID>
          <Key>40ml7zRs</Key>
          <Type>0</Type>
          <String>Protected ansi string by ID</String>
          <Comment/>
        </String>
        <String>
          <ID>2</ID>
          <Key>pGmGqVZS</Key>
          <Type>0</Type>
          <String>This is a protected ansi string by KEY</String>
          <Comment/>
        </String>
        <String>
          <ID>3</ID>
          <Key>YMNytxdB</Key>
          <Type>1</Type>
          <String>Wide protected string by ID</String>
          <Comment/>
        </String>
        <String>
          <ID>4</ID>
          <Key>pjhGG21J</Key>
          <Type>1</Type>
          <String>This is a protected wide string by KEY</String>
          <Comment/>
        </String>
      </Strings>
    </ProtectedStrings>
    <ResourcesProtection>
      <SkipDialog>False</SkipDialog>
      <SkipIconAndGroupIcon>True</SkipIconAndGroupIcon>
      <SkipVersion>True</SkipVersion>
      <SkipString>True</SkipString>
    </ResourcesProtection>
  </Protection>
  <VirtualBox>
    <Secure1>DDC1974B1796ADEBC202942C623A2C3E93713119165E68843C5DB9618175A49EF911E84758C93E2DD671490958F8F53362F06FAF664C0B616C9B4E512C16B4B181BF12CC472E7881F74F4A07AD9CFFE0719EF014A827BBAF03E5659E0F8C44A73E359CE2FB0BF093EC6152B2876B373E2899A78DA3A0959EB63786EBAF94600DB057403F9C83ABE8EE7CF2B5A1979176B7A88FD24748A7693C10840854AB598310CCABC08D3236E4ECBFAC71878A713D4B145BA35204FEEF1429AF4AC9E6A1E5C33BEFE9F173924E5792B1D81385C9065BC993A8353A57AA8D54EF210C18258F037A62905221BA958337CBEB9DC92C31FE4D2F8124F26493F07B9716AA9E3194</Secure1>
    <Secure2>664B354023D93A4C65D219188F97D5B0EF75B2DF7A9253C06BBCB9F345370ABBD41472FB237BECDDAB83E725175DDE7D2D51DCB8A91541211BEBBCEAAE25A145DC9F0CA88BC25EBE049DF97B3A4D6B3CCA455BBCFADD27E8E8A84725956854BB6A3C1DF7ED032DAAD6A84E3743D0D45A4F7EB0126CD89FED4FC576A36255F7C493FE75833A95EB59FFC2C306E2D229176404E15524556FC5D9972C5E5E6D41E7AAE05304016EE0A934C4681811B9854DB3EA20137923069135EFA4790EF5996831C5DF131022D0922A29C6264AD282724BE719217957D543E2AA296A7BC6F3FCDE21E3ADCD2B9A30243180BF9D7BCCB8082E7632F940604041ECF76D2F210161</Secure2>
    <PrivateKey>01FZR9CU3MK4A5FGNFPJ7JQ8LHRKJ5JSAA0ADP5KT5JZTLXZKR988GMSSTXUEWX5ZM23VD43KHWS3TJJ2AJDTFJNCAKVARZ7BBXVKP543AQ9YQWYA4S4BDJ38RUD5GSP69UENMCET8Y7XE6NSEA8JJHT3LFTQYBRJZNHURPR3LWRKRWZCF3CLRAWWKWUMRAZ925S6ZCNG78DFF5GYY0202SNMNAL3V3JH4T9SWEA4S45W95RGQ4YX0202SNMNAL3V3JH4T9SWEA4S45W95RGQ4YX</PrivateKey>
    <PublicKey>02004BD5DDF31CEF7C945D0E18455B21AD50CCS67AS34JREHTNA56PN3NLTL2Y8RTHT32AVBXFEAUVQNZUCLYMTLQXK725QYPQPEYDFBBX6NU8MXLPWY74AHBQR28K5GLYDN4EBASJ4QA9WHB7HDKVGFP9Q6MPRHA7PHRMZ3N3Z5CNVUXMZ3CVSXENFHC7DEPC6USTHUU868HWXDVC2PFALXV45MG34KVG63SJKLDKJQMEK6M0CDGX82TE5U6XB35Q25FX3FPJMRBEYQ6KQU4N7LATM8D9YS6CZ8VF37URPMWWZYQNMG3XREZ7LD3YU782RMY3QW63FNU9CPYGNWCTJEUV7MRT8MWTUYEJG3CMN4G4BPSWLHMRT4AGW5Y675G6WX4XLNLAJUD9XMQ5N9TERHNU286Z6L8MJL8ZKNAUT59H22JXBAJVBGUCLV7JWAP0CDLVUFLTTWZVYFPESSMXECEAUWLPJ9WBMXZBTQF7H3NFP3299YBK98BABEW3MV7EB8Y8NV4SHMLPL9UKVSW9YHWLNNQCULA562AECH5Z2QERWNL4AY6WEFDRUR6WQXE33DQERJYHQPS9C4JP5YVNK8C2R59YGXHJEMNBPZCJ2GDDNS5AQHN6XFCF5WKTP5BQR5P836PN2BBS6AR</PublicKey>
    <Files>
      <Enabled>False</Enabled>
      <DeleteExtractedOnExit>False</DeleteExtractedOnExit>
      <CompressFiles>False</CompressFiles>
      <Files/>
    </Files>
    <Registries>
      <Enabled>False</Enabled>
      <Registries>
        <Registry>
          <Type>1</Type>
          <Virtual>True</Virtual>
          <Name>Classes</Name>
          <ValueType>0</ValueType>
          <Value/>
          <Registries/>
        </Registry>
        <Registry>
          <Type>1</Type>
          <Virtual>True</Virtual>
          <Name>User</Name>
          <ValueType>0</ValueType>
          <Value/>
          <Registries/>
        </Registry>
        <Registry>
          <Type>1</Type>
          <Virtual>True</Virtual>
          <Name>Machine</Name>
          <ValueType>0</ValueType>
          <Value/>
          <Registries/>
        </Registry>
        <Registry>
          <Type>1</Type>
          <Virtual>True</Virtual>
          <Name>Users</Name>
          <ValueType>0</ValueType>
          <Value/>
          <Registries/>
        </Registry>
        <Registry>
          <Type>1</Type>
          <Virtual>True</Virtual>
          <Name>Config</Name>
          <ValueType>0</ValueType>
          <Value/>
          <Registries/>
        </Registry>
      </Registries>
    </Registries>
    <Packaging>
      <Enabled>False</Enabled>
      <CheckIntegrity>True</CheckIntegrity>
    </Packaging>
    <Options>
      <MapExecutableWithTemporaryFile>True</MapExecutableWithTemporaryFile>
      <ShareVirtualSystemToChildProcesses>False</ShareVirtualSystemToChildProcesses>
      <AllowRunningOfVirtualExeFiles>True</AllowRunningOfVirtualExeFiles>
      <HideFromDialogs>False</HideFromDialogs>
    </Options>
  </VirtualBox>
  <VirtualMachine>
    <Enabled>False</Enabled>
    <Common>
      <VMType>1</VMType>
      <RISCVMTrashgen>5</RISCVMTrashgen>
      <RISCVMObfuscation>2</RISCVMObfuscation>
      <RISCVMDuplicates>2</RISCVMDuplicates>
      <RISCVMEncryption>2</RISCVMEncryption>
    </Common>
    <Filter/>
    <CommandLine/>
    <Functions Count="0"/>
    <FileEntryPoint>
      <Enabled>False</Enabled>
    </FileEntryPoint>
  </VirtualMachine>
  <Miscellaneous>
    <SplashScreenEncapsulation>
      <Enabled>False</Enabled>
      <SplashScreenPictureFileName/>
      <ShowOnTop>True</ShowOnTop>
      <TimeToShow>2000</TimeToShow>
      <StopExecution>False</StopExecution>
      <CloseOnMouseClick>False</CloseOnMouseClick>
      <ShowAtStart>True</ShowAtStart>
    </SplashScreenEncapsulation>
    <Watermarks>
      <Enabled>False</Enabled>
      <Watermarks Count="0"/>
    </Watermarks>
    <Plugins>
      <Plugins Count="0"/>
    </Plugins>
    <CustomVersionResource>
      <Enabled>False</Enabled>
      <MajorVersion>1</MajorVersion>
      <MinorVersion>0</MinorVersion>
      <Release>0</Release>
      <Build>0</Build>
      <DebugBuild>False</DebugBuild>
      <Pre-release>False</Pre-release>
      <SpecialBuild>False</SpecialBuild>
      <PrivateBuild>False</PrivateBuild>
      <DLL>False</DLL>
      <Language>1033</Language>
      <CompanyName>The Enigma Protector Developers Team</CompanyName>
      <FileDescription>Software Protection Tool</FileDescription>
      <FileVersion>*******</FileVersion>
      <InternalName>ENIGMA.EXE</InternalName>
      <LegalCopyright>Copyrights (C) 2002-2009 Vladimir Sukhov</LegalCopyright>
      <LegalTrademarks>Trademarks (R) 2002-2009 Vladimir Sukhov</LegalTrademarks>
      <OriginalFilename>enigma.exe</OriginalFilename>
      <ProductName>The Enigma Protector</ProductName>
      <ProductVersion>*******</ProductVersion>
      <Comments>http://enigmaprotector.com/</Comments>
    </CustomVersionResource>
    <CustomManifestResource>
      <Enabled>False</Enabled>
      <Template>2</Template>
      <TemplateFile/>
      <Manifest>&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot; standalone=&quot;yes&quot;?&gt;
&lt;assembly
    xmlns=&quot;urn:schemas-microsoft-com:asm.v1&quot;
    manifestVersion=&quot;1.0&quot;&gt;
    &lt;assemblyIdentity
        version=&quot;*******&quot;
        processorArchitecture=&quot;X86&quot;
        name=&quot;Enigma.exe&quot;
        type=&quot;win32&quot; /&gt;
    &lt;description&gt;The Enigma Protector&lt;/description&gt;
    &lt;dependency&gt;
        &lt;dependentAssembly&gt;
            &lt;assemblyIdentity
                type=&quot;win32&quot;
                name=&quot;Microsoft.Windows.Common-Controls&quot;
                version=&quot;*******&quot;
                processorArchitecture=&quot;X86&quot;
                publicKeyToken=&quot;6595b64144ccf1df&quot;
                language=&quot;*&quot; /&gt;
        &lt;/dependentAssembly&gt;
    &lt;/dependency&gt;
&lt;/assembly&gt;</Manifest>
    </CustomManifestResource>
    <CommandLine>
      <Enabled>False</Enabled>
      <Override>True</Override>
      <CommandLine/>
    </CommandLine>
    <EnvironmentVariables>
      <Variables Count="0"/>
    </EnvironmentVariables>
    <Other>
      <SupressGraphics>False</SupressGraphics>
      <DoNotCheckIfCompressed>False</DoNotCheckIfCompressed>
      <DoNotSearchMarkers>False</DoNotSearchMarkers>
      <NETSmartAssemblyHooking>False</NETSmartAssemblyHooking>
      <RemoveOverlayFromProtectedFile>False</RemoveOverlayFromProtectedFile>
      <KeepOriginalImportDirectory>False</KeepOriginalImportDirectory>
      <DoNotCompressEncryptCode>False</DoNotCompressEncryptCode>
      <DeleteDebugDirectory>True</DeleteDebugDirectory>
      <HookEntireProcessForEnigmaApiCalls>True</HookEntireProcessForEnigmaApiCalls>
      <CustomMemoryManager>False</CustomMemoryManager>
      <TaggantTimestamp>True</TaggantTimestamp>
      <TaggantTimestampUrl>http://taggant-tsa.ieee.org/</TaggantTimestampUrl>
    </Other>
  </Miscellaneous>
  <TrialControl>
    <Common>
      <ResetTrialInNewVersion>True</ResetTrialInNewVersion>
      <OpenFileIfTrialExpired>False</OpenFileIfTrialExpired>
      <OpenFileName>application.exe</OpenFileName>
    </Common>
    <TrialStoring>
      <Enabled>False</Enabled>
      <Items Count="0"/>
    </TrialStoring>
    <LockTrialToUserLanguage>
      <Enabled>False</Enabled>
      <ALB>True</ALB>
      <DZA>True</DZA>
      <ARG>True</ARG>
      <ARM>True</ARM>
      <AUS>True</AUS>
      <AUT>True</AUT>
      <AZE>True</AZE>
      <BHR>True</BHR>
      <BLR>True</BLR>
      <BEL>True</BEL>
      <BLZ>True</BLZ>
      <BOL>True</BOL>
      <BRA>True</BRA>
      <BRN>True</BRN>
      <BGR>True</BGR>
      <KHM>True</KHM>
      <CAN>True</CAN>
      <CAF>True</CAF>
      <TCD>True</TCD>
      <CHL>True</CHL>
      <CHN>True</CHN>
      <COL>True</COL>
      <CRI>True</CRI>
      <HRV>True</HRV>
      <CZE>True</CZE>
      <DNK>True</DNK>
      <DOM>True</DOM>
      <ECU>True</ECU>
      <EGY>True</EGY>
      <SLV>True</SLV>
      <EST>True</EST>
      <FRO>True</FRO>
      <FIN>True</FIN>
      <FRA>True</FRA>
      <GEO>True</GEO>
      <DEU>True</DEU>
      <GRC>True</GRC>
      <GTM>True</GTM>
      <HND>True</HND>
      <HKG>True</HKG>
      <HUN>True</HUN>
      <ISL>True</ISL>
      <IND>True</IND>
      <IDN>True</IDN>
      <IRN>True</IRN>
      <IRQ>True</IRQ>
      <IRL>True</IRL>
      <ISR>True</ISR>
      <ITA>True</ITA>
      <JAM>True</JAM>
      <JPN>True</JPN>
      <JOR>True</JOR>
      <KAZ>True</KAZ>
      <KEN>True</KEN>
      <PRK>True</PRK>
      <KOR>True</KOR>
      <KWT>True</KWT>
      <KGZ>True</KGZ>
      <LVA>True</LVA>
      <LBN>True</LBN>
      <LBR>True</LBR>
      <LIE>True</LIE>
      <LTU>True</LTU>
      <LUX>True</LUX>
      <MAC>True</MAC>
      <MKD>True</MKD>
      <MYS>True</MYS>
      <MEX>True</MEX>
      <MDA>True</MDA>
      <MCO>True</MCO>
      <MNG>True</MNG>
      <MAR>True</MAR>
      <NLD>True</NLD>
      <NZL>True</NZL>
      <NIC>True</NIC>
      <NOR>True</NOR>
      <OMN>True</OMN>
      <PAK>True</PAK>
      <PAN>True</PAN>
      <PRY>True</PRY>
      <PER>True</PER>
      <PHL>True</PHL>
      <POL>True</POL>
      <PRT>True</PRT>
      <PRI>True</PRI>
      <QAT>True</QAT>
      <ROU>True</ROU>
      <RUS>True</RUS>
      <SAU>True</SAU>
      <SCG>True</SCG>
      <SGP>True</SGP>
      <SVK>True</SVK>
      <SVN>True</SVN>
      <ZAF>True</ZAF>
      <ESP>True</ESP>
      <SWE>True</SWE>
      <CHE>True</CHE>
      <TJK>True</TJK>
      <THA>True</THA>
      <TTO>True</TTO>
      <TUN>True</TUN>
      <TUR>True</TUR>
      <TWN>True</TWN>
      <UKR>True</UKR>
      <ARE>True</ARE>
      <GBR>True</GBR>
      <USA>True</USA>
      <URY>True</URY>
      <UZB>True</UZB>
      <VEN>True</VEN>
      <VNM>True</VNM>
      <YEM>True</YEM>
      <ZWE>True</ZWE>
      <AFG>True</AFG>
      <BGD>True</BGD>
      <BIH>True</BIH>
      <CAR>True</CAR>
      <ETH>True</ETH>
      <GRL>True</GRL>
      <LAO>True</LAO>
      <LBY>True</LBY>
      <MDV>True</MDV>
      <MLT>True</MLT>
      <MNE>True</MNE>
      <NEP>True</NEP>
      <NGA>True</NGA>
      <ROM>True</ROM>
      <RWA>True</RWA>
      <SEN>True</SEN>
      <SRB>True</SRB>
      <LKA>True</LKA>
      <SYR>True</SYR>
      <TAJ>True</TAJ>
      <TKM>True</TKM>
    </LockTrialToUserLanguage>
    <CountOfExecutionLimit>
      <Enabled>False</Enabled>
      <MaximalCountOfExecutions>0</MaximalCountOfExecutions>
      <TerminateAfterDelay>True</TerminateAfterDelay>
      <Delay>0</Delay>
      <ShowMessageBeforeTermination>True</ShowMessageBeforeTermination>
      <Message>
        <Title>Trial has expired!</Title>
        <Text>Sorry, but your trial period has expired!
You have exceeded %TrialExecsTotal% trial executions!
Please, register this software to remove trial limitations!</Text>
        <UseHL>False</UseHL>
        <HLSentence>Click here for registration</HLSentence>
        <HLURL>http://www.yourcompany.com/</HLURL>
        <Icon>0</Icon>
        <AutoClose>False</AutoClose>
        <CloseDelay>0</CloseDelay>
        <Disable>False</Disable>
        <DisableDelay>0</DisableDelay>
        <FontName/>
        <FontCharset>0</FontCharset>
        <FontColor>0</FontColor>
        <FontSize>0</FontSize>
        <FontBold>False</FontBold>
        <FontItalic>False</FontItalic>
        <FontUnderline>False</FontUnderline>
        <FontStrikeout>False</FontStrikeout>
      </Message>
    </CountOfExecutionLimit>
    <CountOfDaysLimit>
      <Enabled>False</Enabled>
      <MaximalCountOfDays>0</MaximalCountOfDays>
      <TerminateAfterDelay>True</TerminateAfterDelay>
      <Delay>0</Delay>
      <ShowMessageBeforeTermination>True</ShowMessageBeforeTermination>
      <Message>
        <Title>Trial has expired!</Title>
        <Text>Sorry, but your trial period has expired!
You have exceeded %TrialDaysTotal% trial days!
Please, register this software to remove trial limitations!</Text>
        <UseHL>False</UseHL>
        <HLSentence>Click here for registration</HLSentence>
        <HLURL>http://www.yourcompany.com/</HLURL>
        <Icon>0</Icon>
        <AutoClose>False</AutoClose>
        <CloseDelay>0</CloseDelay>
        <Disable>False</Disable>
        <DisableDelay>0</DisableDelay>
        <FontName/>
        <FontCharset>0</FontCharset>
        <FontColor>0</FontColor>
        <FontSize>0</FontSize>
        <FontBold>False</FontBold>
        <FontItalic>False</FontItalic>
        <FontUnderline>False</FontUnderline>
        <FontStrikeout>False</FontStrikeout>
      </Message>
    </CountOfDaysLimit>
    <ExpirationDateLimit>
      <Enabled>False</Enabled>
      <ExpirationDate>2007-12-19T23:59:30.629Z</ExpirationDate>
      <TerminateAfterDelay>True</TerminateAfterDelay>
      <Delay>0</Delay>
      <ShowMessageBeforeTermination>True</ShowMessageBeforeTermination>
      <Message>
        <Title>Trial has expired!</Title>
        <Text>Sorry, but your trial period has expired!
The expiration date is %TrialExpDay%/%TrialExpMonth%/%TrialExpYear% (dd/mm/yyyy)
Please, register this software to remove trial limitations!</Text>
        <UseHL>False</UseHL>
        <HLSentence>Click here for registration</HLSentence>
        <HLURL>http://www.yourcompany.com/</HLURL>
        <Icon>0</Icon>
        <AutoClose>False</AutoClose>
        <CloseDelay>0</CloseDelay>
        <Disable>False</Disable>
        <DisableDelay>0</DisableDelay>
        <FontName/>
        <FontCharset>0</FontCharset>
        <FontColor>0</FontColor>
        <FontSize>0</FontSize>
        <FontBold>False</FontBold>
        <FontItalic>False</FontItalic>
        <FontUnderline>False</FontUnderline>
        <FontStrikeout>False</FontStrikeout>
      </Message>
    </ExpirationDateLimit>
    <ExpirationFromDateTillDate>
      <Enabled>False</Enabled>
      <StartDate>2007-12-19T23:59:30.629Z</StartDate>
      <EndDate>2007-12-19T23:59:30.629Z</EndDate>
      <TerminateAfterDelay>True</TerminateAfterDelay>
      <Delay>0</Delay>
      <ShowMessageBeforeTermination>True</ShowMessageBeforeTermination>
      <Message>
        <Title>Trial has expired!</Title>
        <Text>Sorry, but your trial period has expired!
You can use it from %TrialStartDay%/%TrialStartMonth%/%TrialStartYear% till %TrialEndDay%/%TrialEndMonth%/%TrialEndYear% (dd/mm/yyyy)
Please, register this software to remove trial limitations!</Text>
        <UseHL>False</UseHL>
        <HLSentence>Click here for registration</HLSentence>
        <HLURL>http://www.yourcompany.com/</HLURL>
        <Icon>0</Icon>
        <AutoClose>False</AutoClose>
        <CloseDelay>0</CloseDelay>
        <Disable>False</Disable>
        <DisableDelay>0</DisableDelay>
        <FontName/>
        <FontCharset>0</FontCharset>
        <FontColor>0</FontColor>
        <FontSize>0</FontSize>
        <FontBold>False</FontBold>
        <FontItalic>False</FontItalic>
        <FontUnderline>False</FontUnderline>
        <FontStrikeout>False</FontStrikeout>
      </Message>
    </ExpirationFromDateTillDate>
    <ExecutionTimeLimit>
      <Enabled>False</Enabled>
      <NumberOfMinutes>0</NumberOfMinutes>
      <TerminateOnExpiration>True</TerminateOnExpiration>
      <ShowMessageBeforeTermination>True</ShowMessageBeforeTermination>
      <Message>
        <Title>Trial has expired!</Title>
        <Text>Sorry, but your trial execution time has expired! 
Application works only %TrialExecMinsTotal% minutes since start.
Please, register this software to remove trial limitations!</Text>
        <UseHL>False</UseHL>
        <HLSentence>Register it now!</HLSentence>
        <HLURL>http://www.yourcompany.com/</HLURL>
        <Icon>0</Icon>
        <AutoClose>False</AutoClose>
        <CloseDelay>0</CloseDelay>
        <Disable>False</Disable>
        <DisableDelay>0</DisableDelay>
        <FontName/>
        <FontCharset>0</FontCharset>
        <FontColor>0</FontColor>
        <FontSize>0</FontSize>
        <FontBold>False</FontBold>
        <FontItalic>False</FontItalic>
        <FontUnderline>False</FontUnderline>
        <FontStrikeout>False</FontStrikeout>
      </Message>
    </ExecutionTimeLimit>
    <Reminder>
      <Enabled>False</Enabled>
      <PeriodOfIterations>0</PeriodOfIterations>
      <Message>
        <Title>Shareware reminder!</Title>
        <Text>This program is shareware - you get to try it before you buy it. 
If you want to continue using it after the trial 
period you will have to buy it.
Please register to remove this nag-screen!
</Text>
        <UseHL>False</UseHL>
        <HLSentence>Click here for registration</HLSentence>
        <HLURL>http://www.yourcompany.com/</HLURL>
        <Icon>0</Icon>
        <AutoClose>False</AutoClose>
        <CloseDelay>0</CloseDelay>
        <Disable>False</Disable>
        <DisableDelay>0</DisableDelay>
        <FontName/>
        <FontCharset>0</FontCharset>
        <FontColor>0</FontColor>
        <FontSize>0</FontSize>
        <FontBold>False</FontBold>
        <FontItalic>False</FontItalic>
        <FontUnderline>False</FontUnderline>
        <FontStrikeout>False</FontStrikeout>
      </Message>
    </Reminder>
    <TimeControl>
      <TerminateOnClockReversing>False</TerminateOnClockReversing>
      <ShowMessageBeforeTermination>True</ShowMessageBeforeTermination>
      <Message>
        <Title>Clock reversing</Title>
        <Text>The software had determined that the system clock
had been turned back. Please set the correct date. 
This program will now close.</Text>
        <UseHL>False</UseHL>
        <HLSentence>Click here for registration</HLSentence>
        <HLURL>http://www.yourcompany.com/</HLURL>
        <Icon>0</Icon>
        <AutoClose>False</AutoClose>
        <CloseDelay>0</CloseDelay>
        <Disable>False</Disable>
        <DisableDelay>0</DisableDelay>
        <FontName/>
        <FontCharset>0</FontCharset>
        <FontColor>0</FontColor>
        <FontSize>0</FontSize>
        <FontBold>False</FontBold>
        <FontItalic>False</FontItalic>
        <FontUnderline>False</FontUnderline>
        <FontStrikeout>False</FontStrikeout>
      </Message>
      <DoNotCheckIfRegistered>False</DoNotCheckIfRegistered>
      <UseProtectionDate>False</UseProtectionDate>
      <Terminate>True</Terminate>
    </TimeControl>
  </TrialControl>
</EnigmaProject>
