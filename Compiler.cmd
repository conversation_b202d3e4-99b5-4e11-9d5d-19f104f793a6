@echo off
setlocal

:: Find the latest installed Visual Studio path using vswhere
for /f "usebackq tokens=*" %%i in (`"%ProgramFiles(x86)%\Microsoft Visual Studio\Installer\vswhere.exe" -latest -products * -requires Microsoft.VisualStudio.Component.VC.Tools.x86.x64 -property installationPath`) do (
    set "VSINSTALLDIR=%%i"
)

:: Make sure we found a valid installation
if not defined VSINSTALLDIR (
    echo [ERROR] Visual Studio not found.
    pause
    exit /b 1
)

:: Call VsDevCmd.bat
call "%VSINSTALLDIR%\Common7\Tools\VsDevCmd.bat"

msbuild "Engine/Engine.vcxproj" /t:Clean;Build /p:Configuration=Release
pause